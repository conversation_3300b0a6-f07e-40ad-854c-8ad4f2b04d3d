from openai import OpenAI                                                                                                                                                      
import time
import multiprocessing
import os
import pandas as pd

ModelName = "Qwen2___5-7B-Instruct"
ModelUrl = "https://u425633-9840-efc15267.nma1.seetacloud.com:8448/v1"
ApiKey = "123"
# 最大输出Token数
MaxResponseToken=2000
# 并发数
ProcessNum = 10
# 是否使用流式
# EnableStream = False
EnableStream = True
# 输入文件名
InputFile="pv_test.txt"

class Model(object):
    def __init__(self, model_path=None):
        self.client = OpenAI(
            base_url= ModelUrl, 
            api_key= ApiKey
        )   

    def inference(self, prompt, streamFlag=False):
        # [ref: https://platform.openai.com/docs/api-reference/chat/create]
        # includeUsage:True
        start_time = time.time()
        response = self.client.chat.completions.create(
            model=ModelName,
            temperature=0.0,
            max_tokens=MaxResponseToken,
            messages=[{"role": "user", "content": prompt}],
            stream=streamFlag
        )   

        result = {}
        responseTokenNum = 0 
        resultStr = ""
        queryTokenNum = 0 

        if streamFlag:
            # 处理流式响应
            first_token_received = False
            full_response = []
            for chunk in response:
                content = chunk.choices[0].delta.content
                responseTokenNum += 1
                full_response.append(content)
                if not first_token_received:
                                first_token_received = True
                                first_token_time = time.time() 
                                first_token_latency = first_token_time - start_time 
                                result["firstTokenLatency"] = first_token_latency
    
            queryLatency = time.time() - start_time
            resultStr = "".join(full_response)
            result["queryLatency"] = queryLatency
        else:
            # 处理非流式响应
            queryLatency = time.time() - start_time
            result["queryLatency"] = queryLatency
            if response is not None and response.choices is not None and len(response.choices) > 0:
                resultStr = response.choices[0].message.content
                responseTokenNum = response.usage.completion_tokens
                queryTokenNum = response.usage.prompt_tokens
                result["queryTokenNum"] = queryTokenNum

        result["responseLen"] =len(resultStr)
        result["responseTokenNum"]=responseTokenNum
        result["tokenRatio"] = responseTokenNum * 1.0 / len(resultStr)
        result["throughPut"] = responseTokenNum * 1.0 / result["queryLatency"]
        return result

def batchQuery(queryList):
    # 不可以传不可序列化对象
    m = Model()
    respList = []
    for input_case in queryList:
        try:
            # 非流式/流式
            respList.append(m.inference(input_case, EnableStream))
        except Exception as e:
            print("Error:", e)
    return respList

if __name__ == '__main__':
    queryList = []
    respList = []

    with open(InputFile, 'r') as f:
        queryList = f.readlines()
    
    detailList = { 
        "queryLatency": [0.9, 0.8, 0.7, 0.5, 'mean', 'max', 'min'],
        "firstTokenLatency": [0.9, 0.8, 0.7, 0.5, 'mean', 'max', 'min'],
        "responseTokenNum":[0.9, 0.8, 0.7, 0.5, 'mean', 'max', 'min'],
        "queryTokenNum": [0.9, 0.8, 0.7, 0.5,'mean','max', 'min'],
        "responseLen":['mean', 'max', 'min'],
        "tokenRatio":['mean', 'max', 'min'],
        "throughPut":['mean', 'max', 'min']
    }   

    print("文件行数", len(queryList))
    pool = multiprocessing.Pool(processes=ProcessNum)
    results = [pool.apply_async(batchQuery, args=(queryList,)) for i in range(ProcessNum)]
    pool.close()
    pool.join()
    respList = [res.get() for res in results]
    respList = [item for sublist in respList for item in sublist]
    
    df = pd.DataFrame(respList)
    print(df)

    result = pd.DataFrame()
    for key, measureList in detailList.items():
        if key not in df: 
            continue
        for item in measureList:
            if isinstance(item, float):
                result_str = key + "_" + str(100 * item) + "%" 
                result[result_str] = df[key].quantile(item)
                print(result_str, "\t\t", df[key].quantile(item))
            elif isinstance(item, str):
                result_str = key + "_" + item
                if item == "sum":
                    result[result_str] = [df[key].sum()]
                elif item == "mean":
                    result[result_str] = [df[key].mean()]
                elif item == "max":
                    result[result_str] = [df[key].max()]
                elif item == "min":
                    result[result_str] = [df[key].min()]
                print(result_str, "\t\t", result[result_str][0])
    
    result.to_csv("result.csv")

