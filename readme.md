## 1.下载镜像
### 镜像 OSS 地址：
oss://ovf/hospital_update/zhuque/hmgpt/mini_aigc_hospital_amd64.tar

## 2.放置项目
### 项目路径
/hm/package/python/aigc_hospital


## 3.配置 aigc_hospital 配置文件
### 配置文件路径
/hm/package/python/aigc_hospital/web/config.ini

### 修改内容
  ![img.png](readme_img/img.png)
  * 将 is_hospital 修改为 1
  * 将 llm_model_path 修改为 ```模型存放路径``` 如果是英特尔修改为 ```xft```
  * 将 llm_model_ip 修改为 推理服务 ip
  * llm_model_port 一般为 8001
  * chip_type 芯片类型，一般为gpu，华为的是npu，Intel的是cpu
  * input_max_len 默认为30000，根据现场硬件进行修改，仁济现场为10000



* 修改 hmcdss2 数据库的 IP
* 修改 hm_llm 数据库的 IP
* 修改 pangoo 的 IP
  
## 4.创建数据库
### 执行SQL文件
/hm/package/python/aigc_hospital/web/web_data/hm_llm.sql

## 5.创建启动脚本
创建位置 /hm/scripts/python/start_aigc_hospital.sh
```shell
#!/bin/bash
source ~/.bashrc
export ALIBABA_CLOUD_ACCESS_KEY_ID=LTAI5t8tJsDxZVap6
export ALIBABA_CLOUD_ACCESS_KEY_SECRET=N1Xm4PCvWqzcs5SM

cd /hm/package/python/aigc_hospital/web
python -u aigc_api_service.py  &>> /hm/logs/aigc.log
```

## 6.启动服务
```shell
docker run -itd --name=mini_aigc_hospital --ipc=host --restart=always --network=host -v /hm/package/python/:/hm/package/python/:rw -v /hm/scripts/:/hm/scripts:rw -v /hm/logs/:/hm/logs:rw mini_aigc_hospital:amd64 /hm/scripts/python/start_aigc_hospital.sh
```

## 7.补充信息
* 如果执行中涉及权限问题
  * 给 start_aigc_hospital.sh 加可执行权限。
  * 给 /hm/logs/aigc.log 加读写权限。