#!/bin/bash

# Set the API endpoint URL
# Replace with your actual server address and port
API_URL="http://**************:8800/hmgpt_qc_stream"

# Create a JSON payload for testing
cat > payload.json << EOF
{
    "customerId":1001,
    "recordId":74472618,
    "rule":{
        "ruleCode":"1001_hospital_llm_01",
        "recordTypeIds":[1, 2, 3],
        "logic":"检查患者的血压记录是否完整，并确认是否有异常值。",
        "outputRequirement":"请提供详细分析结果，当有缺陷存在时请返回“有缺陷”，当无缺陷存在时请返回“无缺陷。”"
    }
}
EOF

echo "Sending request to $API_URL..."
echo "Payload:"
cat payload.json

# Send the request and save the response
curl -X POST \
  -H "Content-Type: application/json" \
  -d @payload.json \
  --no-buffer \
  $API_URL

echo -e "\n\nTest completed." 