# coding:utf-8
import os
import sys
import socket
import logging

from dotenv import load_dotenv
import py_eureka_client.eureka_client as eureka_client

load_dotenv()

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.getcwd())
import datetime
from datetime import timedelta

from flask import Flask, Response, jsonify, render_template, request, session
from flask_apscheduler import APScheduler
from flask_cors import CORS

from web.config import *
from web.prometheus_monitor import g_monitor
from web.regression_test.regressions import regression_disease
from web.web_utils.text_util import valid_login

# 禁用APScheduler的所有日志
logging.getLogger('apscheduler').setLevel(logging.ERROR)

scheduler = APScheduler()
app = Flask(__name__)
from web.api.cdss_llm_api import cdss_llm_api, check_stream_code_timeout
from web.api.dify_llm_api import dify_llm_api
from web.api.dify_api import dify_api
from web.api.mrg_api import mrg_api
from web.api.qc_api import qc_api

app.register_blueprint(mrg_api)
app.register_blueprint(qc_api)
app.register_blueprint(cdss_llm_api)
app.register_blueprint(dify_llm_api)
app.register_blueprint(dify_api)
if config_map['app']['is_hospital'] == '0':
    from web.api.llm_api import llm_api

    app.register_blueprint(llm_api)


class Config(object):
    SCHEDULER_API_ENABLED = True


app.config.from_object(Config())
app.config['JSON_AS_ASCII'] = False
CORS(app, supports_credentials=True)
app.config['SECRET_KEY'] = os.urandom(24)  # 生成24位的随机数种子，用于产生SESSION ID


@app.before_request
def before():
    """
    针对app实例定义全局拦截器
    """
    url = request.path  # 读取到当前接口的地址
    # print(url)
    pass_list = ['/', '/login', '/logout']
    suffix = url.endswith('.png') or url.endswith('.jpg') or url.endswith('.css') or url.endswith('.js')
    prefix = url.startswith('to_') or url.startswith('find_')
    if url in pass_list or suffix:
        pass
    elif prefix and session.get('is_login', '') != 'true':
        return '你还没有登录，<a href="login">去登录</a>'
    else:
        pass


@app.route('/health', methods=['GET'])
def health():
    get_dbg = request.args.get('dbg', "-1")
    if get_dbg != "-1":
        config_map['dbg'] = get_dbg
    return {"status": 200, 'dbg': config_map['dbg']}


@app.route('/', methods=['POST', 'GET'])
@app.route('/login', methods=['POST', 'GET'])
def login():
    if request.method == 'POST':
        flag, msg, login_data, user_name, customer_id = valid_login(request.get_data(), config_map)
        if not flag:
            return jsonify({"flag": 0, "msg": msg})
        session['is_login'] = 'true'
        session['user_name'] = user_name
        session.permanent = True
        config_map['customer_id'] = customer_id
        app.permanent_session_lifetime = timedelta(minutes=10 * 60)
        return jsonify({"flag": 1, "msg": msg})
    else:
        return render_template('common/login.html')


@app.route('/logout', methods=['POST', 'GET'])
def logout():
    session['is_login'] = 'false'
    return render_template('common/login.html')


# @scheduler.task('interval', id='do_job_1', seconds=30, misfire_grace_time=900)
@scheduler.task('cron', id='do_job_scheduler_discharge', day=config_map['scheduler_discharge']['day'],
                hour=config_map['scheduler_discharge']['hour'], minute=config_map['scheduler_discharge']['minute'],
                second=config_map['scheduler_discharge']['second'])
def job_scheduler_discharge():
    print(str(datetime.datetime.now()) + ' Job scheduler_discharge executed')
    if config_map['scheduler_discharge']['is_run'] == '1':
        pass
    print(str(datetime.datetime.now()) + ' Job scheduler_discharge done')


@scheduler.task('cron', id='do_job_scheduler_regression', day=config_map['scheduler_regression']['day'],
                hour=config_map['scheduler_regression']['hour'], minute=config_map['scheduler_regression']['minute'],
                second=config_map['scheduler_regression']['second'])
def job_scheduler_regression():
    print(str(datetime.datetime.now()) + ' Job scheduler_regression executed')
    if config_map['scheduler_regression']['is_run'] == '1':
        error_str = regression_disease(config_map)
        print('regression_disease: ', error_str)
    print(str(datetime.datetime.now()) + ' Job scheduler_regression done')


@scheduler.task('interval', id='do_check_stream_code_timeout', seconds=2)
def job_check_stream_code_timeout():
    check_stream_code_timeout()


@app.route("/metrics")
def metric():
    return Response(g_monitor.get_prometheus_metrics_info(), mimetype="text/plain")

def eureka_init_app(app):
    app_name = config_map['eureka']["app_name"]
    eureka_addr = config_map['eureka']['eureka_addr']
    port = int(config_map['app']['app_port'])
    try:
        # ip = socket.gethostbyname(socket.gethostname())
        ip = config_map['app']['app_ip']
    except Exception as e:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        # ip = s.getsockname()[0]
        ip = config_map['app']['app_ip']

        s.close()
    eureka_client.init(eureka_server=eureka_addr,
                       app_name=app_name,
                       instance_host=ip,
                       instance_port=port)


if __name__ == '__main__':
    scheduler.init_app(app)
    scheduler.start()
    eureka_init_app(app)
    app_port = int(config_map['app']['app_port'])
    app.run(debug=False, host="0.0.0.0", port=app_port)
