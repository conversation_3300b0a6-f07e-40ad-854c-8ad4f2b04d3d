# coding:utf-8
from web.web_utils.sql_util import sql_query, select_by_sql
from web.web_utils.text_util import markdown_ft, normalize_name, normalize_text, xml2text
from datetime import datetime, timedelta
from bs4 import BeautifulSoup
import re
import asyncio
import aiohttp
import json
import logging
import threading


def get_cdss_progress_input(prompt_input, progress_list,cid, rid, start_date, end_date, progress_type_in, progress_type_not_in, config_map, cdss_type='mt'):
    input_max_len = int(config_map['llm_model']['input_max_len'])
    if cdss_type == 'ot':
        sql = '''select pp.id progress_id, pp.customer_id,pp.progress_type,ifnull(pp.progress_type_name,mm.record_name) progress_type_name,pp.progress_message progress_text,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format,ifnull(pp.msg_type,0) msg_type from {}_patient_progress pp 
            left join ms_system_record_type mm on pp.progress_type=mm.record_type
            where pp.record_id={}
                    '''.format(cdss_type, rid)
    else:
        sql = '''select pp.id progress_id,pp.progress_type,ifnull(pp.progress_type_name,mm.record_name) progress_type_name,pp.progress_message progress_text,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format,ifnull(ee.msg_type,0) msg_type from {}_patient_progress pp 
            left join mt_patient_progress_extend ee on pp.id=ee.progress_id
            left join ms_system_record_type mm on pp.progress_type=mm.record_type
            where pp.record_id={} and pp.progress_status!=9
                    '''.format(cdss_type, rid)

    if start_date:
        sql += ''' and pp.record_time_format>'{}' '''.format(start_date)
    if end_date:
        sql += ''' and pp.record_time_format<'{}' '''.format(end_date)
    if progress_type_in:
        sql += ''' and pp.progress_type in ({}) '''.format(','.join([str(i) for i in progress_type_in]))
    if progress_type_not_in:
        sql += ''' and pp.progress_type not in ({}) '''.format(','.join([str(i) for i in progress_type_not_in]))
    sql += ' order by pp.record_time_format'
    xs = sql_query(cid, sql, config_map)
    for v in xs:
        if not v['progress_text'] or not v['progress_type_name']:
            continue
        msg_type = v['msg_type']
        if v['msg_type'] == 1:
            progress_text = xml2text(v['progress_text'], v['progress_type'], msg_type, config_map, cid)
            msg_type = 0
        else:
            soup = BeautifulSoup(v['progress_text'], 'html.parser')
            progress_text = soup.get_text().replace('​', '')
        if len(prompt_input) + len(progress_text) < input_max_len:
            prompt_input += prompt_input_process(progress_text, len(progress_list))
            progress_list.append({'progress_id': v['progress_id'], 'progress_type': v['progress_type'],
                                  'progress_type_name': v['progress_type_name'],
                                  'msg_type': msg_type,
                                  'record_time_format': str(v['record_time_format'])})
    return prompt_input, progress_list


def get_cdss_medical_order_input(prompt_input, progress_list,cid, rid, start_date, end_date,order_type_list,  config_map):
    sql = '''
                    select oo.customer_id,oo.record_id,oo.order_create_time,oo.order_stop_time,oo.order_class,oo.order_type,oo.order_content,oo.pathway,oo.dosage, oo.unit, oo.frequency,oo.description,oo.order_flag  from mt_patient_medical_order oo 
                where oo.record_id = {} and oo.order_flag<4 and oo.status=1
                    '''.format(rid)
    if order_type_list:
        sql += ''' and oo.order_type in ({}) '''.format(','.join([str(i) for i in order_type_list]))
    if start_date:
        sql += ''' and oo.order_create_time>'{}' '''.format(start_date)
    if end_date:
        sql += ''' and oo.order_create_time<'{}' '''.format(end_date)
    sql += ' order by oo.order_create_time'
    sql_rlt = sql_query(cid, sql, config_map)
    medical_order_list = []
    # medical_order_map = {}
    for v in sql_rlt:
        if not v["order_content"]:
            continue
        # medical_order_map[v["order_content"]] = 1
        order_class = v['order_class'] if v['order_class'] else 0
        order_type = v['order_type'] if v['order_type'] else 0
        order_flag = v['order_flag'] if v['order_flag'] else 0
        order_class_str = {1:'住院医嘱', 2:'门诊医嘱', 3:'急诊医嘱', 4:'出院医嘱', 5:'术中医嘱'}.get(order_class, '')
        order_type_str = {1:'检验',2:'检查',3:'药品',4:'护理',5:'膳食（食物）',6:'手术',7:'处置',8:'会诊',9:'死亡医嘱',10:'停止医嘱',11:'输血医嘱', 12:'描述医嘱', 13:'卫材'}.get(order_type, '')
        order_flag_str = {1:'已开立', 2:'已执行', 3:'已停止'}.get(order_flag, '')

        medical_order_list.append(
            '{}-{} {}({})({})({}) {} {} {} {}'.format(v['order_create_time'], v['order_stop_time'], v['order_content'],order_class_str, order_type_str, order_flag_str, v['dosage'], v['unit'], v['frequency'], v['pathway']).replace(
                'None', ''))
    # medical_order_list = list(medical_order_map.keys())
    if len(medical_order_list) > 0:
        progress_text = '医嘱内容：\n' + '；\n'.join(medical_order_list)
        prompt_input += prompt_input_process(progress_text, len(progress_list))
        progress_list.append({'progress_id': -1, 'progress_type': 7001, 'progress_type_name': '格式化医嘱', 'record_time_format': ''})
    return prompt_input, progress_list


def get_cdss_test_input(prompt_input, progress_list,cid, rid, start_date, end_date,  config_map, cdss_type='mt', is_positive=False):
    # 每天的异常检验
    test_table_name = "mt_patient_test"
    test_item_table_name = f"mt_patient_test_item_{int(rid) % 10}"
    if cdss_type == 'ot':
        test_table_name = "ot_patient_test"
        test_item_table_name = "ot_patient_test_item"
    sql = '''select ii.create_date, ii.test_item, ii.test_result, ii.test_value_unit, ii.test_value_change,ii.qualitative_result_value,tt.test_name,tt.report_time test_date_time,tt.create_date
                from {}  ii inner join {} tt on ii.test_id=tt.id
                where ii.customer_id={} and ii.record_id={} '''.format(test_item_table_name,test_table_name, cid, rid)
    if start_date:
        sql += ''' and tt.report_time>'{}' '''.format(start_date)
    if end_date:
        sql += ''' and tt.report_time<'{}' '''.format(end_date)
    sql += ' order by tt.report_time'
    sql_rlt = sql_query(cid, sql, config_map)
    exist_map = {}

    test_last_map = {}
    for it in sql_rlt:
        if not it["test_date_time"] or not it["test_item"]:
            continue
        test_item = normalize_name(it["test_item"])
        test_date_time = it["test_date_time"].strftime("%Y-%m-%d")

        value_change = ''
        if str(it['qualitative_result_value']).lower() == 'h' or str(it['test_value_change']).lower() == 'h' or str(it['test_value_change']).find('高')>-1:
            value_change = '↑'
        elif str(it['qualitative_result_value']).lower() == 'l' or str(it['test_value_change']).lower() == 'l' or str(it['test_value_change']).find('低')>-1:
            value_change = '↓'
        test_value_unit = it["test_value_unit"] if it["test_value_unit"] else ""
        if not test_value_unit:
            value_change = ''
        if is_positive and not value_change:
            continue
        key = '{}_{}'.format(test_date_time, test_item)
        if key in exist_map:
            continue
        exist_map[key] = 1

        if test_date_time not in test_last_map:
            test_last_map[test_date_time] = {}
        if it['test_name'] not in test_last_map[test_date_time]:
            test_last_map[test_date_time][it['test_name']] = {}
        test_last_map[test_date_time][it['test_name']][test_item] = '{} {}'.format(it["test_item"], str(it["test_result"]) + test_value_unit + value_change)
    test_result = ''
    for test_date_time in sorted(test_last_map):
        for test_name in test_last_map[test_date_time]:
            new_item_list = sorted([_v for _, _v in test_last_map[test_date_time][test_name].items()])
            if len(new_item_list) == 0:
                continue
            test_result += '{} {}：{}。\n'.format(test_date_time, test_name, '，'.join(new_item_list))

    if test_result:
        progress_text = '检验结果：\n' + test_result
        prompt_input += prompt_input_process(progress_text, len(progress_list))
        progress_list.append(
            {'progress_id': -1, 'progress_type': 4001, 'progress_type_name': '检验结果', 'record_time_format': ''})
    return prompt_input, progress_list


def get_cdss_exam_input(prompt_input, progress_list, cid, rid, start_date, end_date, config_map, cdss_type='mt'):
    sql = '''select record_time_format as examination_time, examination_name, examination_method,examination_part, examination_result
                from {}_patient_examination
                where customer_id={} and record_id={}  '''.format(cdss_type, cid, rid)
    if start_date:
        sql += ''' and record_time_format>'{}' '''.format(start_date)
    if end_date:
        sql += ''' and record_time_format<'{}' '''.format(end_date)
    sql += ' order by record_time_format'
    sql_rlt = sql_query(cid, sql, config_map)
    rlt = []
    exist_map = {}
    for it in sql_rlt:
        if not it["examination_time"] or not it["examination_name"]:
            continue
        examination_time = it["examination_time"].strftime("%Y-%m-%d")
        key = '{}_{}'.format(examination_time, it["examination_name"])
        if key in exist_map:
            continue
        exist_map[key] = 1
        row = "{} {}：{}".format(examination_time, it["examination_name"], it["examination_result"])
        rlt.append(markdown_ft(row))
    if len(rlt)>0:
        progress_text = '检查结果：\n'+'\n'.join(rlt)
        prompt_input += prompt_input_process(progress_text, len(progress_list))
        progress_list.append(
            {'progress_id': -1, 'progress_type': 8001, 'progress_type_name': '检查结果报告', 'record_time_format': ''})
    return prompt_input, progress_list


def get_vital_signs_input(prompt_input, progress_list, cid, rid, start_date, end_date, config_map):
    progress_text = ''
    sql = '''select blood_pressure,systolic_pressure,diastolic_pressure,pressure_unit,breathing_value,breathing_unit,heart_rate_value,heart_rate_unit,pulse_value,pulse_unit,temperature_value,temperature_unit from mt_patient_vital_signs 
        where record_id = {} and state=1 '''.format(rid)
    if start_date:
        sql += ''' and record_time>'{}' '''.format(start_date)
    if end_date:
        sql += ''' and record_time<'{}' '''.format(end_date)
    sql += ' order by record_time desc'
    sql_rlt = sql_query(cid, sql, config_map)
    for it in sql_rlt:
        if it["pressure_unit"] and progress_text.find('收缩压') == -1:
            progress_text += '收缩压：{}{}，舒张压：{}{};\n'.format(it["systolic_pressure"],it["pressure_unit"],it["diastolic_pressure"],it["pressure_unit"])
        if it["breathing_unit"] and progress_text.find('呼吸') == -1:
            progress_text += '呼吸：{}{};\n'.format(it["breathing_value"],it["breathing_unit"])
        if it["heart_rate_unit"] and progress_text.find('心率') == -1:
            progress_text += '心率：{}{};\n'.format(it["heart_rate_value"],it["heart_rate_unit"])
        if it["pulse_unit"] and progress_text.find('脉搏') == -1:
            progress_text += '脉搏：{}{};\n'.format(it["pulse_value"],it["pulse_unit"])
        if it["temperature_unit"] and progress_text.find('体温') == -1:
            progress_text += '体温：{}{};\n'.format(it["temperature_value"],it["temperature_unit"])
    if progress_text:
        progress_text = '生命体征：\n' + progress_text
        prompt_input += prompt_input_process(progress_text, len(progress_list))
        progress_list.append(
            {'progress_id': -1, 'progress_type': 2006, 'progress_type_name': '生命体征格式化记录', 'record_time_format': ''})
    return prompt_input, progress_list


def get_patient_info_input(prompt_input, progress_list, cid, rid, config_map, cdss_type='mt'):
    sql = '''select mm.patient_name,mm.patient_age,mm.patient_age_type,mm.patient_gender,mm.marital from mt_patient_record mm
                where mm.id={}  '''.format(rid)
    sql_rlt = sql_query(cid, sql, config_map)
    if len(sql_rlt)>0:
        progress_text = '患者基本信息：\n'
        if sql_rlt[0]['patient_name']:
            progress_text += '姓名：{}\n'.format(sql_rlt[0]['patient_name'])
        if sql_rlt[0]['patient_age']:
            progress_text += '年龄：{}{}\n'.format(sql_rlt[0]['patient_age'], sql_rlt[0]['patient_age_type'])
        if sql_rlt[0]['patient_gender'] is not None:
            gender_str = '男' if sql_rlt[0]['patient_gender']==1 else '女'
            progress_text += '性别：{}\n'.format(gender_str)
        if sql_rlt[0]['marital'] is not None:
            marital_str = '已婚' if sql_rlt[0]['marital'] == 1 else '未婚'
            progress_text += '婚姻状况：{}\n'.format(marital_str)
        prompt_input += prompt_input_process(progress_text, len(progress_list))
        progress_list.append(
            {'progress_id': -1, 'progress_type': 1000001, 'progress_type_name': '患者基本信息', 'record_time_format': ''})
    return prompt_input, progress_list

def get_cdss_progress_and_date(cid, rid, start_date, end_date, progress_type_in, progress_type_not_in, config_map):
    sql = '''select id progress_id,progress_type,progress_type_name,progress_message progress_text,date_format(record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format from mt_patient_progress 
    where record_id={} and progress_status!=9 
            '''.format(rid)
    if start_date:
        sql += ''' and record_time_format>'{}' '''.format(start_date)
    if end_date:
        sql += ''' and record_time_format<'{}' '''.format(end_date)
    if progress_type_in:
        sql += ''' and progress_type in ({}) '''.format(','.join([str(i) for i in progress_type_in]))
    if progress_type_not_in:
        sql += ''' and progress_type not in ({}) '''.format(','.join([str(i) for i in progress_type_not_in]))
    sql += ' order by record_time_format desc limit 1'
    xs = sql_query(cid, sql, config_map)
    progress_date = ''
    progress_text = ''
    if len(xs)>0:
        progress_date = xs[0]['record_time_format']
        progress_text = xs[0]['progress_text'] + '\n'
    return progress_date, progress_text


def get_cdss_before_admission_input(prompt_input, progress_list, cid, rid,max_length, config_map):
    sql = '''select w.* from
						(SELECT r.id,r.customer_id,r.patient_guid,a.admission_time_format as real_time,r.record_type,r.create_date,r.patient_age,r.patient_name,r.patient_gender,r.inpatient_department as admission_department
            from mt_patient_record_property a, mt_patient_record r
            where r.id = a.record_id and r.patient_guid in (select patient_guid from mt_patient_record where id = {})
            UNION ALL
            SELECT r.id,r.customer_id,r.patient_guid,re.reg_datetime as real_time,1 as record_type,r.create_date,r.patient_age,r.patient_name,r.patient_gender,COALESCE(r.inpatient_department,re.reg_dept_name) as admission_department
            FROM ot_patient_reg re,ot_patient_record r
            where re.record_id = r.id and r.patient_guid in (select patient_guid from mt_patient_record where id = {}))w ORDER BY w.real_time desc ;
            '''.format(rid, rid)
    xs = sql_query(cid, sql, config_map)
    current_admission_department = ''
    current_admission_time_str = ''
    rid_rank_map = {}
    for i, x in enumerate(xs):
        if not x['real_time']:
            continue
        if x['id'] == int(rid):
            current_admission_department = x['admission_department']
            current_admission_time_str = str(x['real_time'])
        else:
            admission_time_str = str(x['real_time'])
            if not current_admission_time_str or not admission_time_str:
                continue
            current_admission_time = datetime.fromisoformat(current_admission_time_str)
            admission_time = datetime.fromisoformat(admission_time_str)
            if admission_time > current_admission_time-timedelta(days=7):
                rid_rank_map[x['id']] = {"rank":(len(xs)-i) + 10000, "record_type":x['record_type']}
            elif admission_time > current_admission_time-timedelta(days=31):
                if current_admission_department == x['admission_department']:
                    rid_rank_map[x['id']] = {"rank":(len(xs)-i) + 1000, "record_type":x['record_type']}
                else:
                    rid_rank_map[x['id']] = {"rank":(len(xs)-i) + 100, "record_type":x['record_type']}
    for k, v in sorted(rid_rank_map.items(), key=lambda item: item[1]['rank'], reverse=True):
        if prompt_input:
            break
        cdss_type = 'mt' if v['record_type'] == 2 else 'ot'
        rid = int(k)
        if v['rank'] > 1000:
            prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list,cid, rid, '', '', [], [], config_map, cdss_type)
        if v['rank'] > 1000 and len(prompt_input) < max_length and cdss_type == 'mt':
            prompt_input, progress_list = get_cdss_medical_order_input(prompt_input, progress_list,cid, rid, '', '',[], config_map)
        if len(prompt_input) < max_length:
            prompt_input, progress_list = get_cdss_exam_input(prompt_input, progress_list, cid, rid, '', '', config_map, cdss_type)
        if len(prompt_input) < max_length:
            prompt_input, progress_list = get_cdss_test_input(prompt_input, progress_list, cid, rid, '', '', config_map, cdss_type)
    return prompt_input, progress_list


def calculate_time(record_date, days):
    if not record_date:
        return ''
    # date_format = "%Y-%m-%d"
    # date_object = datetime.strptime(record_date, date_format)
    date_object = datetime.fromisoformat(record_date)
    if days < 0:
        new_date_object = date_object - timedelta(days=-days)
    else:
        new_date_object = date_object + timedelta(days=days)

    # new_date_string = new_date_object.strftime(date_format)
    new_date_string = str(new_date_object)
    return new_date_string


def get_sssj(cid, rid, config_map):
    '''手术日期的判断标准（优先级从高到低）：
1. 如果有手术申请单，取申请单中的手术日期。
2. 有手术记录，取手术记录中的手术日期
3. 有术后首次病程记录，以记录日为手术日
4. 有出院记录，提取出院记录中记录的手术日期（如存在）
5. 如果以上数据都没有，则判断为手术尚未进行，提取全量病历信息进行生成。'''
    sql = '''
                select operation_date from mt_patient_operation_apply where record_id ={} order by id desc limit 1
                 '''.format(rid)
    results = sql_query(cid, sql, config_map)
    for v in results:
        if v['operation_date']:
            return str(v['operation_date'])
    sql = '''
                    select operation_start_time from mt_patient_operation_register where record_id ={} order by id desc limit 1
                     '''.format(rid)
    results = sql_query(cid, sql, config_map)
    for v in results:
        if v['operation_start_time']:
            return str(v['operation_start_time'])
    sql = '''
                        select date_format(record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format from mt_patient_progress 
    where record_id={} and progress_type in (6,13) and progress_status!=9 order by record_time_format desc limit 1
                         '''.format(rid)
    results = sql_query(cid, sql, config_map)
    for v in results:
        if v['record_time_format']:
            return str(v['record_time_format'])
    return ''


def get_record_time_by_progress_id(cid, progress_id_list, config_map):
    sql = '''select pp.customer_id, pp.id progress_id,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format from mt_patient_progress pp
    where pp.id in ({});
            '''.format(','.join([str(i) for i in progress_id_list]))
    xs = sql_query(cid, sql, config_map)
    progress_id2record_time = {}
    for v in xs:
        progress_id2record_time[v['progress_id']] = str(v['record_time_format'])
        cid = v['customer_id']
    return progress_id2record_time, cid


async def call_text_parse(progress_param,config_map):
    text_list = []
    progress_text = progress_param['progress_text'].replace('​', '').replace(' ', '')
    json_obj = {
        "recordId": int(progress_param['record_id']),
        "customerId": int(progress_param['customer_id']),
        "progressType": int(progress_param['progress_type']),
        "msgType": int(progress_param['msg_type']),
        "progressText": progress_text
    }
    pangoo_url = 'http://{}/go_pangoo/pangoo/progress/progressParserAcc'.format(config_map['app']['pangoo_ip'])
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(pangoo_url, data=json.dumps(json_obj)) as response:
                content = await response.text()
                content_list = json.loads(content).get('body', [])
                for it in content_list:
                    text_list.append({
                        "attribute_name": it['attributeName'],
                        "content": it["attributeValue"].replace(it["attributeKey"], '')
                    })
                if len(content_list) == 0:
                    text_list.append({
                        "attribute_name": "内容",
                        "content": progress_text
                    })
    except Exception as e:
        logging.error(f"调用text parse API时发生未预期的错误: {e}, json_obj:{json_obj}", exc_info=True)
    return progress_param['progress_id'], text_list


async def get_text_parse(progress_param_list, progress_id2text_parse,config_map):
    tasks = [call_text_parse(progress_param,config_map) for progress_param in progress_param_list]
    results = await asyncio.gather(*tasks)
    for r in results:
        progress_id2text_parse[r[0]] = r[1]


def prompt_input_process(progress_input, progress_idx):
    progress_input = normalize_text(progress_input).replace(' ','')
    prompt_input = '[{}]{}\n{}\n'.format(progress_idx+1, '-'*10, progress_input)
    return prompt_input


def get_progress_references(cid, rid, prompt, progress_list, config_map):
    progress_references = []
    prompt_input_list = re.split(r"\[\d+\]----------", prompt)
    if len(prompt_input_list) > 1 and len(prompt_input_list) == len(progress_list)+1:
        progress_id_list = [p['progress_id'] for p in progress_list if p['progress_id'] > 0]
        progress_id2record_time, cid = get_record_time_by_progress_id(cid, progress_id_list, config_map)
        progress_id2text_parse = {}
        progress_param_list = []
        prompt_input_list_new = []
        for i, prompt_input in enumerate(prompt_input_list[1:]):
            if prompt_input.find('【输出】') > -1:
                prompt_input = prompt_input[:prompt_input.find('【输出】')]
            elif prompt_input.find('【问题】') > -1:
                prompt_input = prompt_input[:prompt_input.find('【问题】')]
            elif prompt_input.find('【严格按照示例的格式进行输出】') > -1:
                prompt_input = prompt_input[:prompt_input.find('【严格按照示例的格式进行输出】')]
            prompt_input = prompt_input.strip()
            prompt_input_list_new.append(prompt_input)
            if progress_list[i]['progress_id'] == -1:
                continue
            progress_param_list.append({'customer_id':cid,'record_id':rid,'progress_text':prompt_input,'progress_id':progress_list[i]['progress_id'],'progress_type':progress_list[i]['progress_type'],'msg_type':progress_list[i]['msg_type']})
        loop1 = asyncio.new_event_loop()
        asyncio.set_event_loop(loop1)
        loop1.run_until_complete(get_text_parse(progress_param_list, progress_id2text_parse,config_map))
        loop1.close()
        # asyncio.run(get_text_parse(progress_param_list, progress_id2text_parse,config_map))
        for i, prompt_input in enumerate(prompt_input_list_new):
            progress_modify = 0
            if progress_id2record_time.get(progress_list[i]['progress_id'], '') != progress_list[i]['record_time_format']:
                progress_modify = 1
            if progress_list[i]['progress_id'] == -1:
                contents = [{
                    "attribute_name": '内容',
                    "content": prompt_input
                }]
            else:
                contents = progress_id2text_parse.get(progress_list[i]['progress_id'], [])
            progress_map = {"index_id": str(i+1),
                            "customer_id": cid,
                            "progress_type_name": progress_list[i]['progress_type_name'],
                            "progress_type": progress_list[i]['progress_type'],
                            "progress_modify": progress_modify,
                            "record_time_format": str(progress_list[i]['record_time_format']),
                            "contents": contents
                            }
            progress_references.append(progress_map)
    return progress_references


def get_progress_references_discharge(cid, rid, prompt_progress_map, real_idx_map, config_map):
    progress_references = []
    exist_map = {}
    progress_id_list = []
    prompt_input_list_new = []
    progress_id2text_parse = {}
    progress_param_list = []
    new_progress_list = []
    for prompt_type in prompt_progress_map:
        prompt = prompt_progress_map[prompt_type]['prompt']
        progress_list = prompt_progress_map[prompt_type]['progress_list']

        prompt_input_list = re.split(r"\[\d+\]----------", prompt)
        if len(prompt_input_list) > 1 and len(prompt_input_list) == len(progress_list)+1:
            progress_id_list.extend([p['progress_id'] for p in progress_list if p['progress_id'] > 0])
            for i, prompt_input in enumerate(prompt_input_list[1:]):
                progress_map = progress_list[i]
                key = '{}-{}-{}'.format(progress_map['progress_id'], progress_map['progress_type'], progress_map['progress_type_name'])
                if key in exist_map:
                    continue
                exist_map[key] = 1
                for w in ['【输出', '【出院情况】', '【出院医嘱】']:
                    if prompt_input.find(w) > -1:
                        prompt_input = prompt_input[:prompt_input.find(w)]
                prompt_input = prompt_input.strip()
                prompt_input_list_new.append(prompt_input)
                new_progress_list.append(progress_map)
                if progress_list[i]['progress_id'] == -1:
                    continue
                progress_param_list.append({'customer_id':cid,'record_id':rid,'progress_text':prompt_input,'progress_id':progress_list[i]['progress_id'],'progress_type':progress_list[i]['progress_type'],'msg_type':progress_list[i]['msg_type']})

    loop2 = asyncio.new_event_loop()
    asyncio.set_event_loop(loop2)
    loop2.run_until_complete(get_text_parse(progress_param_list, progress_id2text_parse,config_map))
    loop2.close()
    # asyncio.run(get_text_parse(progress_param_list, progress_id2text_parse,config_map))
    progress_id2record_time, cid = get_record_time_by_progress_id(cid, progress_id_list, config_map)
    for i, prompt_input in enumerate(prompt_input_list_new):
        progress_modify = 0
        if progress_id2record_time.get(new_progress_list[i]['progress_id'], '') != new_progress_list[i]['record_time_format']:
            progress_modify = 1
        if new_progress_list[i]['progress_id'] == -1:
            contents = [{
                "attribute_name": '内容',
                "content": prompt_input
            }]
        else:
            contents = progress_id2text_parse.get(new_progress_list[i]['progress_id'], [])
        key = '{}-{}-{}'.format(new_progress_list[i]['progress_id'], new_progress_list[i]['progress_type'],
                                new_progress_list[i]['progress_type_name'])
        index_id = real_idx_map.get(key, str(i+1))
        progress_map = {"index_id": index_id,
                        "progress_type_name": new_progress_list[i]['progress_type_name'],
                        "progress_type": new_progress_list[i]['progress_type'],
                        "progress_modify": progress_modify,
                        "record_time_format": new_progress_list[i]['record_time_format'],
                        "contents": contents
                        }
        progress_references.append(progress_map)
    return progress_references


def get_llm_prompt_input(prompt_input, progress_list, input_param_list, cid, rid, cdss_type, config_map):
    max_length = int(config_map['llm_model']['input_max_len'])
    for input_param in input_param_list:
        progress_type_filter_str = input_param.get('progress_type_filter', '')
        progress_type_in_str = input_param.get('progress_type_in', '')
        progress_type_not_in_str = input_param.get('progress_type_not_in', '')
        start_date = input_param.get('start_date', '')
        end_date = input_param.get('end_date', '')
        input_type = input_param.get('input_type', '')
        if len(prompt_input) >= max_length:
            break
        if input_type.find('text') > -1:
            progress_type_filter_map = {}
            if progress_type_filter_str:
                try:
                    progress_type_filter_map = eval(progress_type_filter_str)
                except:
                    print('eval error progress_type_filter_str:', progress_type_filter_str)

            progress_type_in = []
            progress_type_not_in = []
            if progress_type_in_str:
                progress_type_in = progress_type_in_str.split(',')
            if progress_type_not_in_str:
                progress_type_not_in = progress_type_not_in_str.split(',')

            prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date,
                                                                  progress_type_in, progress_type_not_in, config_map, cdss_type)
            if progress_type_filter_map:
                prompt_input_new_list = []
                progress_references = get_progress_references(cid, rid, prompt_input, progress_list, config_map)
                for progress_reference in progress_references:
                    prompt_input_new = ''
                    progress_type_name = progress_reference['progress_type_name']
                    if progress_type_name not in progress_type_filter_map:
                        continue
                    for ct in progress_reference['contents']:
                        attribute_name = ct['attribute_name']
                        if attribute_name not in progress_type_filter_map[progress_type_name]:
                            continue
                        prompt_input_new += '{}：{}\n'.format(attribute_name, ct['content'])
                    if prompt_input_new:
                        prompt_input_new_list.append(prompt_input_new)
                if prompt_input_new_list:
                    prompt_input = ''
                    for progress_idx in range(len(prompt_input_new_list)):
                        prompt_input += '[{}]{}\n{}\n'.format(progress_idx + 1, '-' * 10, prompt_input_new_list[progress_idx])
        elif input_type.find('test') > -1:
            prompt_input2, progress_list = get_cdss_test_input(prompt_input, progress_list, cid, rid, start_date,
                                                               end_date, config_map, cdss_type)
            if len(prompt_input2) < max_length:
                prompt_input = prompt_input2
        elif input_type.find('exam') > -1:
            prompt_input2, progress_list = get_cdss_exam_input(prompt_input, progress_list, cid, rid, start_date,
                                                               end_date, config_map, cdss_type)
            if len(prompt_input2) < max_length:
                prompt_input = prompt_input2
        elif input_type.find('order') > -1 and cdss_type!='ot':
            prompt_input2, progress_list = get_cdss_medical_order_input(prompt_input, progress_list, cid, rid,
                                                                        start_date, end_date, [], config_map)
            if len(prompt_input2) < max_length:
                prompt_input = prompt_input2
        elif input_type.find('info') > -1:
            prompt_input2, progress_list = get_patient_info_input(prompt_input, progress_list, cid, rid, config_map, cdss_type)
            if len(prompt_input2) < max_length:
                prompt_input = prompt_input2
    return prompt_input, progress_list


def get_rid_list_by_guid(cid, rid, config_map):
    sql = '''select w.* from
    						(SELECT r.id,r.customer_id,r.patient_guid,a.admission_time_format as real_time,r.record_type,r.create_date,r.patient_age,r.patient_name,r.patient_gender,r.inpatient_department as admission_department
                from mt_patient_record_property a, mt_patient_record r
                where r.id = a.record_id and r.empi in (select empi from mt_patient_record where id = {} and empi>0)
                )w ORDER BY w.real_time desc ;
                '''.format(rid)
    xs = sql_query(cid, sql, config_map)
    if len(xs) == 0:
        sql = '''
              SELECT r.id,r.customer_id,r.patient_guid,a.admission_time_format as real_time,r.record_type,r.create_date,r.patient_age,r.patient_name,r.patient_gender,r.inpatient_department as admission_department from mt_patient_record_property a, mt_patient_record r where r.id = a.record_id and r.id = {} order by real_time desc; '''.format(rid)
        xs = sql_query(cid, sql, config_map)

    current_admission_time_str = ''
    rid_list = []
    if len(xs) == 0:
        rid_list = [rid]
    for i, x in enumerate(xs):
        if not x['real_time']:
            continue
        if x['id'] == int(rid):
            current_admission_time_str = str(x['real_time'])
            rid_list.append(x)
        else:
            admission_time_str = str(x['real_time'])
            if not current_admission_time_str or not admission_time_str:
                continue
            current_admission_time = datetime.fromisoformat(current_admission_time_str)
            admission_time = datetime.fromisoformat(admission_time_str)
            if admission_time < current_admission_time:
                rid_list.append(x)
    return rid_list
