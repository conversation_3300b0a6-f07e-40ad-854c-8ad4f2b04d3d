# coding:utf-8
from web.web_utils.sql_util import sql_query, select_by_sql
from web.web_utils.db_util import execute_sql_many, execute_sql


def get_llm_agent_list(request_data, config_map):
    customer_id = request_data.get("customer_id", 1001)
    user_code = request_data.get('user_code', '')
    access_flag = request_data.get('access_flag', 'm')
    page_source = int(request_data.get('page_source', -1))
    progress_type = int(request_data.get('progress_type', -1))
    attention_flag = 0
    sql = '''
                select * from gpt_agent_type where type<2 and flag=1 and sort is not null;
            		 '''
    agent_type_name_map = {}
    xs = sql_query(customer_id, sql, config_map)
    for v in xs:
        agent_type_name_map[v['code']] = v

    llm_agent_list = []
    agent_type2agent_list = {}
    # if '0' not in agent_type_name_map:
    #     return llm_agent_list
    # agent_type_name = agent_type_name_map['0']['name']
    # agent_sort = agent_type_name_map['0']['sort']
    # agent_type2agent_list[agent_type_name] = {"agent_type": '0',"agent_sort":agent_sort, "agent_type_name": agent_type_name, "agent_list": []}
    # agent_type2agent_list['我的关注'] = {"agent_type": 4, "agent_type_name": '我的关注', "agent_list": []}
    sql = '''select w.* FROM
    (SELECT
	cc.*,ss.access_flag,ss.page_source,ss.progress_type
FROM
	gpt_agent_config_customer cc
	left join gpt_agent_scene_customer ss on cc.code=ss.agent_code
WHERE
	cc.`status` = 2 and cc.flag=1
 	and cc.user_code in ("{}","agent_admin")
	and cc.customer_id in (-1,{}) 
	
UNION ALL
SELECT
	cc.*,ss.access_flag,ss.page_source,ss.progress_type
FROM
	gpt_agent_config cc
	left join gpt_agent_scene ss on cc.code=ss.agent_code
WHERE
	cc.`status` = 2 and cc.flag=1
 	and cc.user_code in ("{}","agent_admin")
	and cc.customer_id in (-1,{}) )w order by w.modify_date desc'''.format(user_code, customer_id, user_code, customer_id)
    xs = sql_query(customer_id, sql, config_map)
    agent_code2detail = {}
    for i, x in enumerate(xs):
        agent_type = str(x['type'])
        if agent_type not in agent_type_name_map:
            continue
        agent_type_name = agent_type_name_map[agent_type]['name']
        unfold = agent_type_name_map[agent_type]['unfold']
        if x['code'] not in agent_code2detail:
            agent_code2detail[x['code']] = {"agent_id": x['id'],"output_type": x.get('output_type', 1),
                                                            "agent_name": x['name'], "agent_type_name":agent_type_name,
                                                            "agent_code": x['code'], "agent_type":agent_type,"unfold":unfold,
                                                            "agent_icon": x['logo_url'], "attention_flag":0,"desc": x['desc'],
                                            'agent2scene':{}}
        if x['access_flag']:
            if x['access_flag'] not in agent_code2detail[x['code']]['agent2scene']:
                agent_code2detail[x['code']]['agent2scene'][x['access_flag']] = {'page_source_map':{},'progress_type_map':{}}
            if x['page_source']:
                agent_code2detail[x['code']]['agent2scene'][x['access_flag']]['page_source_map'][x['page_source']]=1
            if x['progress_type']:
                agent_code2detail[x['code']]['agent2scene'][x['access_flag']]['progress_type_map'][x['progress_type']]=1

    for agent_code in agent_code2detail:
        agent_type_name = agent_code2detail[agent_code]['agent_type_name']
        agent_type = agent_code2detail[agent_code]['agent_type']
        if agent_code2detail[agent_code]['agent2scene']:
            if access_flag not in agent_code2detail[agent_code]['agent2scene']:
                continue
            if agent_code2detail[agent_code]['agent2scene'][access_flag]['page_source_map'] and page_source not in agent_code2detail[agent_code]['agent2scene'][access_flag]['page_source_map']:
                continue
            if agent_code2detail[agent_code]['agent2scene'][access_flag]['progress_type_map'] and progress_type not in agent_code2detail[agent_code]['agent2scene'][access_flag]['progress_type_map']:
                continue
        if agent_type_name_map[agent_type]['middle_show'] != 1:
            continue
        # agent_type2agent_list[agent_type_name_map['0']['name']]['agent_list'].append(agent_code2detail[agent_code])

        if agent_type_name not in agent_type2agent_list:
            agent_sort = agent_type_name_map[agent_type]['sort']
            agent_type2agent_list[agent_type_name] = {"agent_type": agent_type, "agent_type_name": agent_type_name,"agent_sort":agent_sort,
                                                      "agent_list": []}
        agent_type2agent_list[agent_type_name]['agent_list'].append(agent_code2detail[agent_code])
    for k, v in sorted(agent_type2agent_list.items(), key=lambda item: item[1]['agent_sort'], reverse=True):
        llm_agent_list.append(v)
    return llm_agent_list


def get_llm_agent_by_code(agent_code, config_map, customer_id=1001):
    sql = '''
            SELECT * FROM gpt_agent_config_customer WHERE `status` = 2 and code = '{}' 
UNION ALL
SELECT a.* FROM gpt_agent_config a where `status` = 2 and a.code = '{}' 
        		 '''.format(agent_code, agent_code)
    xs = sql_query(customer_id, sql, config_map)
    dify_api = ''
    secret_key = ''
    agent_name = ''
    agent_code = ''
    dify_id = ''
    for v in xs:
        # dify_api = v['api']
        dify_api = 'http://{}:{}/v1/chat-messages'.format(config_map['llm_model']['dify_ip'], config_map['llm_model']['dify_port'])
        secret_key = v['secret_key']
        agent_name = v['name']
        agent_code = v['code']
        dify_id = v['dify_id']
        break
    return agent_name, agent_code, dify_api, secret_key, dify_id


def update_agent_attention(request_data, config_map):
    customer_id = request_data.get("customer_id", 1001)
    agent_id = request_data.get("agent_id")
    attention_flag = request_data.get("attention_flag", -1)
    user_code = request_data.get("user_code")
    ret = -1
    if attention_flag==1:
        sql = '''
                        INSERT INTO llm_agent_attention (agent_id, customer_id, user_code,flag) VALUES  (%s, %s,%s, %s);
                		 '''
        ret = execute_sql_many(sql, [[agent_id, customer_id, user_code, attention_flag]], 'hm_llm', config_map)
    elif attention_flag==0:
        sql = '''
                            update llm_agent_attention set flag=0 where user_code='{}' and agent_id={} ;
                             '''.format(user_code, agent_id)
        ret = execute_sql(sql, 'hm_llm', config_map)
    return ret
