# coding:utf-8
import json
from web.chat.chat import *
from web.web_utils.text_util import get_uuid,normalize_text
import hashlib
from web.mrg_service.model_offline import insert_admission_result, get_admission_result
from web.mrg_service.get_hmcdss_data import *

admission_field = [{"name":"主诉", "id": 2},
                   {"name":"现病史", "id": 10},
                   {"name":"既往史", "id": 13},
                   {"name":"过敏史", "id": 14},
                    {"name":"个人史", "id": 15},
                   {"name":"家族史", "id": 16},
                   # {"name":"体格检查", "id": 3},
                   {"name":"辅助检查", "id": 20},
                   # {"name":"初步诊断", "id": 86}
                   ]
admission_field_map = {it["name"]:it["id"] for it in admission_field}

# 主诉、现病史、既往史、过敏史、家族史、个人史、体格检查、辅助检查、初步诊断
def assemble_admission_summary(request_data, config_map):
    prompt,answer_origin,error_msg = get_chat_response(request_data, config_map, chat_type='admission')
    answer_map = answer_split(answer_origin)
    generate_text = []
    for attr_map in admission_field:
        answer = answer_map.get(attr_map['name'],'')
        if attr_map['name']=='初步诊断':
            answer = answer[:answer.find('。')]
        item = {
            "dictionary_attribute_id": attr_map['id'],
            "attribute_name": attr_map['name'],
            "progress_content": answer
        }
        generate_text.append(item)
    request_data = json.loads(request_data.decode('utf-8'))
    serial_number = request_data.get('serial_number', '').strip()
    patient_guid = request_data.get('patient_guid', '').strip()
    if serial_number and patient_guid and len(generate_text)>0:
        cid = request_data.get("customer_id", 1001)
        rid = request_data.get("record_id")
        uuid = get_uuid()
        prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
        xs=[[prompt_hash, prompt, answer_origin,json.dumps(generate_text, ensure_ascii=False), uuid, "admission_model", cid, rid,serial_number,patient_guid, "入院记录",'']]
        insert_admission_result(serial_number,patient_guid, xs, config_map)
    return generate_text, ''

def answer_split(text):
    sc_reg = re.compile(r"[一二三四五六七八九十、]*(?:主诉|现病史|既往史|过敏史|家族史|个人史|体格检查|辅助检查|初步诊断|治疗建议)[:：]")
    key_reg = re.compile(r"主诉|现病史|既往史|过敏史|家族史|个人史|体格检查|辅助检查|初步诊断")
    keys = sc_reg.findall(text)
    values = sc_reg.split(text)[1:]
    rlt = {}
    if len(keys) == len(values):
        rlt = {key_reg.findall(k)[0]: v for k, v in zip(keys, values)}
    return rlt


def assemble_admission_summary_offline(cid, rid, config_map, medical_record=None):
    generate_text = []
    if not rid:
        if medical_record:
            serial_number = medical_record.get('serialNumber')
            if not serial_number:
                return generate_text, '', '缺少serialNumber', cid, rid
            cid, rid,patient_guid = get_record_id(cid, serial_number, config_map)
        else:
            return generate_text, '', '查不到患者的就诊记录', cid, rid

        admission_result = get_admission_result(serial_number, patient_guid, config_map)
        if len(admission_result)>0:
            uuid = admission_result[0]['uuid']
            generate_text_str = admission_result[0]['result']
            generate_text = json.loads(generate_text_str)
            generate_text2 = add_dadian_for_item('85', '204', uuid, '入院记录', generate_text)
            return generate_text2, uuid, '', cid, rid
    return generate_text, '', '未生成患者的入院记录', cid, rid


if __name__ == '__main__':
    a = '''主诉：
患者主诉XX天前出现XX症状，症状包括XX、XX、XX等。
现病史：
患者XX天前出现XX症状，症状逐渐加重。患者未采取任何治疗措施，症状未缓解。
既往史：
患者无重大疾病史，无手术史，无过敏史。
个人史：
患者无吸烟史，无酗酒史，无药物滥用史。
家族史：
患者无家族遗传疾病史。
体格检查：
一般情况：患者神志清楚，面色苍白，体力消耗明显。
皮肤：皮肤湿润，无明显异常。
呼吸系统：呼吸平稳，无明显呼吸困难。
心血管系统：心率正常，心音有力，无明显杂音。
消化系统：腹部平坦，无压痛，肠鸣音正常。
神经系统：神经系统检查未见明显异常。
辅助检查：
血常规：白细胞计数正常，红细胞计数正常。
尿常规：尿常规正常。
心电图：心电图显示正常。
胸部X光片：胸部X光片未见明显异常。
初步诊断：
根据患者的主诉、现病史和体格检查结果，初步诊断为XX疾病。
治疗计划：
1. 继续观察患者的病情变化，密切监测生命体征。
2. 给予相应的药物治疗，包括XX药物。
3. 给予适当的饮食，保证营养摄入。
4. 给予必要的休息和心理支持。
备注：
患者需住院治疗，密切观察病情变化，及时调整治疗方案。'''
    b = answer_split(a)
    print(b)