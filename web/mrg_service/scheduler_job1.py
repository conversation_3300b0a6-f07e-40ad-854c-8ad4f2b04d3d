# coding:utf-8
import json
import hashlib
from web.web_utils.text_util import get_uuid
from web.web_utils.sql_util import sql_query
from web.mrg_service.differential_diagnosis import get_prompt_map,get_differential_diagnosis
from web.mrg_service.gen_discharge_zljg_use_model import generate_operation_use_model
from web.mrg_service.model_offline import *


def get_need_predict_records(cid,progress_types,ignore_type , config_map):
    department = config_map['app']['department']
    sql = '''
             select rr.customer_id,rr.id record_id,rr.patient_age,rr.patient_age_type,rr.inpatient_department,rr.patient_gender,pp.progress_message,pp.progress_type,pp.record_time_format,ee.msg_type from mt_patient_record rr
 inner join mt_patient_progress pp on pp.record_id=rr.id 
 inner join mt_patient_progress_extend ee on pp.id=ee.progress_id
 where rr.inpatient_department regexp '{}' and pp.progress_type in ({}) 
 order by pp.record_time_format desc
    		 '''.format(department, ','.join(progress_types))

    record2progress_map = {}
    ignore_record_map = {}
    xs = sql_query(cid, sql, config_map)
    for x in xs:
        record_id = x['record_id']
        progress_type = x['progress_type']
        if progress_type==ignore_type:
            ignore_record_map[record_id] = progress_type
        if record_id not in record2progress_map:
            record2progress_map[record_id] = []
        record2progress_map[record_id].append(x)
    for record_id in ignore_record_map:
        record2progress_map.pop(record_id)
    return record2progress_map


def run_gen_diagnosis(config_map):
    cid = 1001
    prompt = '假设你是医生。下面是一份病历，请基于患者症状、病史等信息预测初步诊断、诊断依据、鉴别诊断。\n'
    record2progress_map = get_need_predict_records(cid,['1','2'],2 , config_map)
    for record_id in record2progress_map:
        xs = record2progress_map[record_id]
        gender = ''
        if xs[0]['patient_gender'] == 1:
            gender = '男'
        elif xs[0]['patient_gender'] == 0:
            gender = '女'
        start = 1
        base_info = '{}、患者,{},{}{}。\n'.format(start, gender, xs[0]['patient_age'], xs[0]['patient_age_type'])
        patient_progress = ''
        cid = xs[0]['customer_id']
        prompt_map = get_prompt_map(cid, xs, config_map)
        for k in ['主诉', '现病史', '专科情况']:
            if k in prompt_map:
                start += 1
                patient_progress += '{}、{}:{}\n'.format(start, k, prompt_map[k])
        if len(patient_progress)==0:
            continue
        prompt += base_info+patient_progress
        prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
        record2prompt = get_model_offline_result([str(record_id)], 'llm_diagnosis', config_map)
        if record_id in record2prompt:
            if prompt_hash in record2prompt[record_id]:
                continue
        xs = []
        for model_name, lora_model in zip(['模型1', '模型2', '模型3'],
                                          ['disease_model_1', 'disease_model_2', 'disease_model_3']):
            uuid = get_uuid()
            generate_text = get_differential_diagnosis(prompt, lora_model, config_map)
            xs.append([prompt_hash, prompt, json.dumps(generate_text, ensure_ascii=False), uuid, lora_model, cid, record_id,
                       model_name, ''])
        insert_model_result(xs, 'llm_diagnosis', config_map)
    return 1


def run_gen_operation(config_map):
    cid = 1001
    record2progress_map = get_need_predict_records(cid, ['10', '6', '13', '21'], 10, config_map)
    for record_id in record2progress_map:
        record_data_list = record2progress_map[record_id]
        cid = record_data_list[0]['customer_id']
        _, _, _, _ = generate_operation_use_model(cid, record_id, record_data_list,config_map)
    return 1

