# coding:utf-8
from web.web_utils.sql_util import sqls, sql_query
from web.web_utils.text_util import get_uuid
from web.mrg_service.get_hmcdss_data import *
from web.mrg_service.model_offline import *
import json
import hashlib


# 组装出院记录
def generate_differential_diagnosis(cid, rid, medical_record,medical_record_360, config_map):
    biz_code='84'
    module_code = '204'
    generate_text_list = []
#     differential_diagnosis_text = '''
#     初步诊断:乳房肿块(右乳)
# 鉴别诊断:1.乳腺小叶增生:乳腺小叶增生多见于30-50岁左右女性,其主要表现为显著性周期乳房疼痛和乳腺增厚结节感。疼痛与月经周期有关,无固定部位,月经来潮后疼痛缓解;查体乳房的外上象限可触及结节感或局部增厚感,疼痛与结节具有相同的周期性变化。该患者临床表现为无痛性边界不清的乳腺肿块,同时伴有同侧腋窝淋巴结肿大,其大小与月经周期无关,故暂不考虑该诊断。
# 2.乳腺脂肪坏死:多见于中老年,有乳腺外伤史,乳腺包块长时间无变化或缩小。乳腺包块质硬,边界不清,活动差,可有皮肤红肿或与皮肤粘连。针吸或切除活检有助于鉴别。该患者为中年女性,肿块边界不清,有皮粘,同侧腋窝未及肿大淋巴结,根据患者辅助检查结果,故与该诊断不符合。'''
    prompt,cid,rid = get_diagnosis_prompt(cid,rid,medical_record,medical_record_360,config_map)
    if len(prompt)==0 or prompt.find('主诉')==-1 or prompt.find('现病史')==-1:
        return generate_text_list, '入院记录缺少主诉/现病史'
    prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
    record2prompt = get_model_offline_result([str(rid)], 'llm_diagnosis', config_map)
    lora_model2feedback = get_feedback_result(rid, 'disease_model', config_map)
    if rid in record2prompt:
        if prompt_hash in record2prompt[rid]:
            for model_name in record2prompt[rid][prompt_hash]:
                dadian_content = '鉴别诊断-' + model_name
                generate_text_str, uuid, lora_model = record2prompt[rid][prompt_hash][model_name]
                generate_text = json.loads(generate_text_str)
                generate_text2 = add_dadian_for_item(biz_code,module_code,uuid,dadian_content,generate_text)
                # generate_text2 = get_treatment_plan(cid, rid, prompt, generate_text2, 'treatment_plan_model', config_map)
                dadian_map = {'bizCode': biz_code, 'moduleCode': module_code, 'ruleId': uuid}
                feedback = lora_model2feedback.get(lora_model, '')
                generate_text_list.append(
                    {'model_name': model_name,'lora_model': lora_model,'feedback': feedback, 'dadian': dadian_map, 'dadian_content': model_name,
                     'generate_text': generate_text2})
            return generate_text_list, ''

    xs = []
    # for model_name,lora_model in zip(['模型1','模型2','模型3'],['disease_model_1','disease_model_2','disease_model_3']):
    for model_name,lora_model in zip(['模型1'],['disease_model_1']):
        uuid = get_uuid()
        dadian_content = '鉴别诊断-'+model_name
        if lora_model!='disease_model_1' or prompt.find('初步诊断')==-1:
            prompt = prompt.replace('预测诊断依据、鉴别诊断','预测初步诊断、诊断依据、鉴别诊断')
        generate_text = get_differential_diagnosis(prompt, lora_model, config_map)
        # 过滤无诊断依据和鉴别诊断的结果
        if len(generate_text[-1]['progress_content'])>5:
            xs.append([prompt_hash, prompt, json.dumps(generate_text, ensure_ascii=False), uuid, lora_model, cid, rid, model_name, ''])
        generate_text2 = add_dadian_for_item(biz_code,module_code,uuid,dadian_content,generate_text)
        # generate_text2 = get_treatment_plan(cid, rid, prompt, generate_text2, 'treatment_plan_model', config_map)
        dadian_map = {'bizCode':biz_code,'moduleCode':module_code,'ruleId':uuid}
        feedback = lora_model2feedback.get(lora_model, '')
        generate_text_list.append({'model_name': model_name,'lora_model': lora_model,'feedback': feedback, 'dadian': dadian_map, 'dadian_content': dadian_content,
                                   'generate_text': generate_text2})
    insert_model_result(xs,'llm_diagnosis', config_map)
    return generate_text_list,''

def get_differential_diagnosis(prompt, lora_model, config_map):
    generate_text = []
    initial_diagnosis = ""
    differential_diagnosis = ""
    zdyj_diagnosis = ""
    if len(prompt)>0:
        differential_diagnosis_text = generate_differential_diagnosis_from_model(prompt,config_map,lora_model)
        initial_start = differential_diagnosis_text.find('初步诊断')
        differential_start = differential_diagnosis_text.find('鉴别诊断')
        zdyj_start = differential_diagnosis_text.find('诊断依据')
        if initial_start>-1:
            initial_end = min([x for x in [differential_start,zdyj_start, len(differential_diagnosis_text)] if x>initial_start])
            initial_diagnosis = differential_diagnosis_text[initial_start:initial_end].replace('初步诊断：','').replace('初步诊断:','').replace('初步诊断','')
        if differential_start>-1:
            differential_end = min(
                [x for x in [initial_start, zdyj_start, len(differential_diagnosis_text)] if x > differential_start])
            differential_diagnosis = differential_diagnosis_text[differential_start:differential_end].replace('鉴别诊断：','').replace('鉴别诊断:','').replace('鉴别诊断','')
        if zdyj_start>-1:
            zdyj_end = min(
                [x for x in [differential_start, initial_start, len(differential_diagnosis_text)] if x > zdyj_start])
            zdyj_diagnosis = differential_diagnosis_text[zdyj_start:zdyj_end].replace('诊断依据：','').replace('诊断依据:','').replace('诊断依据','')
    if lora_model=='disease_model_1':
        if prompt.find('初步诊断:')>-1:
            initial_diagnosis = prompt[prompt.find('初步诊断:')+5:]

    initial_diagnosis = get_standard_disease(initial_diagnosis)
    generate_text.append({
        "dictionary_attribute_id": 86,
        "attribute_name": "初步诊断",
        "progress_content": initial_diagnosis
    })
    zdyj_diagnosis = get_standard_content(zdyj_diagnosis)
    generate_text.append({
        "dictionary_attribute_id": 11,
        "attribute_name": "诊断依据",
        "progress_content": zdyj_diagnosis
    })
    differential_diagnosis = get_standard_content(differential_diagnosis)
    generate_text.append({
        "dictionary_attribute_id": 12,
        "attribute_name": "鉴别诊断",
        "progress_content": differential_diagnosis+'\n'
    })
    return generate_text

def get_standard_disease(sentence):
    pattern = r"\s*修正诊断.*$|医师签名[\s\S]*|\s*职称.*$|住院.*|主治.*|副?主任.*|新建S章节[\s\S]*|记录医师[\s\S]*"
    result = re.sub(pattern, "", sentence)
    return result

def get_standard_content(sentence):
    pattern = r"(\d+-\d+)[:\.]|(\d+)[\.:](?!\d)"
    segments = re.split(pattern, sentence)
    segments = [segment for segment in segments if segment != '' and segment is not None]
    content = ""
    pattern2 = r'^[1-9][0-9]*$|\d-\d'
    for i in range(len(segments)):
        match = re.match(pattern2, segments[i])
        if not match:
            content += segments[i] + "\n"
        else:
            content += segments[i] + ":"
    return content

def get_treatment_plan(cid, rid, prompt,generate_text, lora_model, config_map):
    treatment_plan = ""
    initial_diagnosis = ""
    uuid = get_uuid()
    if len(prompt)>0:
        for item in generate_text:
            if item['dictionary_attribute_id']==86:
                initial_diagnosis = item['progress_content']
                break
        prompt_start = prompt.find('主诉:')
        prompt_end = len(prompt)
        for w in ['辅助检查', '初步诊断']:
            prompt_end = prompt.find(w+':')
            if prompt_end > -1:
                break
        prompt = prompt[prompt_start:prompt_end].replace('\n2、','').replace('\n3、','').replace('\n4、','').replace('\n5、','')
        prompt = '假设你是医生。下面是一份病历，请基于患者症状、病史等信息预测诊疗计划。初步诊断:{}\n{}'.format(initial_diagnosis, prompt)
        prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
        record2prompt = get_model_offline_result([str(rid)], 'llm_treatment_plan', config_map)
        if rid in record2prompt and prompt_hash in record2prompt[rid] and '诊疗计划' in record2prompt[rid][prompt_hash]:
            treatment_plan, uuid, lora_model = record2prompt[rid][prompt_hash]['诊疗计划']
        else:
            treatment_plan = generate_differential_diagnosis_from_model(prompt,config_map,lora_model)
            treatment_plan = treatment_plan.replace('诊疗计划：','')
            if treatment_plan:
                xs = [[prompt_hash, prompt, treatment_plan, uuid, 'treatment_plan_model', cid, rid, '诊疗计划', '']]
                insert_model_result(xs,'llm_treatment_plan', config_map)
    rule_id = '{}-{}'.format(uuid, 6)
    dadian_map = {'bizCode': '84', 'moduleCode': '204', 'ruleId': rule_id}
    generate_text.append({
        "dictionary_attribute_id": 6,
        "attribute_name": "诊疗计划",
        'dadian': dadian_map,
        'dadian_content': "诊疗计划",
        "progress_content": treatment_plan
    })
    return generate_text


def generate_differential_diagnosis_from_model_old(prompt,config_map,lora_model='disease_model_1', max_length=2048):
    json_obj = {"input":prompt, "max_length": max_length,"lora_model":lora_model}
    dia_url = 'http://{}:{}/generate'.format(config_map['llm_diagnosis']['chat_diagnosis_ip'],
                                                                         config_map['llm_diagnosis']['chat_diagnosis_port'])
    # return_json = post_url('http://*************:28018/generate', json_obj)
    return_json = post_url(dia_url, json_obj)
    differential_diagnosis = return_json.get('response','')
    return differential_diagnosis


def generate_differential_diagnosis_from_model(prompt,config_map,lora_model='disease_model_1', max_length=2048):
    json_obj = {"query":prompt}
    dia_url = 'http://{}:{}/inference'.format(config_map['llm_diagnosis']['chat_diagnosis_ip'], config_map['llm_diagnosis']['chat_diagnosis_port'])
    return_json = post_url(dia_url, json_obj)
    differential_diagnosis = return_json.get('response','')
    return differential_diagnosis

def get_diagnosis_prompt(cid,rid, medical_record,medical_record_360,config_map):
    inpatient_department = '呼吸科'
    prompt = '假设你是{}医生。下面是一份病历，请基于患者症状、病史等信息预测诊断依据、鉴别诊断。\n'
    # sql = sqls['progress_text'].format(cid, rid, 2)
    # text_list = sql_query(cid, sql, config_map)
    if not medical_record and not medical_record_360:
        return '',cid,rid
    if medical_record:
        serial_number = medical_record.get('serialNumber')
        inpatient_department = medical_record.get('inpatientDepartment', '呼吸科')
        prompt = prompt.format(inpatient_department)
        if not serial_number:
            return '',cid,rid
        cid, rid,_ = get_record_id(cid, serial_number, config_map)
        patientInfo = medical_record['patientInfo']
        gender = ''

        if str(patientInfo.get('gender',-1))=='1':
            gender = '男'
        elif str(patientInfo.get('gender',-1))=='0':
            gender = '女'
        age_and_unit = ''
        birthDate = patientInfo.get('birthDate', -1)
        if birthDate:
            rysj = find_rysj(cid, rid, config_map)
            age_and_unit = gen_age_unit_zheer(str(birthDate)[:10], str(rysj)[:10])
        else:
            age_and_unit = '{}{}'.format(patientInfo.get('age',''),patientInfo.get('ageType',''))

        start = 1
        base_info = '{}、患者,{},{}。\n'.format(start, gender, age_and_unit)
        patient_progress = ''
        is_record_type1 = False
        progressNoteList = medical_record.get('progressNoteList',[])
        for progressNote in progressNoteList:
            if progressNote['progressType'] not in [1,2]:
                continue
            if progressNote['progressType']==1:
                is_record_type1 = True
            messageList = progressNote.get('messageList', [])
            for k in ['主诉', '病历特点', '体格检查', '辅助检查', '初步诊断']:
                for message in messageList:
                    if k not in message['key']:
                        continue
                    if progressNote['progressType']==2:
                        prompt_input = str(message['value'])
                        if message['key'] == '辅助检查':
                            fzjc_start = prompt_input.find('检查结论')
                            if fzjc_start == -1:
                                fzjc_start = prompt_input.find('检查所见')
                            fzjc_start2 = prompt_input[:fzjc_start].find('。')
                            if fzjc_start - fzjc_start2 < 50:
                                fzjc_start = fzjc_start2 + 1
                            if fzjc_start == -1:
                                fzjc_start = 0
                            prompt_input = prompt_input[fzjc_start:]
                        if len(prompt_input) == 0:
                            continue
                        start += 1
                        patient_progress += '{}、{}:{}\n'.format(start, message['key'], prompt_input)
                    else:
                        if k not in ['主诉','现病史','专科情况']:
                            continue
                        if len(message['value']) ==0:
                            continue
                        start += 1
                        patient_progress += '{}、{}:{}\n'.format(start, message['key'], message['value'])
        if not is_record_type1 and rid:
            patient_progress2 = get_diagnosis_prompt_from_hmcdss(cid,rid,config_map)
            if patient_progress2:
                patient_progress = patient_progress2
        if not patient_progress:
            return '',cid,rid
        prompt += base_info + patient_progress
        return prompt,cid,rid

    if medical_record_360:
        prompt = prompt.format(inpatient_department)
        for medical_record in medical_record_360:
            progress = medical_record['progress']
            if progress.get('progress_type') != 1:
                continue
            start = 0
            patient_progress = ''
            messageList = medical_record.get('list', [])
            for message in messageList:
                attribute_key = message['attribute_key']
                attribute_name = message['attribute_name']
                attribute_value = message['attribute_value'].replace(attribute_key, '')
                if attribute_name in ['主诉', '现病史', '专科情况']:
                    if len(attribute_value) == 0:
                        continue
                    start += 1
                    patient_progress += '{}、{}:{}\n'.format(start, attribute_name, attribute_value)
            if not patient_progress:
                return '',cid,rid
            prompt += patient_progress
            return prompt,cid,rid
    return prompt,cid,rid


def get_diagnosis(request_data,config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    prompt = request_data.get('prompt', '').strip()
    max_length_str = request_data.get('max_length', None)
    max_length = 2048
    if not max_length_str and len(max_length_str)>0:
        max_length = int(max_length_str)
    differential_diagnosis = generate_differential_diagnosis_from_model(prompt, config_map, max_length=max_length)
    return differential_diagnosis

def get_diagnosis_prompt_from_hmcdss(cid,rid,config_map):
    if not rid:
        return ''
    xs = get_origin_progress(cid, rid, ['1'], config_map)
    start = 1
    patient_progress = ''
    prompt_map = get_prompt_map(cid, xs, config_map)
    for k in ['主诉', '现病史', '专科情况', '辅助检查', '初步诊断']:
        if k in prompt_map:
            prompt_input = str(prompt_map[k])
            if k == '辅助检查':
                fzjc_start = prompt_input.find('检查结论')
                if fzjc_start== -1:
                    fzjc_start = prompt_input.find('检查所见')
                fzjc_start2 = prompt_input[:fzjc_start].find('。')
                if fzjc_start-fzjc_start2<50:
                    fzjc_start = fzjc_start2+1
                if fzjc_start == -1:
                    fzjc_start = 0
                prompt_input = prompt_input[fzjc_start:]
            elif k == '初步诊断':
                prompt_input = get_standard_disease(prompt_input)
                # if prompt_input.find('医师签名')>-1:
                #     prompt_input = prompt_input[:prompt_input.find('医师签名')]
            if len(prompt_input)==0:
                continue
            start += 1
            patient_progress += '{}、{}:{}\n'.format(start, k, prompt_input)
    return patient_progress

def get_diagnosis_prompt_from_hmcdss_v2(cid,rid,config_map):
    if not rid:
        return ''
    xs = get_origin_progress(cid, rid, ['2'], config_map)
    patient_progress = ''
    prompt_map = get_prompt_map(cid, xs, config_map)
    for k in ['主诉','病历特点']:
        if k in prompt_map:
            prompt_input = str(prompt_map[k])
            if len(prompt_input)==0:
                continue
            patient_progress += '{}\n'.format(prompt_input)
    return patient_progress


def get_prompt_map(cid,xs,config_map):
    prompt_map = {}
    for x in xs:
        msg_type = x['msg_type']
        text = x["progress_text"]
        progress_type = x["progress_type"]
        sps = progress_parse(text, progress_type, msg_type, config_map, cid)
        for i in range(0, len(sps), 2):
            # if sps[i] in ['主诉', '现病史', '专科情况', '辅助检查']:
            attribute_value = markdown_ft(sps[i + 1])
            if len(attribute_value) == 0 or attribute_value=='-':
                continue
            if sps[i] not in prompt_map:
                prompt_map[sps[i]] = attribute_value
    return prompt_map

def is_leap_year(year):
    return (year % 400 == 0) or (year % 4 == 0 and year % 100 != 0)
def gen_age_unit_zheer(ds1, ds2):
    age_and_unit = ''
    mon2day = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
    ds_list1 = ds1.split('-')
    ds_list2 = ds2.split('-')
    if len(ds_list1)==3 and len(ds_list2)==3:
        y1,m1,d1 = int(ds_list1[0]),int(ds_list1[1]),int(ds_list1[2])
        y2,m2,d2 = int(ds_list2[0]),int(ds_list2[1]),int(ds_list2[2])
        delta_d = d2-d1
        real_delta_d = delta_d +1
        if delta_d < 0:
            real_delta_d = d2+mon2day[m1] - d1+1
            if is_leap_year(y1):
                real_delta_d += 1
            m2 -= 1
        delta_m = m2 - m1
        real_delta_m = delta_m
        if delta_m < 0:
            real_delta_m += 12
            y2 -= 1
        real_delta_y = y2 - y1
        if real_delta_y >0:
            if real_delta_m >0:
                age_and_unit = '{}岁{}月'.format(real_delta_y,real_delta_m)
            else:
                age_and_unit = '{}岁'.format(real_delta_y)
            return age_and_unit
        if real_delta_m > 0:
            age_and_unit = '{}月{}天'.format(real_delta_m, real_delta_d)
            return age_and_unit
        if real_delta_d > 0:
            age_and_unit = '{}天'.format(real_delta_d)
            return age_and_unit
    return age_and_unit

