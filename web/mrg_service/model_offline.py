# coding:utf-8
import sys
import re
from web.web_utils.text_util import progress_parse, markdown_ft, normalize_text
from web.web_utils.db_util import *


def get_model_offline_result(rids,table_name, config_map, limit_len=0):
    sql = '''
            select prompt_hash, prompt,record_id,answer,answer_type,lora_model,uuid from {} where record_id in ({})
    		 '''.format(table_name, ','.join(rids))
    if limit_len > 0:
        sql += ' order by id desc limit {}'.format(limit_len)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    record2prompt = {}
    for v in xs:
        record_id = v['record_id']
        prompt_hash = v['prompt_hash']
        if record_id not in record2prompt:
            record2prompt[record_id] = {}
        if prompt_hash not in record2prompt[record_id]:
            record2prompt[record_id][prompt_hash] = {}
        record2prompt[record_id][prompt_hash][v['answer_type']] = [v['answer'],v['uuid'],v['lora_model']]
    return record2prompt


def insert_model_result(xs,table_name, config_map):
    if len(xs) == 0:
        return -1
    sql = '''
            INSERT INTO {} (prompt_hash,prompt, answer, uuid, lora_model, customer_id, record_id, answer_type, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s);
    		 '''.format(table_name)
    ret = execute_sql_many(sql,xs, 'hm_llm', config_map)
    return ret

def delete_result(rids, table_name, config_map):
    sql = '''
            delete from {} where record_id in ({});
    		 '''.format(table_name, ','.join(rids))
    ret = execute_sql(sql, 'hm_llm', config_map)
    return ret

def insert_discharge_result(xs, config_map):
    sql = '''
            INSERT INTO llm_discharge (answer, uuid, treatment_uuid, customer_id, record_id, remark) VALUES  (%s, %s, %s, %s, %s, %s);
    		 '''
    ret = execute_sql_many(sql,xs, 'hm_llm', config_map)
    return ret

def get_feedback_result(rid,lora_model, config_map):
    sql = '''
            select lora_model, customer_id,record_id,doctor_name,feedback,create_date from llm_feedback where record_id = {} and lora_model regexp '{}' order by id;
    		 '''.format(rid, lora_model)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    lora_model2feedback = {}
    for v in xs:
        lora_model2feedback[v['lora_model']] = v['feedback']
    return lora_model2feedback

def insert_feedback_result(xs, config_map):
    sql = '''
            INSERT INTO llm_feedback (lora_model, answer_uuid, doctor_name, customer_id, record_id, feedback) VALUES  (%s, %s, %s, %s, %s, %s);
    		 '''
    ret = execute_sql_many(sql,xs, 'hm_llm', config_map)
    return ret

def insert_admission_result(serial_number, patient_guid, xs, config_map):
    if len(xs) == 0:
        return -1
    sql = '''
                delete from llm_admission where serial_number='{}' and patient_guid='{}';
        		 '''.format(serial_number, patient_guid)
    ret = execute_sql(sql, 'hm_llm', config_map)
    sql = '''
            INSERT INTO llm_admission (prompt_hash,prompt, answer,result, uuid, lora_model, customer_id, record_id,serial_number,patient_guid, answer_type, remark) VALUES (%s, %s, %s, %s, %s, %s, %s, %s,%s, %s, %s,%s);
    		 '''
    ret = execute_sql_many(sql,xs, 'hm_llm', config_map)
    return ret

def get_admission_result(serial_number, patient_guid, config_map):
    sql = '''
            select uuid,result from llm_admission where serial_number='{}' and patient_guid='{}';
        		 '''.format(serial_number, patient_guid)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    return xs