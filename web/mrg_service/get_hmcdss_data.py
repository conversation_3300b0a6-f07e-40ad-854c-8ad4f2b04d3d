# coding:utf-8
import sys
import re
from web.web_utils.text_util import progress_parse, markdown_ft, normalize_text
from web.web_utils.sql_util import sqls, sql_query

def get_record_id(cid, serial_number, config_map):
    sql = '''
            select id record_id,customer_id,patient_guid from mt_patient_record where serial_number ='{}' 
    		 '''.format(serial_number)
    disease_result = sql_query(cid, sql, config_map)
    for v in disease_result:
        if v['record_id']:
            return v['customer_id'], v['record_id'],v['patient_guid']
    return None, None, None

def get_origin_progress(cid, rid, progress_type_list, config_map):
    data_sql = sqls['progress_text'].format(cid, rid, ','.join(progress_type_list))
    xs = sql_query(cid, data_sql, config_map)
    return xs

def add_dadian_for_item(biz_code,module_code,uuid,dadian_content, generate_text):
    for t in generate_text:
        dictionary_attribute_id = t['dictionary_attribute_id']
        rule_id = '{}-{}'.format(uuid,dictionary_attribute_id)
        dadian_content2 = '{}-{}'.format(dadian_content,t['attribute_name'])
        dadian = {'bizCode':biz_code,'moduleCode':module_code,'ruleId':rule_id}
        t['dadian'] = dadian
        t['dadian_content'] = dadian_content2
    return generate_text
