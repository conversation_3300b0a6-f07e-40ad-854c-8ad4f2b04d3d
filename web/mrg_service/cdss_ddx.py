# coding:utf-8
from web.web_utils.sql_util import sqls, sql_query
from web.web_utils.text_util import get_uuid
from web.mrg_service.get_hmcdss_data import *
from web.mrg_service.model_offline import *
import json
import hashlib
import requests
from dashscope import Generation
from http import HTTPStatus
import dashscope
dashscope.api_key = 'sk-348f56558947442388dd8e9ea7974c55'

def generate_differential_diagnosis(medical_record, config_map, get_prompt_fn, model_type='llm_diagnosis'):
    generate_list = []
    prompt, cid, rid = get_prompt_fn(medical_record, config_map)
    
    if not prompt:
        return generate_list, '病历信息无内容'
    
    prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
    record2prompt = get_model_offline_result([str(rid)], model_type, config_map)

    if rid in record2prompt and prompt_hash in record2prompt[rid]:
        for model_name, (generate_text_str, _, _) in record2prompt[rid][prompt_hash].items():
            generate_list = simple_diagnosis_extraction(generate_text_str)
        return generate_list, ''

    generate_text = fetch_preliminary_diagnosis(prompt)
    if generate_text != "":
        uuid = get_uuid()
        xs = [[prompt_hash, prompt, generate_text, uuid, 'Qwen1.5-14B-Chat', cid, rid, '诊断列表', '']]
        insert_model_result(xs, model_type, config_map)
        generate_list = simple_diagnosis_extraction(generate_text)         
    return generate_list, ''

def generate_differential_diagnosis_lst(medical_record, config_map):
    return generate_differential_diagnosis(medical_record, config_map, get_diagnosis_prompt)

def generate_differential_diagnosis_exam(medical_record, config_map):
    return generate_differential_diagnosis(medical_record, config_map, get_exam_prompt)

def extract_patient_info(medical_record):
    """提取性别和年龄相关信息"""
    patientInfo = medical_record.get('patientInfo', {})
    gender_map = {'1': '男', '0': '女'}
    gender = gender_map.get(str(patientInfo.get('gender', -1)), 
                            gender_map.get(str(medical_record.get('gender', -1)), ''))
    age_and_unit = '{}{}'.format(patientInfo.get('age', medical_record.get('age', '')),
                                 patientInfo.get('ageType', medical_record.get('ageType', '')))
    return gender, age_and_unit

def extract_patient_progress(medical_record, attrs, config_map):
    """提取患者的病历进展信息"""
    patient_progress = ''
    main_desc = ''
    progressNoteList = medical_record.get('progressNoteList', [])
    keywords = ["要求住院", "开住院证", "办理住院", "住院治疗", "进一步住院", "主诉:住院", "主诉: 小时"]
    def check_hospitalization_complaint(complaint):
        if any(keyword in str(complaint) for keyword in keywords):
            return True
        else:
            return False
    
    for progressNote in progressNoteList:
        messageList = progressNote.get('messageList', [])
        for message in messageList:
            if '主诉' in message['key']:
                if not message['value'] or check_hospitalization_complaint(message['value']):
                    return '', ''

    for progressNote in progressNoteList:
        messageList = progressNote.get('messageList', [])
        for k in attrs:
            for message in messageList:
                if k in message['key'] and message['value'] != "":
                    if k == "主诉":
                        main_desc = f"{message['key']}:{message['value']}\n"
                    else:
                        patient_progress += '{}:{}\n'.format(message['key'], message['value'])
                    break
    
    if main_desc == "":
        prompt_map = get_prompt_map(1001, progressNoteList, config_map)
        if '主诉' not in prompt_map:
            return '', ''
        for k in attrs:
            if k in prompt_map:
                prompt_input = str(prompt_map[k])
                if prompt_input == "":
                    continue
                if k == '主诉':
                    if not prompt_input or check_hospitalization_complaint(prompt_input):
                        return '', ''
                    main_desc = f'{k}:{prompt_input}\n'
                else:
                    patient_progress += f'{k}:{prompt_input}\n'
    return main_desc, patient_progress

def get_prompt_map(cid, xs, config_map):
    prompt_map = {}
    for x in xs:
        msg_type = x['msgType']
        text = x["progressMessage"]
        progress_type = x["progressType"]
        sps = progress_parse(text, progress_type, msg_type, config_map, cid)
        for i in range(0, len(sps), 2):
            attribute_value = markdown_ft(sps[i + 1])
            if len(attribute_value) == 0 or attribute_value=='-':
                continue
            if sps[i] not in prompt_map:
                prompt_map[sps[i]] = attribute_value
    return prompt_map

def get_diagnosis_prompt(medical_record, config_map):
    serial_number = medical_record.get('serialNumber')
    cid, rid, _ = get_record_id(1001, serial_number, config_map)

    gender, age_and_unit = extract_patient_info(medical_record)
    base_info = '性别:{},年龄:{}。\n'.format(gender, age_and_unit)

    attrs = ['主诉', '现病史', '既往史', '手术史', '体格检查', '专科情况', '辅助检查']
    main_desc, patient_progress = extract_patient_progress(medical_record, attrs, config_map)
    if not main_desc:
        return '', cid, rid

    emergency_record = base_info + main_desc + patient_progress
    prompt = f"""假设你是急诊科医生，具备全面的医学知识和临床经验。请基于提供的患者急诊病历，按照以下步骤给出最精确的诊断列表。

步骤一：根据患者的主诉和其他关键症状（如现病史），推荐所有可能的标准医学诊断，包括但不限于罕见病以及可能危及生命的重要急诊情况。请注意，对于特定诊断需遵循以下医学原则：
- 对于非钻顶样上腹痛的情况，排除胆道蛔虫症的可能性。
- 在没有食用山楂、柿子或黑枣等食物引起腹痛的情况下，不考虑胃石症。
- 若无血压下降或SPO2低于92%的记录，不将感染性休克或感染中毒性休克纳入考量范围。
- 腹痛病例中需评估急性阑尾炎的可能性，但若缺乏典型症状则将其可能性后置。
- 急性上腹痛时应考虑到急性心肌梗死，并结合病历信息调整其可能性。
- 突发剧烈左下腹痛需考虑腹膜后血肿或腹腔血管急性病变（如动脉夹层或破裂）的可能性。
- 慢性腹痛患者需考虑是否存在腹腔或结肠肿瘤。
- 女性患者年龄超过55岁且已无生育能力时，不应考虑妊娠相关疾病。
- 当总胆红素和直接胆红素显著升高时，应考虑梗阻性黄疸的存在，通常由胆道系统物理性梗阻引起，例如胆总管结石。

步骤二：结合主诉之外的信息（例如现病史、既往史、体格检查结果）对初步诊断列表进行细致筛选，排除那些缺乏足够证据支持的诊断。

具体要求如下：
1. 尽量覆盖所有可能的标准医学诊断选项，但确保每个诊断都有相应的临床表现或检查结果作为支撑。注意，诊断名称必须是标准的医学术语，而非基于检验、检查结果提炼的描述性词条。
2. 只关注与当前就诊直接相关的急性问题，忽略基础病或慢性病（除非它们在此刻表现出急性恶化）。
3. 每个列出的诊断都应有充分的临床证据支持，避免推测性诊断。
4. 对于慢性疾病的急性发作或复发，必须有足够的新发症状或检查结果证明。
5. 最终诊断以JSON格式输出，并按可能性排序（无需明确标注可能性大小）。
6. 输出仅限于诊断列表，无需解释。
7. 包括‘危重’字段（是/否），指示该诊断是否属于需要紧急处理的危急重症。
8. 添加‘相关’字段（是/否），表明该诊断是否与主诉中的症状直接相关。
9. 保证诊断名称为标准医学术语，不允许出现诸如“血象升高”、“心电图异常”等描述性词条。

输出格式示例：
[
  {{
      "诊断":"xxx",
      "危重":"xxx",
      "相关":"xxx"
  }},
  ...
]

以下是一个示例：
急诊病历：
主诉:腹痛12小时
现病史:伴恶心,无吐泻,无发热,排成形便
既往史:高血压 糖尿病 12年前肠梗阻手术
体格检查:T:体温°C P:72次/分 R:R次/分 BP:124/88mmHg SPO2:血氧% 神志清,精神可,双肺未闻及干啰音及湿啰音,心律齐,腹软,脐周压痛,无反跳痛,神经系统阴性,双下肢无水肿。

输出结果：
[
  {{
    "诊断": "肠梗阻复发",
    "危重": "是",
    "相关": "是"
  }},
  {{
    "诊断": "肠系膜血管缺血性疾病",
    "危重": "是",
    "相关": "是"
  }},
  {{
    "诊断": "肠道功能紊乱",
    "危重": "否",
    "相关": "是"
  }},
  {{
    "诊断": "腹腔肿瘤",
    "危重": "否",
    "相关": "是"
  }},
  {{
    "诊断": "血管性疾病（如肠系膜动脉栓塞）",
    "危重": "是",
    "相关": "是"
  }},
  {{
    "诊断": "非溃疡性消化不良",
    "危重": "否",
    "相关": "是"
  }},
  {{
    "诊断": "神经性疼痛",
    "危重": "否",
    "相关": "是"
  }}
]

参考以上示例完成新任务。
新任务：
患者急诊病历：
{emergency_record}
输出结果：
"""
    return prompt, cid, rid

def get_exam_prompt(medical_record, config_map):
    serial_number = medical_record.get('serialNumber')
    cid, rid, _ = get_record_id(1001, serial_number, config_map)

    gender, age_and_unit = extract_patient_info(medical_record)
    base_info = '性别:{},年龄:{}。\n'.format(gender, age_and_unit)

    attrs = ['主诉', '既往史', '专科情况', '辅助检查']
    main_desc, patient_progress = extract_patient_progress(medical_record, attrs, config_map)
    if not patient_progress or '辅助检查' not in patient_progress:
        return '', cid, rid

    emergency_record = base_info + main_desc + patient_progress
    prompt = f"""假设你是急诊科医生。请基于患者检验检查异常结果并结合其临床表现给出最正确的诊断列表。

诊断标准及输出格式要求：

1. 所有诊断名称必须使用医学界公认的、标准化的疾病或病理状态名称。检验、检查结果的描述不视为独立诊断，无需列出，如高N末端B型脑钠肽前体、N末端B型脑钠肽前体升高、N端B型利钠肽原升高。
2. 根据提供的检验检查异常指标，明确所有可以得出的确切诊断。
3. 列出的每个诊断都应有足够的临床表现或检查结果作为强烈支持依据，缺少充分临床证据的诊断不予列出。
4. 仅关注本次急诊中需要紧急处理的、危及生命的诊断，排除既往史中的已知诊断、基础性疾病、慢性病以及与本次就诊无关的诊断。
5. 不列出无需临床干预的检验检查结果类诊断，例如：白细胞增多、淋巴细胞减少等。
6. 诊断结果需以JSON格式呈现，并根据可能性从高到低排序（具体可能性数值无需提供）。
7. 输出内容仅限于诊断列表，不需要对诊断进行解释说明。
8. 在诊断列表中，针对每个诊断标明其是否为危急重症（定义：存在明确证据表明病情危重，需立即急诊处理），并使用“是”或“否”表示。
9. 每个诊断还需标明其相关临床表现是否出现在主诉中，标记为“是”或“否”。

注意：请确保所提供的诊断符合上述所有准则，以保证医疗决策的准确性和有效性。

输出结果格式如下：
[
  {{
      "诊断":"xxx",
      "危重":"xxx",
      "相关":"xxx"
  }},
  {{
      "诊断":"xxx",
      "危重":"xxx",
      "相关":"xxx"
  }},
  .....
]

以下是一个示例：
急诊病历：
主诉：胸闷、胸痛
辅助检查：血糖2.1mmol/l，肌钙蛋白 12 
心电图：窦性心律，V1-3 ST弓背抬高

输出结果：
[
  {{
    "诊断": "急性心肌梗死",
    "危重": "是",
    "相关": "是"
  }},
  {{
    "诊断": "低血糖",
    "危重": "是",
    "相关": "否"
  }}
]

参考以上示例完成新任务。
新任务：
患者急诊病历：
{emergency_record}
相关医学知识：
1.肌酐↓不具有诊断意义。
2.病历中没有提供血压下降时不能出感染性休克。
3.双肺纹理增重不能直接出肺部感染，如果没有咳嗽、咳痰、发热等具体的临床表现则不能出肺部感染。
4.D-二聚体水平正常时不能出肺栓塞。
5.对于年龄≥50岁的患者，NT-pro BNP小于450 ng/l属于正常值。
6.高敏肌钙蛋白I升高结合临床表现需考虑急性心肌梗死或急性冠脉综合征的可能性。

输出结果：
"""
    return prompt, cid, rid

def get_diagnosis_basis_one_case_prompt(medical_record, disease, source, config_map):
    serial_number = medical_record.get('serialNumber')
    cid, rid, _ = get_record_id(1001, serial_number, config_map)

    gender, age_and_unit = extract_patient_info(medical_record)
    base_info = '性别:{},年龄:{}。\n'.format(gender, age_and_unit)

    attrs = ['主诉', '现病史', '既往史', '手术史', '体格检查', '专科情况', '辅助检查'] if source == '2' else ['主诉', '既往史', '专科情况', '辅助检查']
    main_desc, patient_progress = extract_patient_progress(medical_record, attrs, config_map)
    if not main_desc or not disease:
        return '', cid, rid

    emergency_record = base_info + main_desc + patient_progress
    prompt = f"""假设你是急诊科医生, 请基于患者急诊病历以及对应的诊断，给出该诊断相关的诊断依据。
要求如下：
1.根据患者已知的病历信息内容对诊断给出相应的解释，解释应依据病历中现有的信息，不要编造患者既往史、检验检查结果等内容。
2.输出结果中只需给出该诊断对应的解释即可，不需要再给出诊断名称，也不需要给出其他可能的诊断以及对应的解释。
3.不需要显示输出‘输出结果：’。

参考以下示例完成新任务。
以下是一个示例：
急诊病历：
主诉:发热伴咳嗽咳痰3天,呼吸困难3小时
现病史:患者于入院前3天无明显诱因出现发热,体温38°C左右,伴咳嗽咳痰,轻微喘息,无畏寒寒战,无恶心、呕吐,无腹痛腹泻,无尿频尿急尿痛,无胸痛肩背部及四肢放射性痛,就诊于当地医院,完善胸CT:双肺多发结节影,建议增强CT,白细胞升高,予对症止喘、化痰治疗。患者喘息逐渐加重,于入院前3小时患者突发呼吸困难,氧饱和度下降,120测指脉氧最低40%左右,予尼可刹米兴奋呼吸、止喘、吸氧治疗。现患者家属为求进一步诊治入我科。
既往史: 高血压 脑出血 : 。
其它:今年3月份因脑出血气切术后,携带金属管入院。
体格检查
T:体温°C P:120次/分 R:30次/分 BP:135/64mmHg SPO2:85%
神志欠清,不能对答,瞳孔左3.0mm,右3.0mm,对光反射存在+;颈软,咽无充血,扁桃体无肿大,结膜无苍白、无充血,巩膜无黄染,口唇无紫绀。胸廓无畸形,右肺呼吸音粗,两肺闻及干啰音,左肺呼吸音粗,两肺闻及干啰音,心音有力,心律整齐,HR120次/分。腹部柔软,无压痛,肝脏肋下未触及,胆囊区无压痛,莫菲氏征阴性,麦氏点压痛未触及,输尿管无点压痛,无反跳痛,肌无紧张,肠鸣音3次/分,能肢体活动,病理征阴性。
其他检查:
外院检查:
表格<诊断>内容:

诊断：
急性呼吸窘迫综合征(ARDS)

输出结果：
患者突发呼吸困难，氧饱和度最低降至40%，伴有发热、咳嗽和咳痰，提示可能的感染。胸CT显示双肺多发结节影，白细胞升高，进一步支持感染的存在。此外，体格检查发现呼吸音改变和心率增快，符合ARDS的临床表现。


新任务：
急诊病历：
{emergency_record}

诊断：
{disease}

输出结果：
"""
    return prompt, cid, rid

def fetch_preliminary_diagnosis(content): 
    message = [
        {
            "role": "user",
            "content": content
        }
    ]
    response = Generation.call(
        model='qwen2.5-14b-instruct',
        messages=message,
        result_format='message',
        top_k=1,
    )
    answer = ""
    if response.status_code == HTTPStatus.OK:
        answer = response.output.choices[0].get("message", {}).get("content", '')
    return answer

async def fetch_preliminary_diagnosis_stream(content, config_map):
    url = f"http://{config_map['llm_diagnosis']['chat_diagnosis_ip']}:{config_map['llm_diagnosis']['chat_diagnosis_port']}/v1/chat/completions"
    data = {
        "model": "/root/Qwen2.5-14B-Instruct",
        "messages": [{"role": "user", "content": content}],
        "temperature": 0.0,
        "n": 1,
        "stream": True
    }

    try:
        with requests.post(url, json=data, stream=True) as response:
            response.raise_for_status()
            for chunk in response.iter_content(chunk_size=1024):
                if chunk and chunk.startswith(b'data:'):
                    chunk = chunk.decode('utf-8')
                    chunks = chunk.split("\n\n")
                    chunk = chunks[len(chunks)-2]
                    json_str = chunk[5:].strip()
                    if '[DONE]' not in json_str:
                        for result in parse_response_json(json_str):
                            yield result
                    else:
                        yield "请求结束。"
    except requests.RequestException as e:
        yield f"请求失败: {e}"

def parse_response_json(json_str):
    try:
        json_data = json.loads(json_str)
        choices = json_data.get('choices', [])
        if choices:
            content = choices[0].get('delta', {}).get('content', '')
            finish_reason = choices[0].get('finish_reason', '')
            if content:
                yield content
            if finish_reason == 'stop' or finish_reason == 'length':
                yield "请求结束。"
    except json.JSONDecodeError:
        print("#########无法解析JSON:*********")
        print(json_str)
        yield ""

def simple_diagnosis_extraction(text, stream=False):
    pattern = r"\[(.+?)\]"
    match = re.search(pattern, text, re.DOTALL)

    diseases_result = []
    if match:
        json_str = '[' + match.group(1) + ']'
        json_str = json_str.replace("'", '"')
        json_str = json_str.replace(" ", "")
        json_str = re.sub(r'"危重":"(是|否|可能)"[^\n]*', r'"危重":"\1",', json_str)
        json_str = re.sub(r'(?<=:)([^"\s][^,}\s]*)', r'"\1"', json_str)
        try:
            diagnoses_data = json.loads(json_str)
            for d in diagnoses_data:
                if d.get("诊断"):
                    diseases_result.append({"disease_name":d["诊断"], "is_critical_disease": d.get("危重", "否"), "has_relation": d.get("相关", "否")})
        except json.JSONDecodeError as e:
            diseases_result = error_diagnosis_extraction(json_str)
    if stream:
        parse_result = []
        for disease in diseases_result:
            serious = "危重" if disease["is_critical_disease"] == "是" else "非危重"
            one_case = f"{disease['disease_name']}：{serious}\n"
            parse_result.append(one_case)
        diseases_result = ''.join(parse_result)
    return diseases_result

def error_diagnosis_extraction(text):
    json_str = ""
    buffer = ""
    inside_diagnosis = False
    diseases_result = []
    for r in text:
        if "[" in r:
            inside_diagnosis = True
        if inside_diagnosis:
            json_str += r
            if '{' in json_str and '}' in json_str:
                start = json_str.find('{')
                end = json_str.find('}') + 1

                buffer = json_str[start:end].strip()
                json_str = json_str[end:].strip()

                if buffer:
                    try:
                        diagnosis = json.loads(buffer)
                        diseases_result.append({"disease_name":diagnosis["诊断"], "is_critical_disease": diagnosis.get("危重", "否"), "has_relation": diagnosis.get("相关", "否")})
                    except json.JSONDecodeError:
                        continue
    return diseases_result
