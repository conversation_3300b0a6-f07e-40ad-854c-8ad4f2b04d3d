# coding:utf-8
from web.web_utils.sql_util import sql_query, select_by_sql
from web.web_utils.db_util import execute_sql_many, execute_sql
from web.web_utils.api_util import post_url
from web.web_utils.text_util import normalize_text, xml2text
from web.mrg_service.cdss_llm_input import get_cdss_progress_input, get_cdss_test_input, get_cdss_medical_order_input, get_cdss_exam_input, get_cdss_progress_and_date, calculate_time, get_cdss_before_admission_input, get_sssj,get_vital_signs_input,prompt_input_process,get_progress_references,get_progress_references_discharge
from bs4 import BeautifulSoup
import hashlib
import os
import json


tree_merge_map = {'1': {'name': '入院记录', 'idx': '1'}, '23': {'name': '入院记录', 'idx': '1'}, '2': {'name': '病程记录', 'idx': '2'}, '3': {'name': '病程记录', 'idx': '2'}, '4': {'name': '病程记录', 'idx': '2'}, '24': {'name': '病程记录', 'idx': '2'}, '25': {'name': '病程记录', 'idx': '2'}, '26': {'name': '病程记录', 'idx': '2'}, '8': {'name': '病程记录', 'idx': '2'}, '31': {'name': '病程记录', 'idx': '2'}, '32': {'name': '病程记录', 'idx': '2'}, '33': {'name': '病程记录', 'idx': '2'}, '35': {'name': '病程记录', 'idx': '2'}, '28': {'name': '病程记录', 'idx': '2'}, '12': {'name': '病程记录', 'idx': '2'}, '43': {'name': '病程记录', 'idx': '2'}, '54': {'name': '病程记录', 'idx': '2'}, '6': {'name': '病程记录', 'idx': '2'}, '5': {'name': '手术记录', 'idx': '3'}, '13': {'name': '手术记录', 'idx': '3'}, '27': {'name': '手术记录', 'idx': '3'}, '21': {'name': '手术记录', 'idx': '3'}, '34': {'name': '手术记录', 'idx': '3'}, '36': {'name': '手术记录', 'idx': '3'}, '49': {'name': '手术记录', 'idx': '3'}, '50': {'name': '手术记录', 'idx': '3'}, '51': {'name': '手术记录', 'idx': '3'}, '52': {'name': '手术记录', 'idx': '3'}, '53': {'name': '手术记录', 'idx': '3'}, '9': {'name': '会诊记录', 'idx': '4'}, '40': {'name': '知情同意书', 'idx': '5'}, '38': {'name': '知情同意书', 'idx': '5'}, '48': {'name': '知情同意书', 'idx': '5'}, '39': {'name': '知情同意书', 'idx': '5'}, '37': {'name': '知情同意书', 'idx': '5'}, '41': {'name': '知情同意书', 'idx': '5'}, '42': {'name': '知情同意书', 'idx': '5'}, '10': {'name': '出院/死亡记录', 'idx': '6'}, '29': {'name': '出院/死亡记录', 'idx': '6'}, '11': {'name': '其它记录', 'idx': '8'}, '2005': {'name': '护理文书', 'idx': '9'}, '2001': {'name': '护理文书', 'idx': '9'}, '2002': {'name': '护理文书', 'idx': '9'}, '2003': {'name': '护理文书', 'idx': '9'}, '46': {'name': '护理文书', 'idx': '9'}, '10011': {'name': '检查报告', 'idx': '10'}, '24001': {'name': '检查报告', 'idx': '10'}}
# type2name = {'1': '入院记录', '2': '病程记录', '3': '手术记录', '4': '会诊记录', '5': '知情同意书', '6': '出院/死亡记录', '8': '其它记录', '9': '护理文书', '10': '检查报告', '88': '病案首页'}
type2name = {'1': {'name':'入院记录','open':True}, '2': {'name':'病程记录','open':False}, '3': {'name':'手术记录','open':False}, '4': {'name':'会诊记录','open':True}, '5': {'name':'知情同意书','open':True}, '6': {'name':'出院/死亡记录','open':True}, '8': {'name':'其它记录','open':False}, '9': {'name':'护理文书','open':True}, '10': {'name':'检查报告','open':True}, '88': {'name':'病案首页','open':True}}


def get_llm_prompt_list(customer_id, department_name, llm_type, config_map):
    sql = '''
            select id, prompt,type,sub_type,sub_type_code,model,remark from llm_prompt where type='{}' and flag=1 order by rank desc;
        		 '''.format(llm_type)
    if config_map['llm_model']['custom_prompt'] == '1' and llm_type == 'llm_cdss':
        sql = '''
                    select ee.id,pp.type,pp.sub_type,pp.sub_type_code,pp.model,pp.remark from llm_prompt_extend ee inner join llm_prompt pp on ee.prompt_id=pp.id
where ee.flag=1 and ee.department_name='{}' and pp.type='{}' and pp.flag=1 order by pp.rank desc;
                		 '''.format(department_name, llm_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    new_xs = []
    for v in xs:
        if v['remark'] == 'need_date':
            need_date = 1
        else:
            need_date = 0
        new_xs.append({'prompt_id':v['id'], 'prompt_type':v['sub_type'],'prompt_type_code':v['sub_type_code'],'need_date':need_date})
    return new_xs


def get_llm_prompt_by_id(prompt_id, config_map, customer_id=1001, custom_prompt_flag=True):
    sql = '''
            select id, prompt,type,sub_type,model,remark from llm_prompt where id={};
        		 '''.format(prompt_id)
    if config_map['llm_model']['custom_prompt'] == '1' and custom_prompt_flag:
        sql = '''
                    select ee.id,ifnull(tt.instruction,pp.prompt) prompt ,pp.type,pp.sub_type,pp.model,pp.remark
                    from llm_prompt_extend ee inner join llm_prompt pp on ee.prompt_id=pp.id
        		    left join label_task tt on ee.task_id=tt.id where ee.id={};
                		 '''.format(prompt_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    prompt_template = ''
    need_date = 0
    for v in xs:
        if v['remark'] == 'need_date':
            need_date = 1
        prompt_template = v['prompt']
    return prompt_template, need_date

def get_record_tree(cid, rid, config_map,cdss_type='mt'):
    sql = '''select pp.id progress_id,pp.progress_type,if(ifnull(pp.progress_type_name,mm.record_name)='',mm.record_name,ifnull(pp.progress_type_name,mm.record_name)) progress_type_name,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format from mt_patient_progress pp
left join ms_system_record_type mm on pp.progress_type=mm.record_type
 where pp.record_id={} and pp.progress_status!=9 order by pp.id;
            '''.format(rid)
    if cdss_type == 'ot':
        sql = '''select pp.id progress_id,pp.progress_type,if(ifnull(pp.progress_type_name,mm.record_name)='',mm.record_name,ifnull(pp.progress_type_name,mm.record_name)) progress_type_name,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format from ot_patient_progress pp
            left join ms_system_record_type mm on pp.progress_type=mm.record_type
             where pp.record_id={} order by pp.id;
                        '''.format(rid)
    xs = sql_query(cid, sql, config_map)
    tree_map = {}
    for v in xs:
        if not v['progress_type_name']:
            continue
        progress_type = str(v['progress_type'])
        if progress_type in tree_merge_map:
            idx = tree_merge_map[progress_type]['idx']
        else:
            idx = '8'
        v['name'] = v['progress_type_name']
        if idx not in tree_map:
            tree_map[idx] = []
        tree_map[idx].append(v)
    tree_list = []
    for k, v in sorted(tree_map.items()):
        tree_list.append({'type':int(k),'name':type2name[k]['name'],'open':type2name[k]['open'],'children':v})
    return tree_list

def get_input_by_progress_id(cid, progress_id_list, config_map, input_max_len,cdss_type='mt'):
    sql = '''select pp.id progress_id,pp.progress_type,if(ifnull(pp.progress_type_name,mm.record_name)='',mm.record_name,ifnull(pp.progress_type_name,mm.record_name)) progress_type_name,pp.progress_message progress_text,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format,ifnull(ee.msg_type,0) msg_type from mt_patient_progress pp
    left join mt_patient_progress_extend ee on pp.id=ee.progress_id
    left join ms_system_record_type mm on pp.progress_type=mm.record_type
    where pp.id in ({}) order by pp.id desc;
            '''.format(','.join([str(i) for i in progress_id_list]))
    if cdss_type=='ot':
        sql = ''' select pp.id progress_id,pp.progress_type,if(ifnull(pp.progress_type_name,mm.record_name)='',mm.record_name,ifnull(pp.progress_type_name,mm.record_name)) progress_type_name,pp.progress_message progress_text,date_format(pp.record_time_format,'%Y-%m-%d %H:%i:%s') record_time_format,pp.msg_type from ot_patient_progress pp
                left join ms_system_record_type mm on pp.progress_type=mm.record_type
                where pp.id in ({}) order by pp.id desc;
                        '''.format(','.join([str(i) for i in progress_id_list]))

    xs = sql_query(cid, sql, config_map)
    prompt_input = ''
    progress_list = []
    for v in xs:
        if not v['progress_text'] or not v['progress_type_name']:
            continue
        msg_type = v['msg_type']
        if v['msg_type'] == 1:
            progress_text = xml2text(v['progress_text'], v['progress_type'], msg_type, config_map, cid)
            msg_type = 0
        else:
            soup = BeautifulSoup(v['progress_text'], 'html.parser')
            progress_text = soup.get_text()
        if len(prompt_input)+len(progress_text) < input_max_len:
            prompt_input += prompt_input_process(progress_text, len(progress_list))
            progress_list.append({'progress_id': v['progress_id'], 'progress_type': v['progress_type'], 'progress_type_name': v['progress_type_name'], 'msg_type': msg_type, 'record_time_format': str(v['record_time_format'])})
        else:
            break
    return prompt_input, progress_list


def get_input_by_prompt_type(cid, rid, prompt_type_code, record_date, config_map, max_length=30000):
    prompt_input = ''
    progress_list = []
    progress_type_in = []
    progress_type_not_in = []
    start_date = ''
    end_date = ''
    need_more_data = True
    if prompt_type_code in ['llm_cdss_fxbq', 'llm_cdss_gen_jdxj', 'llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_cyjl', 'llm_cdss_gen_ssjl']:
        if prompt_type_code == 'llm_cdss_gen_jdxj':
            start_date = calculate_time(record_date, -30)
            end_date = record_date
            progress_type_not_in = [10]
        elif prompt_type_code in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_ssjl']:
            end_date = record_date
        elif prompt_type_code in ['llm_cdss_gen_sqxj']:
            end_date = get_sssj(cid, rid, config_map)
            if end_date:
                prompt_input += '手术时间：{}\n'.format(end_date)
        elif prompt_type_code == 'llm_cdss_gen_cyjl':
            progress_type_not_in = [10]
        prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in,  config_map)
    elif prompt_type_code in ['llm_cdss_gen_ryjl', 'llm_cdss_gen_scbc']:
        if prompt_type_code == 'llm_cdss_gen_scbc':
            progress_type_in = [1]
            prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in,  config_map)
        if not prompt_input or prompt_type_code == 'llm_cdss_gen_ryjl':
            prompt_input, progress_list = get_cdss_before_admission_input(prompt_input, progress_list, cid, rid, max_length, config_map)
        need_more_data = False
    elif prompt_type_code in ['llm_cdss_gen_cfjl_1','llm_cdss_gen_cfjl_zz','llm_cdss_gen_cfjl_zr']:
        start_date = calculate_time(record_date, -3)
        end_date = record_date
        progress_type_not_in = [1, 2, 10]
        prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in, config_map)
        progress_type_in = [1, 2]
        progress_type_not_in = []
        start_date = ''
        # end_date = ''
        prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in, config_map)
        prompt_input, progress_list = get_vital_signs_input(prompt_input, progress_list, cid, rid, start_date, end_date,  config_map)
    elif prompt_type_code in ['llm_cdss_gen_cfjl_2']:
        start_date = get_sssj(cid, rid, config_map)
        end_date = record_date
        progress_type_not_in = [10]
        prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in, config_map)
        prompt_input, progress_list = get_vital_signs_input(prompt_input, progress_list, cid, rid, start_date, end_date,  config_map)
    elif prompt_type_code in ['llm_cdss_jbzd']:
        progress_type_in = [1]
        prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in,  config_map)
        if not prompt_input:
            progress_type_in = [2]
            prompt_input, progress_list = get_cdss_progress_input(prompt_input, progress_list, cid, rid, start_date, end_date, progress_type_in, progress_type_not_in,  config_map)
        need_more_data = False

    if need_more_data:
        if len(prompt_input) < max_length:
            prompt_input, progress_list = get_cdss_medical_order_input(prompt_input, progress_list, cid, rid, start_date, end_date, [],  config_map)
        if len(prompt_input) < max_length:
            prompt_input, progress_list = get_cdss_exam_input(prompt_input, progress_list, cid, rid, start_date, end_date,  config_map)
        if len(prompt_input) < max_length:
            prompt_input, progress_list = get_cdss_test_input(prompt_input, progress_list, cid, rid, start_date, end_date,  config_map)

    return prompt_input, progress_list


def get_cdss_chat_history(dialogue_code, config_map,max_history_len=30000):
    sql = '''
                select dialogue_code,content,ai_content,chat_model,create_date from llm_dialogue_content where dialogue_code='{}' ORDER BY create_date desc
        		 '''.format(dialogue_code)
    result = select_by_sql(sql, 'hm_llm', config_map)
    history_list = []
    history_len = 0
    for i, v in enumerate(result):
        content = v['content'] if v['content'] else ''
        ai_content = v['ai_content'] if v['ai_content'] else ''
        if not ai_content:
            continue
        history_len += len(ai_content)
        if ai_content and history_len < max_history_len:
            history_list.append({"role": "assistant", "content": ai_content})
        history_len += len(content)
        if content and history_len < max_history_len:
            history_list.append({"role": "user", "content": content})

    history_list = history_list[::-1]
    return history_list


def save_dialogue_content(dialogue_param, config_map):
    prompt_hash = str(hashlib.sha1(normalize_text(dialogue_param['prompt']).encode('utf-8')).hexdigest())
    doc_references_str = json.dumps(dialogue_param['doc_references'], ensure_ascii=False)
    chat_param_str = json.dumps(dialogue_param['chat_param'], ensure_ascii=False)
    sql = '''
                        INSERT INTO llm_dialogue_content (dialogue_code,stream_code,user_code,user_name,department_code,department_name,content,content_hash, prompt_type, prompt_type_code,ai_content,chat_model,customer_id,record_id,doc_references,chat_param,agent_id,agent_name,agent_code) VALUES  (%s,%s, %s,%s, %s,%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);
                		 '''
    xs = [[dialogue_param['dialogue_code'],dialogue_param['stream_code'],dialogue_param['user_code'],dialogue_param['user_name'],dialogue_param['department_code'],dialogue_param['department_name'],dialogue_param['prompt'],prompt_hash,dialogue_param['prompt_type'],dialogue_param['prompt_type_code'],dialogue_param['ai_content'],dialogue_param['chat_model'],dialogue_param['customer_id'],dialogue_param['record_id'],doc_references_str,chat_param_str,dialogue_param.get('agent_id',-1),dialogue_param.get('agent_name',''),dialogue_param.get('agent_code','')]]
    ret = execute_sql_many(sql, xs, 'hm_llm', config_map)
    # prompt = xs[0][3]
    # prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
    # xs[0].append(prompt_hash)
    # sql = '''
    #                 INSERT INTO llm_dialogue_content (dialogue_code,stream_code,user_code,content, prompt_type, prompt_type_code,ai_content,chat_model,customer_id,record_id,doc_references,chat_param,content_hash) VALUES  (%s,%s, %s,%s, %s,%s, %s, %s, %s, %s, %s, %s, %s);
    #         		 '''
    # ret = execute_sql_many(sql, xs, 'hm_llm', config_map)
    return ret


def update_dialogue_content(dialogue_update_param, config_map):
    sql = '''update llm_dialogue_content set flag=%s '''
    values = [dialogue_update_param['flag']]
    if 'content' in dialogue_update_param:
        prompt_hash = str(hashlib.sha1(normalize_text(dialogue_update_param['content']).encode('utf-8')).hexdigest())
        sql += ' , content = %s , content_hash = %s'
        values.extend([dialogue_update_param['content'], prompt_hash])
    if 'ai_content' in dialogue_update_param:
        sql += ' , ai_content = %s'
        values.append(dialogue_update_param['ai_content'])
    if 'chat_model' in dialogue_update_param:
        sql += ' , chat_model = %s'
        values.append(dialogue_update_param['chat_model'])
    if 'doc_references' in dialogue_update_param:
        sql += ' , doc_references = %s'
        doc_references_str = json.dumps(dialogue_update_param['doc_references'], ensure_ascii=False)
        values.append(doc_references_str)
    if 'chat_param' in dialogue_update_param:
        sql += ' , chat_param = %s'
        chat_param_str = json.dumps(dialogue_update_param['chat_param'], ensure_ascii=False)
        values.append(chat_param_str)
    if 'images' in dialogue_update_param:
        sql += ' , images = %s'
        images_str = json.dumps(dialogue_update_param['images'], ensure_ascii=False)
        values.append(images_str)
    if 'conversation_id' in dialogue_update_param:
        sql += ' , conversation_id = %s'
        values.append(dialogue_update_param['conversation_id'])
    sql += ' where record_id=%s and stream_code=%s;'
    values.extend([dialogue_update_param['record_id'], dialogue_update_param['stream_code']])
    ret = execute_sql(sql, 'hm_llm', config_map, values=values)
    return ret
# def update_dialogue_content2(record_id, stream_code, ai_content, doc_references, config_map):
#     values = (ai_content, record_id, stream_code)
#     sql = '''update llm_dialogue_content set ai_content=%s '''
#     if doc_references:
#         sql += ' , doc_references = %s'
#         values = (ai_content, str(doc_references), record_id, stream_code)
#     sql += ' where record_id=%s and stream_code=%s;'
#     ret = execute_sql(sql, 'hm_llm', config_map, values=values)
#     return ret


def update_dialogue_feedback(request_data, config_map):
    customer_id = request_data.get("customer_id", 1001)
    record_id = request_data.get("record_id")
    dialogue_code = request_data.get("dialogue_code")
    stream_code = request_data.get("stream_code")
    tags = request_data.get("tags", 0)
    feedback = request_data.get("feedback", '')
    ret = -1
    if tags != 0:
        set_str = 'tags={}'.format(tags)
        if tags == 3:
            set_str = 'copy_flag=1'
        elif tags == 4:
            set_str = 'backfill_flag=1'
        sql = '''
                            update llm_dialogue_content set {} where record_id={} and dialogue_code='{}' and stream_code='{}';
                             '''.format(set_str, record_id, dialogue_code, stream_code)
        ret = execute_sql(sql, 'hm_llm', config_map)
    elif feedback:
        sql = '''
                                    update llm_dialogue_content set feedback=%s where record_id=%s and dialogue_code=%s and stream_code=%s;
                                     '''
        values = (feedback, record_id, dialogue_code, stream_code)
        ret = execute_sql(sql, 'hm_llm', config_map, values)
    return ret


def get_llm_qc_stream_result(prompt_type, prompt_type_code, stream_code, dialogue_code, user_code,user_name,department_code,department_name,customer_id,record_id,config_map):
    json_obj = {"customer_id":customer_id, "record_id":record_id, "stream_code": stream_code}
    qc_url = 'http://{}/RuleEngineZhikong/llm/result_stream'.format(config_map['llm_api']['llm_qc_stream_ip'])
    return_json = post_url(qc_url, json_obj)
    if 'body' in return_json and 'dialogue_end_flag' in return_json['body'] and return_json['body']['dialogue_end_flag']:
        # xs = [[dialogue_code, stream_code, user_code, prompt_type, prompt_type, prompt_type_code, return_json['body']['content'], 'qc_api',customer_id, record_id,'','']]
        dialogue_param = {'dialogue_code':dialogue_code,
                            'stream_code':stream_code,
                            'user_code':user_code,
                            'user_name':user_name,
                            'department_code':department_code,
                            'department_name':department_name,
                            'prompt':prompt_type,
                            'prompt_type':prompt_type,
                            'prompt_type_code':prompt_type_code,
                            'ai_content':return_json['body']['content'],
                            'chat_model':'qc_api',
                            'customer_id':customer_id,
                            'record_id':record_id,
                            'doc_references':[],
                            'chat_param':{}}
        save_dialogue_content(dialogue_param, config_map)
    return return_json


def get_dialogue_content(record_id, config_map):
    sql = '''
            select stream_code,content_hash,ai_content,record_id from llm_dialogue_content where record_id={} and (tags is null or tags!=2) and flag=1;
    		 '''.format(record_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    record2prompt = {}
    for v in xs:
        record_id = v['record_id']
        prompt_hash = v['content_hash']
        ai_content = v['ai_content']
        stream_code = v['stream_code']
        if not record_id or not prompt_hash or not ai_content or not stream_code:
            continue
        if record_id not in record2prompt:
            record2prompt[record_id] = {}
        if prompt_hash not in record2prompt[record_id]:
            record2prompt[record_id][prompt_hash] = {}
        record2prompt[record_id][prompt_hash] = (stream_code, ai_content)
    return record2prompt


def get_path_by_name(cid, doc_name, config_map):
    sql = '''
SELECT pp.path,pp.name FROM (
SELECT id,path,name FROM files
union all
SELECT id,path,name FROM pubmed_files )pp  WHERE pp.path like '%{}%' order by pp.id desc limit 1;
            '''.format(doc_name)
    xs = sql_query(10001, sql, config_map)
    if len(xs) > 0:
        basename, extension = os.path.splitext(xs[0]['name'])
        return basename, xs[0]['path']
    return doc_name, None


def get_dialogue_list(request_data, config_map):
    customer_id = request_data.get("customer_id", 1001)
    record_id = request_data.get("record_id")
    sql = '''select w.* FROM
        (SELECT
    	cc.*
    FROM
    	gpt_agent_config_customer cc
    WHERE
    	cc.`status` = 2 and cc.flag=1
    	and cc.customer_id in (-1,{}) 

    UNION ALL
    SELECT
    	cc.*
    FROM
    	gpt_agent_config cc
    WHERE
    	cc.`status` = 2 and cc.flag=1
    	and cc.customer_id in (-1,{}) )w order by w.modify_date desc'''.format(customer_id, customer_id)
    xs = sql_query(customer_id, sql, config_map)
    code2agent = {}
    for i, x in enumerate(xs):
        code2agent[x['code']] = x
    sql = '''
                select id,dialogue_code,prompt_type,prompt_type_code,content,create_date,chat_param,agent_id,agent_name,agent_code from llm_dialogue_content 
                where record_id={}
                group by dialogue_code
                ORDER BY create_date desc limit 20
        		 '''.format(record_id)
    result = select_by_sql(sql, 'hm_llm', config_map)
    dialogue_list = []
    for i, v in enumerate(result):
        dialogue_map = {}
        dialogue_map['dialogue_code'] = v['dialogue_code']
        dialogue_title = v['content']
        if v['prompt_type'] and v['prompt_type_code']:
            dialogue_title = v['prompt_type']
            if v['prompt_type_code'].startswith('llm_rag'):
                dialogue_title = '@{}，{}'.format(v['prompt_type'], v['content'])
            elif v['prompt_type_code'] in ['llm_chat', 'llm_medical_chat']:
                if v['chat_param'] and len(v['chat_param']) > 5:
                    chat_param = eval(v['chat_param'].replace('null', 'None'))
                    dialogue_title = chat_param.get('prompt', '')
        if v['agent_code'] and v['agent_name']:
            dialogue_title = '{}：{}'.format(v['agent_name'], v['content'])
            if v['agent_code'] in code2agent:
                dialogue_map['agent_icon'] = code2agent[v['agent_code']]['logo_url']
        else:
            if v['chat_param'] and len(v['chat_param']) > 5:
                chat_param = eval(v['chat_param'].replace('null', 'None'))
                dialogue_title = chat_param.get('prompt', '')
        dialogue_map['dialogue_title'] = dialogue_title
        dialogue_map['dialogue_time'] = str(v['create_date'])[5:16]
        dialogue_list.append(dialogue_map)
    return dialogue_list


def get_dialogue_content_list(request_data, config_map):
    customer_id = request_data.get("customer_id", 1001)
    record_id = request_data.get("record_id")
    dialogue_code = request_data.get("dialogue_code")
    # 0 没有使用到病历， 1 基于病历问答
    dialogue_medical_record = 0
    sql = '''
                    select dialogue_code,stream_code,content,ai_content,prompt_type,prompt_type_code,create_date,doc_references,chat_param,flag,agent_id,agent_name,agent_code,images,conversation_id from llm_dialogue_content where record_id={} and dialogue_code='{}' ORDER BY create_date
            		 '''.format(record_id, dialogue_code)
    result = select_by_sql(sql, 'hm_llm', config_map)
    dialogue_content_list = []
    for i, v in enumerate(result):
        dialogue_content_map = {}
        content = v['content'] if v['content'] else v['agent_name']
        ai_content = v['ai_content'] if v['ai_content'] else ''
        if not ai_content:
            continue
        if i == 0 and ((v['prompt_type_code'] and v['prompt_type_code'] not in ['llm_chat']) or v['agent_code']):
            chat_param = {}
            if v['chat_param'] and len(v['chat_param']) > 5:
                chat_param = eval(v['chat_param'].replace('null', 'None'))
            progress_references = []
            if 'progress_list' in chat_param:
                progress_references = get_progress_references(customer_id, record_id, content, chat_param['progress_list'],config_map)
            elif 'discharge' in chat_param:
                discharge_uuid = chat_param['discharge'].get('discharge_uuid','')
                real_idx_map = chat_param['discharge']['real_idx_map']
                if discharge_uuid and real_idx_map:
                    prompt_progress_map = {}
                    discharge_sql = '''
                                        select prompt,record_id,answer_type,remark from llm_discharge_model where record_id={} and uuid='{}'
                                         '''.format(record_id, discharge_uuid)
                    discharge_result = select_by_sql(discharge_sql, 'hm_llm', config_map)
                    for dr in discharge_result:
                        if dr['remark'] and len(dr['remark']) > 5:
                            progress_list = eval(dr['remark'].replace('null', 'None'))
                            prompt_progress_map[dr['answer_type']] = {'prompt': dr['prompt'], 'progress_list': progress_list}
                    progress_references = get_progress_references_discharge(customer_id, record_id, prompt_progress_map, real_idx_map, config_map)
            if progress_references:
                dialogue_content_map['progress_references'] = progress_references
            record_date = chat_param.get('record_date', '')
            if v['prompt_type'] and v['prompt_type_code']:
                content = v['prompt_type']
                if v['prompt_type_code'].startswith('llm_rag'):
                    content = '@{}，{}'.format(v['prompt_type'], v['content'])
                else:
                    if record_date:
                        content = '<span class="mar5">日期：{}</span> {}'.format(record_date, v['prompt_type'])
                if v['prompt_type_code'] in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_ssjl']:
                    dialogue_content_list.append({"content":content,"ai_content":'请输入拟采用的麻醉方式及术式'})
                    content = chat_param.get('prompt', '')
                elif v['prompt_type_code'] in ['llm_medical_chat']:
                    if 'progress_list' in chat_param and len(chat_param['progress_list']) > 0:
                        progress_list_str = '\n'.join(['{} {}'.format(p['progress_type_name'], p['record_time_format']) for p in chat_param['progress_list']])
                        dialogue_content_list.append({"content": '', "ai_content": progress_list_str})
                        content = chat_param.get('prompt', '')
            # if v['agent_code']:
            #     content = '{}：{}'.format(v['agent_name'], chat_param.get('prompt', ''))
        if v['conversation_id']:
            dialogue_content_map['conversation_id'] = v['conversation_id']
        dialogue_content_map['stream_code'] = v['stream_code']
        dialogue_content_map['content'] = content
        dialogue_content_map['ai_content'] = ai_content
        dialogue_content_map['dialogue_stop'] = 0
        if v['flag'] == 3 or v['flag'] == 4:
            # 3：手动停止，4：被动停止
            dialogue_content_map['dialogue_stop'] = 1
        dialogue_content_map['dialogue_time'] = str(v['create_date'])
        doc_references = v['doc_references']
        if doc_references and len(doc_references) > 5:
            doc_references = doc_references.replace('null', 'None')
            dialogue_content_map['doc_references'] = eval(doc_references)
        images = v['images']
        if images and len(images) > 5:
            images = images.replace('null', 'None')
            dialogue_content_map['images'] = eval(images)
        dialogue_content_list.append(dialogue_content_map)
        if i == 0 and v['prompt_type_code'] and v['prompt_type_code'] not in ['llm_chat'] and not v['prompt_type_code'].startswith('llm_rag'):
            dialogue_medical_record = 1
    return dialogue_content_list, dialogue_medical_record
