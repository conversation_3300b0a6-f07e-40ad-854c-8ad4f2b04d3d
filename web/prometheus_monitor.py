# encoding: utf-8
from prometheus_client import Counter, Gauge, Summary,Histogram, generate_latest
from prometheus_client.core import CollectorRegistry
from prometheus_client.exposition import choose_encoder

global g_monitor
QUALITY_CONTROL="quality_control_stream"
QUALITY_CONTROL_FAIL="quality_control_fail"
QUALITY_CONTROL_BATCH="quality_control_batch"
CASE_GENERATE="case_generate"
CASE_GENERATE_FAIL="case_generate_fail"


class Monitor:
    def __init__(self):
        # 注册收集器&最大耗时map
        self.collector_registry = CollectorRegistry(auto_describe=False)
        self.request_time_max_map = {}
    
        # 基于业务请求大模型的qps统计
        self.http_model_request_count = Counter(name="model_request",
                                          documentation="model request qps",
                                          labelnames=["product"],
                                          registry=self.collector_registry)

        # 基于业务请求大模型的失败请求
        self.http_model_request_fail_count = Counter(name="model_request_fail",
                                          documentation="model request fail",
                                          labelnames=["product"],
                                          registry=self.collector_registry)


        # 基于业务请求大模型的耗时直方图
        self.model_time_historgram = Histogram(name="model_resp_time", 
                                    documentation="model response time",
                                    labelnames=["product"],
                                    buckets=[1.0, 2.0, 4.0, 7.0, 10.0, 15.0, 20.0],
                                    registry=self.collector_registry)
    

        # 基于业务请求大模型的请求长度统计和返回统计
        self.model_length_historgram = Histogram(name="model_length",
                                    documentation= "model length",
                                    labelnames=["product", "req_resp"],
                                    buckets=[32, 64, 128, 256, 512, 1024, 2048, 4096, 8192],
                                    registry=self.collector_registry)
    
    
        # 大模型业务最大耗时统计
        self.model_request_time_max_guage = Gauge(name="model_max_request_time",
                                            documentation="the max time query model",
                                            labelnames=["product"],
                                            registry=self.collector_registry)
    
        # Agent失败数
        self.agent_fail_status_counter = Counter(name = "aigc_hospital_agent_status",
                                            documentation="the dify agent status",
                                            labelnames=["agent_name", "dify_id", "code"],
                                            registry=self.collector_registry)

        # Agent总数
        self.agent_count = Counter(name="aigc_hospital_agent_total",
                                          documentation="agent request qps",
                                          labelnames=["agent_name", "dify_id"],
                                          registry=self.collector_registry)


    # 获取/metrics结果
    def get_prometheus_metrics_info(self):
        self.reset_request_time_max_map()
        return generate_latest(self.collector_registry)

    # def get_prometheus_metrics_info(self, handler):
    #     encoder, content_type = choose_encoder(handler.request.headers.get('accept'))
    #     handler.set_header("Content-Type", content_type)
    #     handler.write(encoder(self.collector_registry))
    #     self.reset_request_time_max_map()


    # 大模型qps统计
    def set_prometheus_qps_count(self,  product_value):
        self.http_model_request_count.labels(product=product_value).inc()

    # 大模型失败统计
    def set_prometheus_fail_count(self,  product_value):
        self.http_model_request_fail_count.labels(product=product_value).inc()

    # 大模型耗时直方图统计
    def set_model_time_historgram(self, product_value, time):
        self.model_time_historgram.labels(product=product_value).observe(time)
        if product_value not in self.request_time_max_map or self.request_time_max_map.get(product_value, 0) < time:
            self.request_time_max_map[product_value] = time
            self.model_request_time_max_guage.labels(product=product_value).set(time)

    # 大模型请求返回长度直方图统计
    def set_model_length_summary(self, product_value, requestLength, respLength):
        self.model_length_historgram.labels(product=product_value, req_resp="req").observe(requestLength)
        self.model_length_historgram.labels(product=product_value, req_resp="resp").observe(respLength)

    # Agent失败数
    def set_agent_fail_gauage(self, agent_name, dify_id, code):
        self.agent_fail_status_counter.labels(agent_name=agent_name, dify_id=dify_id, code=code).inc()

    # Agent总数
    def set_agent_count(self, agent_name, dify_id):
        self.agent_count.labels(agent_name=agent_name, dify_id=dify_id).inc()

    # # 接口调用summary统计
    # self.http_request_summary = Summary(name="http_server_requests_seconds",
    #                                documentation="Num of request time summary",
    #                                labelnames=("method", "code", "uri"),
    #                                registry=self.collector_registry)
    # # 接口最大耗时统计
    # self.http_request_max_cost = Gauge(name="http_server_requests_seconds_max",
    #                               documentation="Number of request max cost",
    #                               labelnames=("method", "code", "uri"),
    #                               registry=self.collector_registry)

    # # 请求失败次数统计
    # self.http_request_fail_count = Counter(name="http_server_requests_error",
    #                                  documentation="Times of request fail in total",
    #                                  labelnames=("method", "code", "uri"),
    #                                  registry=self.collector_registry)

    
    # # 模型预测耗时统计
    # self.http_request_predict_cost = Counter(name="http_server_requests_seconds_predict",
    #                                     documentation="Seconds of prediction cost in total",
    #                                     labelnames=("method", "code", "uri"),
    #                                     registry=self.collector_registry)
    # # 图片下载耗时统计
    # self.http_request_download_cost = Counter(name="http_server_requests_seconds_download",
    #                                      documentation="Seconds of download cost in total",
    #                                      labelnames=("method", "code", "uri"),
    #                                      registry=self.collector_registry)

    # # summary统计
    # def set_prometheus_request_summary(self, handler):
    #     self.http_request_summary.labels(handler.request.method, handler.get_status(), handler.request.path).observe(handler.request.request_time())
    #     self.set_prometheus_request_max_cost(handler)

    # # 自定义summary统计
    # def set_prometheus_request_summary_customize(self, method, status, path, cost_time):
    #     self.http_request_summary.labels(method, status, path).observe(cost_time)
    #     self.set_prometheus_request_max_cost_customize(method, status, path, cost_time)

    # # 失败统计
    # def set_prometheus_request_fail_count(self, handler, amount=1.0):
    #     self.http_request_fail_count.labels(handler.request.method, handler.get_status(), handler.request.path).inc(amount)

    # # 自定义失败统计
    # def set_prometheus_request_fail_count_customize(self, method, status, path, amount=1.0):
    #     self.http_request_fail_count.labels(method, status, path).inc(amount)

    # # 最大耗时统计
    # def set_prometheus_request_max_cost(self, handler):
    #     requset_cost = handler.request.request_time()
    #     if self.check_request_time_max_map(handler.request.path, requset_cost):
    #         self.http_request_max_cost.labels(handler.request.method, handler.get_status(), handler.request.path).set(requset_cost)
    #         self.request_time_max_map[handler.request.path] = requset_cost

    # # 自定义最大耗时统计
    # def set_prometheus_request_max_cost_customize(self, method, status, path, cost_time):
    #     if self.check_request_time_max_map(path, cost_time):
    #         self.http_request_max_cost.labels(method, status, path).set(cost_time)
    #         self.request_time_max_map[path] = cost_time

    # # 预测耗时统计
    # def set_prometheus_request_predict_cost(self, handler, amount=1.0):
    #     self.http_request_predict_cost.labels(handler.request.method, handler.get_status(), handler.request.path).inc(amount)

    # # 自定义预测耗时统计
    # def set_prometheus_request_predict_cost_customize(self, method, status, path, cost_time):
    #     self.http_request_predict_cost.labels(method, status, path).inc(cost_time)

    # # 下载耗时统计
    # def set_prometheus_request_download_cost(self, handler, amount=1.0):
    #     self.http_request_download_cost.labels(handler.request.method, handler.get_status(), handler.request.path).inc(amount)

    # # 自定义下载耗时统计
    # def set_prometheus_request_download_cost_customize(self, method, status, path, cost_time):
    #     self.http_request_download_cost.labels(method, status, path).inc(cost_time)

    # 校验是否赋值最大耗时map
    # def check_request_time_max_map(self, uri, cost):
    #     if uri not in self.request_time_max_map:
    #         return True
    #     if self.request_time_max_map[uri] < cost:
    #         return True
    #     return False

    # 重置最大耗时map
    def reset_request_time_max_map(self):
        for key in self.request_time_max_map:
            self.request_time_max_map[key] = 0.0

g_monitor = Monitor()
