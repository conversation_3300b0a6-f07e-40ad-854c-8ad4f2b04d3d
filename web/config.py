# coding:utf-8
import os
import configparser
import dashscope
from flask import Blueprint, request, jsonify, Response
import openai
from dashscope import Generation
from http import HTTPStatus
from openai import OpenAI, AzureOpenAI

proDir = os.path.split(os.path.realpath(__file__))[0]
# 在当前文件路径下查找.ini文件
configPath = os.path.join(proDir, 'config.ini')
con = configparser.RawConfigParser()
con.read(configPath, encoding='utf-8')
sections = con.sections()
config_map = {}
for sec in sections:
    items = dict(con.items(sec))
    config_map[sec] = items
#如果是公司环境加载config_company.ini
if config_map['app']['is_hospital'] == '0':
    configPath = os.path.join(proDir, 'config_company.ini')
    con.read(configPath, encoding='utf-8')
    sections = con.sections()
    config_map = {}
    for sec in sections:
        items = dict(con.items(sec))
        config_map[sec] = items
dashscope.api_key = 'sk-348f56558947442388dd8e9ea7974c55'
# 用于控制日志打印
# dbg 0 表示不打印日志
# dbg 1 表示打印日志
config_map['dbg'] = '0'  # 设置默认值
config_map['customer_id'] = 1001
