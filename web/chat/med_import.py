import json
import queue
import re
import os
import socket
import threading
import time
import urllib
import uuid
from http import HTTPStatus
import dashscope
import requests

from .ocr import ocr_config
from alibabacloud_ocr_api20210707.client import Client as ocr_api20210707Client
from alibabacloud_darabonba_stream.client import Client as StreamClient
from alibabacloud_ocr_api20210707 import models as ocr_api_20210707_models
from alibabacloud_tea_util import models as util_models
from loguru import logger


from ..config import config_map
from ..web_utils.db_util import select_by_sql, execute_sql


TASK_DB_NAME = 'hm_llm'
IMPORT_TASK_RUNNER_NUM = 0
MEDICAL_ORDER_DB = "hmcdss2"
# is_company = str(config_map["app"].get("is_hospital", 0)) == '0'
is_company = False
if socket.gethostname() == 'LAPTOP-0ORON6M1':
    import json5
    import fitz
    from PIL import Image
    # 王杰的开发机，用测试db里的llm_task表
    # 只开一个线程
    TASK_DB_NAME = 'eval_data'
    IMPORT_TASK_RUNNER_NUM = 1
elif socket.gethostname() == 'izbp153sz6abkpbsl0cpnmz':
    import json5
    import fitz
    from PIL import Image
    # 测试服务器，开10个线程，用测试库
    IMPORT_TASK_RUNNER_NUM = 10
    TASK_DB_NAME = 'eval_data'
    MEDICAL_ORDER_DB = "hmcdss2_3_71"
elif socket.gethostname() == 'hm-gpt' or is_company:
    import json5
    import fitz
    from PIL import Image
    # 正式服务器，开10个线程，用正式库
    IMPORT_TASK_RUNNER_NUM = 10
    TASK_DB_NAME = 'hm_llm'
else:
    # 其他人的机器，不开线程
    IMPORT_TASK_RUNNER_NUM = 0
    TASK_DB_NAME = 'eval_data'
MEDICAL_ORDER_MAPPING_SERVICE = os.environ.get('MEDICAL_ORDER_MAPPING_SERVICE')
PDF_STORAGE_PATH = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'pdf_path')
PDF_IMG_PATH = os.path.join(PDF_STORAGE_PATH, "img")
if not os.path.exists(PDF_IMG_PATH):
    os.makedirs(PDF_IMG_PATH, exist_ok=True)


def singleton(cls, *args, **kwargs):
    instances = {}

    def _singleton():
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return _singleton


class RWLock:
    def __init__(self):
        self.lock = threading.Lock()
        self.cnt = 0
        self.extra = threading.Lock()

    def read_acquire(self):
        with self.extra:
            self.cnt += 1
            if self.cnt == 1:
                self.lock.acquire()

    def read_release(self):
        with self.extra:
            self.cnt -= 1
            if self.cnt == 0:
                self.lock.release()

    def write_acquire(self):
        self.lock.acquire()

    def write_release(self):
        self.lock.release()


def load_prompt(prompt_type):
    sql = '''select prompt, type, sub_type, sub_type_code from llm_prompt where type='{}' and flag=1'''.format(prompt_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    return {x['sub_type_code']: x['prompt'] for x in xs}


@singleton
class OcrThenQwen(object):

    def __init__(self):
        self.rwlock = RWLock()
        self.prompts = {}
        self.refresh_config()

    def refresh_config(self):
        self.rwlock.write_acquire()
        try:
            self.prompts = load_prompt("llm_import_ocrqwen")
        except Exception as e:
            return "ocrqwen刷新失败:"+str(e)
        finally:
            self.rwlock.write_release()
        return None

    @classmethod
    def ocr(cls, image_path):
        client = ocr_api20210707Client(ocr_config)
        body_stream = StreamClient.read_from_file_path(image_path)
        advanced_config = ocr_api_20210707_models.RecognizeAllTextRequestAdvancedConfig(
            output_paragraph=True
        )
        recognize_all_text_request = ocr_api_20210707_models.RecognizeAllTextRequest(
            body=body_stream,
            type='Advanced',
            advanced_config=advanced_config
        )
        runtime = util_models.RuntimeOptions()
        try:
            response = client.recognize_all_text_with_options(recognize_all_text_request, runtime)
            if response.status_code == HTTPStatus.OK:
                sub_images = response.body.data.sub_images
                if len(sub_images) > 0:
                    paragraph_info = sub_images[0].paragraph_info
                    content_list = [paragraph_detail.paragraph_content for paragraph_detail in
                                    paragraph_info.paragraph_details]
                    content = '\n'.join(content_list)
                    return content, None
                else:
                    return "", None
        except Exception as e:
            return None, "ocr出错:"+str(e)

    @classmethod
    def qwen(cls, prompt, callback):
        messages = [{"role": "system", "content": "你是医疗文书结构化的专家"},
                    {"role": "user", "content": prompt}]
        responses = dashscope.Generation.call(
            model=dashscope.Generation.Models.qwen_plus,
            messages=messages,
            result_format='message',
            stream=True,
            incremental_output=True
        )
        res = {
            "prompt": prompt
        }
        start_time = time.time()
        qwen_content = ""
        process = 0
        for response in responses:
            if response.status_code == HTTPStatus.OK:
                token_cnt = sum(response["usage"].values())
                res["token_cnt"] = token_cnt
                res["cost"] = 0.012 * token_cnt / 1000
                res["time"] = time.time() - start_time
                response_choice = response["output"]["choices"][0]
                if response_choice["finish_reason"] == "length":
                    return res, "超过模型最大返回限制"
                qwen_content += response.output.choices[0].message.content
                if callback is not None:
                    process = callback(process, qwen_content)
                res["qwen"] = qwen_content
            else:
                return res, "请求大模型失败:" + str(response.message)
        data, err = cls.to_json(qwen_content)
        if err is not None:
            return res, err
        res["data"] = data
        return res, None


    @classmethod
    def to_json(cls, message):
        start_json_idx = message.find("```json")
        if start_json_idx == -1:
            return None, "模型返回未检测到json"
        start_json_idx += 7
        end_json_idx = message.find("```", start_json_idx)
        if end_json_idx == -1:
            return None, "模型返回json未闭合"
        try:
            res = json5.loads(message[start_json_idx:end_json_idx])
            return res, None
        except Exception as e:
            return None, "模型返回非法json格式："+str(e)

    def transform(self, content, prompt_key, callback=None, ocr=False):
        prompt_key = str(prompt_key)
        text, err = self.ocr(content) if ocr else (content, None)
        if err is not None:
            return None, err
        text = text.strip()
        self.rwlock.read_acquire()
        try:
            prompt = self.prompts.get(prompt_key)
            if prompt is None:
                return None, "未找到对应的prompt"
            # 检验中抗体抗原特别处理，在 (+)0.033 前后加上", "(+)0.033"
            text = re.sub(r"(抗[体原]\s*\r?\n?\s*)(\([+\-]\)(?:\d+\.\d+|\d+))", "\g<1>\"\g<2>\"", text)
            prompt = prompt.replace("{input}", text)
            return self.qwen(prompt, callback)
        except Exception as e:
            return None, "ocr后未知错误：" + str(e)
        finally:
            self.rwlock.read_release()

@singleton
class ImportTaskManager(object):

    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    FAIL = "fail"

    def __init__(self):
        self.dec_char = "import_"
        self.db_name = None
        self.rwlock = RWLock()

    def dec_code(self, code):
        return f"{self.dec_char}{code}"

    def de_dec_code(self, code):
        return code.replace(self.dec_char, "")

    def task_status(self, code):
        code = self.dec_code(code)
        sql = f"select status, error, process from llm_task where code = '{code}'"
        res = select_by_sql(sql, self.db_name, config_map)
        if len(res) == 0:
            return None, None, None
        else:
            return res[0]["status"], res[0]["error"], json.loads(res[0]["process"])

    def add_task(self, ocr, contents, record_type, code, engine, host, is_first_page=False):
        # 1. 查询表中，是否有同code，未完成任务
        task_status, _, _ = self.task_status(code)
        if task_status is not None and (task_status == self.PENDING or task_status == self.RUNNING):
            return "有同code导入任务还在运行"
        # 2. 创建任务
        d_code = self.dec_code(code)
        del_sql = f"delete from llm_task where code = '{d_code}'"
        sql = "insert into llm_task(info, code, status, debug, process, error) values(%s, %s, %s, %s, %s, %s)"
        info = json.dumps({
            "images": contents if ocr else [],
            "contents": contents if not ocr else [],
            "ocr": ocr,
            "record_type": record_type,
            "engine": engine,
            "code": code,
            "host": host
        }, ensure_ascii=False, indent=2)
        debug = json.dumps([], ensure_ascii=False, indent=2)
        process = json.dumps(
            {"process": 0, "total": 5 if is_first_page else len(contents)}, ensure_ascii=False, indent=2)
        error = ""
        try:
            if task_status is not None:
                execute_sql(del_sql, self.db_name, config_map)
            execute_sql(sql, self.db_name, config_map, (info, d_code, self.PENDING, debug, process, error))
        except Exception as e:
            return "数据库异常：" + str(e)

    def fetch_task(self):
        # 领取任务
        self.rwlock.write_acquire()
        sql = f"select id, info, debug, process, result, error from llm_task where status = 'pending' order by id asc limit 1"
        res = select_by_sql(sql, self.db_name, config_map)
        if len(res) == 0:
            self.rwlock.write_release()
            return None, None
        try:
            info = json.loads(res[0]["info"])
            debug = json.loads(res[0]["debug"])
            process = json.loads(res[0]["process"])
            result = None if res[0]["result"] is None else json.loads(res[0]["result"])
            task = {
                "id": res[0]["id"],
                "info": info,
                "debug": debug,
                "process": process,
                "result": result,
                "error": res[0]["error"]
            }
            execute_sql(f"update llm_task set status = 'running' where id = {res[0]['id']}", self.db_name, config_map)
            return task, None
        except Exception as e:
            return None, str(e)
        finally:
            self.rwlock.write_release()

    def update_tasks(self, task_id, status, debug, process, error, result):
        sql = "update llm_task set status = %s, debug = %s, process = %s, error=%s, result=%s where id = %s"
        debug = json.dumps(debug, ensure_ascii=False, indent=2)
        process = json.dumps(process, ensure_ascii=False, indent=2)
        result = result if result is None or isinstance(result, str) else json.dumps(result, ensure_ascii=False, indent=2)
        try:
            execute_sql(sql, self.db_name, config_map, (status, debug, process, error, result, task_id))
        except Exception as e:
            return str(e)

    def resume_task(self):
        execute_sql(f"update llm_task set status = 'pending' where status = 'running'", self.db_name, config_map)

    def update_process(self, task_id, process):
        sql = "update llm_task set process = %s where id = %s"
        process = json.dumps(process, ensure_ascii=False, indent=2)
        try:
            execute_sql(sql, self.db_name, config_map, (process, task_id))
        except Exception as e:
            return str(e)


class ImportTaskRunner(object):

    def __init__(self, task_manager, idx):
        self.task_manager = task_manager
        self.idx = idx

    def notify(self, host, code, success, result, error):
        notify_url = f"{host}/mdPatient/progressResult"
        try:
            resp = requests.post(notify_url, headers={
              'Content-Type': 'application/json'
            }, data=json.dumps({
                "code":  code,
                "status": 1 if success else 2,
                "data": result,
                "msg": error
            }))
            logger.info(f"notify", json.loads(resp.text))
        except Exception as e:
            logger.info(f"notify error", e)

    def post_process(self, result, record_type):
        if record_type != "7001":
            return result
        for res in result:
            res["order_code"] = medical_order_code_mapping(res["order_content"], res["order_type"])
        return result

    def run(self, task):
        proc = task["process"]
        pg_idx = proc["process"]
        engine = task["info"]["engine"]
        ocr = task["info"]["ocr"]
        contents = task["info"]["images"] if ocr else task["info"]["contents"]
        code = task["info"]["code"]
        host = task["info"]["host"]
        record_type = task["info"]["record_type"]
        debug = task["debug"]
        error = task["error"]
        result = task["result"]
        update_process = lambda process: self.task_manager.update_process(task["id"], process)

        def trans_callback(prev_proc, qwen_content):
            proc_now = -1
            if '"feeData"' in qwen_content:
                proc_now = 4
            elif '"operationList"' in qwen_content:
                proc_now = 3
            elif '"otherData"' in qwen_content:
                proc_now = 2
            elif '"diseaseList"' in qwen_content:
                proc_now = 1
            if proc_now > prev_proc:
                update_process({"process": proc_now, "total": 5})
                return proc_now
            else:
                return prev_proc

        while pg_idx < len(contents):
            content = contents[pg_idx]
            if engine == "llm_import_ocrqwen":
                res, err = ocr_then_qwen.transform(content, record_type,
                                                   trans_callback if record_type == "15" else None,
                                                   ocr=ocr)
            if res is not None:
                debug.append(res)
            if err is not None:
                error = err
                self.notify(host, code, False, result, error)
                self.task_manager.update_tasks(task["id"], self.task_manager.FAIL, debug, proc, err, None)
                break
            pg_idx += 1
            if record_type != "15":
                proc["process"] = pg_idx
            else:
                proc["process"] = 5
            if pg_idx == len(contents):
                result = []
                if record_type == "15":
                    result = debug[0]["data"]
                else:
                    for res in debug:
                        result.extend(res["data"])
                result = self.post_process(result, record_type)
                self.notify(host, code, True, result, error)
                self.task_manager.update_tasks(task["id"], self.task_manager.SUCCESS, debug, proc, None, result)
            else:
                self.task_manager.update_tasks(task["id"], self.task_manager.RUNNING, debug, proc, None, None)

    def wait_run(self):
        while True:
            task, err = self.task_manager.fetch_task()
            if err is not None:
                print(err)
                continue
            if task is None:
                time.sleep(5)
                continue
            logger.info(f"{self.idx} 线程开始运行任务{task['id']}")
            self.run(task)


MEDICAL_ORDER_1001_SQL = {
    "4": """select mnc.org_nurse_id  as res
from mt_nurse_customer_relation mncr 
join mt_nurse_customer mnc 
on mncr.org_nurse_id = mnc.id 
and mnc.status = 1
where mncr.customer_id = 1001 and mncr.status=1 
and mncr.nurse_id = {} limit 1""",
    "5": """select mnc.org_operation_id as res
from mt_operation_customer_relation mncr 
join mt_operation_customer mnc 
on mncr.org_id = mnc.id 
and mnc.status = 1
where mncr.customer_id = 1001 and mncr.status=1 
and mncr.operation_id = {} limit 1""",
    "6": """select mnc.org_operation_id as res
from mt_operation_customer_relation mncr 
join mt_operation_customer mnc 
on mncr.org_id = mnc.id 
and mnc.status = 1
where mncr.customer_id = 1001 and mncr.status=1 
and mncr.operation_id = {} limit 1""",
    "7": """select mnc.org_operation_id as res
from mt_operation_customer_relation mncr 
join mt_operation_customer mnc 
on mncr.org_id = mnc.id 
and mnc.status = 1
where mncr.customer_id = 1001 and mncr.status=1 
and mncr.operation_id = {} limit 1""",
    "2": """select mnc.org_exam_id as res
from mt_exam_customer_relation mecr
join mt_exam_customer mnc 
on mecr.org_id = mnc.id 
and mnc.status = 1
where mecr.customer_id = 1001
and mecr.exam_id = {} limit 1""",
    "1": """select mnc.org_exam_id as res
from mt_exam_customer_relation mecr
join mt_exam_customer mnc 
on mecr.org_id = mnc.id 
and mnc.status = 1
where mecr.customer_id = 1001
and mecr.exam_id = {} limit 1""",
    "3": """select org_drug_id as res
from dt_drug_customer
where customer_id = 1001 and common_id = {} limit 1"""
}


def medical_order_code_1001_mapping(order_code, order_type):
    if str(order_type) not in MEDICAL_ORDER_1001_SQL:
        return None
    result = select_by_sql(MEDICAL_ORDER_1001_SQL[str(order_type)].format(order_code), MEDICAL_ORDER_DB, config_map)
    if result is None:
        return None
    return str(result[0]["res"])


def medical_order_code_mapping(content, order_type):
    # 1：检验，2：检查:3：药品，4：护理:5：膳食:6：手术:7：处置:8：会诊，9：死亡医嘱
    # drug、exam、nurse、department、operation
    mapping = {
        "1": "exam",
        "2": "exam",
        "3": "drug",
        "4": "nurse",
        "5": "nurse",
        "6": "operation",
        "7": "operation"
    }
    if content is None or str(content).strip() == "" or str(order_type) not in mapping:
        return None
    url = MEDICAL_ORDER_MAPPING_SERVICE + "/dict_map"
    payload = json.dumps({
        "customer_id": "1001",
        "dict_type": mapping[str(order_type)],
        "threshold": 0.9,
        "items": [
            {
                "id": 1,
                "name": str(content).strip()
            }
        ]
    })
    headers = {
        'Content-Type': 'application/json'
    }
    try:
        response = requests.request("POST", url, headers=headers, data=payload)
        res = json.loads(response.text)
        hm_id = res[0]["results"][0]["hm_id"]
        if hm_id < 0:
            return None
        return medical_order_code_1001_mapping(str(hm_id), order_type)
    except Exception as e:
        with open("debug.txt", "a", encoding="utf-8") as f:
            f.write("请求医嘱映射失败"+str(e)+"\n")
        return None


def refresh_config():
    global ocr_then_qwen
    err_list = []
    err_msg_a = ocr_then_qwen.refresh_config()
    if err_msg_a is not None:
        err_list.append(err_msg_a)
    return "\n".join(err_list) if len(err_list) > 0 else None


def download_file(url, target_path):
    try:
        urllib.request.urlretrieve(url, target_path)
    except Exception as e:
        return str(e)


def to_long_img(images):
    target_img_path = images[0]
    images_list = [Image.open(img_path) for img_path in images]
    widths, heights = zip(*(i.size for i in images_list))

    total_height = sum(heights)
    max_width = max(widths)
    new_im = Image.new('RGB', (max_width, total_height))
    y_offset = 0
    for im in images_list:
        new_im.paste(im, (0, y_offset))
        y_offset += im.size[1]
    new_im.save(target_img_path)
    for img_path in images[1:]:
        os.remove(img_path)
    return [images[0]], None


def pdf2img(file_path, code, all_in_one=False):
    images = []
    try:
        pdf_doc = fitz.open(file_path)
        for pg in range(pdf_doc.page_count):
            page = pdf_doc[pg]
            rotate = int(0)
            zoom_x = 2
            zoom_y = 2
            mat = fitz.Matrix(zoom_x, zoom_y).prerotate(rotate)
            pix = page.get_pixmap(matrix=mat, alpha=False)
            img_path = os.path.join(PDF_IMG_PATH, f"{code}_{str(pg).zfill(3)}.png")
            pix.pil_save(img_path)
            images.append(img_path)
        if not all_in_one or len(images) == 1:
            return images, None
        return to_long_img(images)
    except Exception as e:
        return None, "pdf转图片出错:"+str(e)


def import_pg_status(req):
    code = req["code"]
    status, error, process = import_task_manager.task_status(code)
    if status is None:
        return {"status": 1, "process": 0, "total": 1}
    if status == import_task_manager.PENDING:
        return {"status": 2, "process": process["process"], "total": process["total"]}
    if status == import_task_manager.FAIL:
        return {"status": 4, "error": error, "process": process["process"], "total": process["total"]}
    if status == import_task_manager.RUNNING:
        return {"status": 2, "process": process["process"], "total": process["total"]}
    if status == import_task_manager.SUCCESS:
        return {"status": 3, "process": process["process"], "total": process["total"]}


def import_pg(req):
    # 文件下载
    url = req.get("url", '')
    code = req.get("code", '')
    host = req.get("host", '')
    process_type = req.get("type", 2)
    content = req.get("content", [])
    if content is None:
        content = []
    content = [con for con in content if con.strip() != '']
    record_type = str(req.get("record_type", ''))
    if url == '' or code == '' or host == '' or record_type == '':
        return "请求参数缺失，检查url、code、host、record_type参数是否正确"
    if process_type == 1 and (content is None or len(content) == 0):
        return "请求参数错误，type为1（文本解析），但content为空"
    parser_engine = req.get("engine", "llm_import_ocrqwen")
    if process_type == 2:
        if url.startswith("file://"):
            file_path = url[7:]
        elif url.startswith("http://") or url.startswith("https://"):
            file_path = os.path.join(PDF_STORAGE_PATH, f"{code}.pdf")
            err = download_file(url, file_path)
            if err is not None:
                return "url下载失败:"+err
        else:
            return "url格式错误"
        if not os.path.exists(file_path):
            return "url给定的文件下载出错"
        # pdf转图片
        images, err = pdf2img(file_path, code, record_type == "15")
        if err is not None:
            return err
        return import_task_manager.add_task(process_type == 2, images, record_type, code,
                                            parser_engine, host, record_type == "15")
    else:
        return import_task_manager.add_task(process_type == 2, content, record_type, code,
                                            parser_engine, host, record_type == "15")


def pdf_ocr_impl(req):
    url = req.get("file_url", '')
    if url == '':
        return -1, [], "请求参数缺失，检查file_url参数是否正确", 1
    code = str(uuid.uuid4())
    if url.startswith("file://"):
        file_path = url[7:]
    elif url.startswith("http://") or url.startswith("https://"):
        file_path = os.path.join(PDF_STORAGE_PATH, f"{code}.pdf")
        err = download_file(url, file_path)
        if err is not None:
            return -1, [], "url下载失败:" + err, 1
    else:
        return -1, [], "url格式错误", 1
    images, err = pdf2img(file_path, code)
    if err is not None:
        return -1, [], err, 1
    que = queue.Queue()

    def page_ocr(img, q, pid):
        txt, er = ocr_then_qwen.ocr(img)
        if er is not None:
            txt = ""
        q.put((pid, txt, er))

    for idx in range(0, len(images), 4):
        ts = []
        for jdx, img in enumerate(images[idx:idx+4]):
            t = threading.Thread(target=page_ocr, args=(img, que, idx+jdx))
            ts.append(t)
            t.start()
        for t in ts:
            t.join()
    result = list(que.queue)
    result.sort(key=lambda x: x[0])
    error_cnt = 0
    contents = []
    error_msg = ""
    for _, content, err in result:
        contents.append(content)
        if err is not None:
            error_cnt += 1
            error_msg += err
    return len(images), contents if error_cnt == 0 else [], None if error_msg == "" else error_msg, error_cnt


ocr_then_qwen = OcrThenQwen()
import_task_manager = ImportTaskManager()
import_task_manager.db_name = TASK_DB_NAME
if IMPORT_TASK_RUNNER_NUM > 0:
    # 服务重启导致任务暂停，但状态仍然为running，更新为pending
    import_task_manager.resume_task()
    for i in range(IMPORT_TASK_RUNNER_NUM):
        import_task_runner = ImportTaskRunner(import_task_manager, idx=i)
        threading.Thread(target=import_task_runner.wait_run).start()
