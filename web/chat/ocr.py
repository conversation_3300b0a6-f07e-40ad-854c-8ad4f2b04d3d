# coding:utf-8
import json,os
from http import HTTPStatus
import sys
from typing import List
import time
from alibabacloud_ocr_api20210707.client import Client as ocr_api20210707Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_darabonba_stream.client import Client as StreamClient
from alibabacloud_ocr_api20210707 import models as ocr_api_20210707_models
from alibabacloud_tea_util import models as util_models
import urllib.request
proDir = os.path.split(os.path.realpath(__file__))[0]
ocr_config = open_api_models.Config(
            access_key_id=os.environ['ALIBABA_CLOUD_ACCESS_KEY_ID'],
            access_key_secret=os.environ['ALIBABA_CLOUD_ACCESS_KEY_SECRET']
        )
# ocr_config.endpoint = f'ocr-api.cn-hangzhou.aliyuncs.com'
ocr_config.endpoint = f'ocr-api-vpc.cn-hangzhou.aliyuncs.com'


def image_ocr(request_data):
    answer = ''
    request_data = json.loads(request_data.decode('utf-8'))
    image_url = request_data.get('image_url', '').strip()
    image_path = os.path.join(proDir, 'image_path')
    file_path = os.path.join(image_path, image_url.split('/')[-1])
    # if os.path.exists(file_path):
    #     os.remove(file_path)
    start = time.time()
    # opener = urllib.request.build_opener()
    # opener.addheaders = [('User-agent',
    #                       'Mozilla/5.0 (iPhone; CPU iPhone OS 13_2_3 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0.3 Mobile/15E148 Safari/604.1')]
    # urllib.request.install_opener(opener)
    urllib.request.urlretrieve(image_url, file_path)
    download_done = time.time()
    print('ocr download time：{}'.format(download_done-start))
    if not os.path.exists(file_path):
        return image_url, answer, '图片下载失败'
    client = ocr_api20210707Client(ocr_config)
    # 需要安装额外的依赖库，直接点击下载完整工程即可看到所有依赖。
    body_stream = StreamClient.read_from_file_path(file_path)
    advanced_config = ocr_api_20210707_models.RecognizeAllTextRequestAdvancedConfig(
        output_paragraph=True
    )
    recognize_all_text_request = ocr_api_20210707_models.RecognizeAllTextRequest(
        body=body_stream,
        type='Advanced',
        advanced_config=advanced_config
    )
    runtime = util_models.RuntimeOptions()
    try:
        response = client.recognize_all_text_with_options(recognize_all_text_request, runtime)
        print('ocr api time：{}'.format(time.time()-download_done))
        if response.status_code == HTTPStatus.OK:
            sub_images = response.body.data.sub_images
            if len(sub_images) > 0:
                paragraph_info = sub_images[0].paragraph_info
                content_list = [paragraph_detail.paragraph_content for paragraph_detail in
                                paragraph_info.paragraph_details]
                content = '\n'.join(content_list)
                return image_url, content, ""
    except Exception as error:
        return image_url, answer, str(error.message)
    return image_url, answer, "error"


