# coding:utf-8
# Note: you need to be using OpenAI Python v0.27.0 for the code below to work
import json,os
import urllib.request
import re
from web.chat.chat_history import get_chat_history,get_chat_inquiry
from web.web_utils.api_util import post_url
from http import HTT<PERSON>tatus
from dashscope import Generation, MultiModalConversation
import dashscope
from openai import OpenAI, AzureOpenAI
dashscope.api_key = 'sk-348f56558947442388dd8e9ea7974c55'
proDir = os.path.split(os.path.realpath(__file__))[0]
reg_pattern_disease = re.compile(r'{}'.format("(预测|生成|给出)[^。;]*(初步诊断|诊断依据|鉴别诊断)"))
reg_pattern_treatment = re.compile(r'{}'.format("(预测|生成|给出)[^。;]*诊疗计划"))


def get_chat_response(request_data,config_map, chat_type=''):
    answer = ''
    error_msg = ''
    request_data = json.loads(request_data.decode('utf-8'))
    prompt = request_data.get('prompt', '').strip()
    dialogue_code = request_data.get('dialogue_code', None)
    chat_model = request_data.get('chat_model', None)
    customer_id = request_data.get('customer_id', None)
    if not chat_model:
        return prompt,answer,'chat_model不能为空'
    pattern = r"prefix=特殊计算流程.*prefix_end"
    # 遍历句子并提取错误和正确的词汇
    matches = re.findall(pattern, prompt)
    if len(matches)>0:
        answer, code = special_rule(prompt)
        if answer and code and code != 'pass':
            return prompt, answer, error_msg
        else:
            prompt = str(prompt).replace(matches[0],'').strip()

    if chat_model=='chatgpt' or chat_model in ['qwen_test','qwen_pre','qwen_api','qwen_1_5']:
        prompt, answer,error_msg = get_chatgpt_response(customer_id, chat_model,prompt,dialogue_code,config_map,chat_type=chat_type)
    elif chat_model=='hmgpt_gpu' or chat_model=='hmgpt_cpu' or chat_model in ['qwen_test_mrg','qwen_pre_mrg']:
        prompt, answer,error_msg = get_hmgpt_response(customer_id, chat_model,prompt,dialogue_code,config_map,chat_type=chat_type)

    return prompt,answer,error_msg


def get_chatgpt_response(cid, chat_model,prompt, dialogue_code, config_map,chat_type=''):
    history_list = []
    # message_list = [{"role": "system", "content": "You are a helpful assistant."}]
    message_list = []
    if chat_type == 'admission':
        history_list = get_chat_inquiry(cid, chat_model,dialogue_code, config_map)
        if prompt.find('{input}')>-1:
            prompt = prompt.replace('{input}', '\n'.join(history_list))
        else:
            prompt += '\n'.join(history_list)
    else:
        if dialogue_code:
            history_list = get_chat_history(cid, chat_model,dialogue_code, config_map,chat_type=chat_type)
        for history in history_list:
            message_list.append(history)
    message_list.append({"role": "user", "content": prompt})
    answer = ''
    error_msg = ''
    try:
        if chat_model in ['qwen_test','qwen_pre','qwen_1_5']:
            openai_api_key = "EMPTY"
            openai_api_base = "http://{}:{}/v1".format(config_map['app']['{}_ip'.format(chat_model)],
                                                       config_map['app']['{}_port'.format(chat_model)])
            client = OpenAI(
                api_key=openai_api_key,
                base_url=openai_api_base,
            )
            if chat_model == 'qwen_1_5':
                response = client.chat.completions.create(
                    model="/data/share_model/Qwen1.5-14B-Chat",
                    top_p=0.01,
                    timeout=30,
                    messages=message_list
                )
            else:
                response = client.chat.completions.create(
                    model="qwen",
                    top_p=0,
                    timeout=30,
                    # frequency_penalty=0,
                    # presence_penalty=0,
                    # stop=None,
                    messages=message_list
                )
        elif chat_model in ['qwen_api']:
            response = Generation.call(
                Generation.Models.qwen_max,
                # model='qwen-max-longcontext',
                messages=message_list,
                result_format='message',  # set the result to be "message" format.
                top_k=1,
            )
            if response.status_code == HTTPStatus.OK:
                answer = response.output.choices[0].get("message", {}).get("content", '')
                if len(answer) > 0:
                    return prompt, answer, error_msg
        else:
            openai_api_key = "********************************"
            openai_api_base = "https://hmopenairesource.openai.azure.com"
            client = AzureOpenAI(
                api_key=openai_api_key,
                api_version="2023-03-15-preview",
                azure_endpoint=openai_api_base,
            )
            response = client.chat.completions.create(model="test_gpt_16k", messages=message_list, top_p=0, timeout=30)
        if response and len(response.choices) > 0:
            if response.choices[0].finish_reason == 'stop':
                answer = response.choices[0].message.content
                if len(answer) > 0:
                    return prompt, answer, error_msg
    except:
        print("异常：")
    return prompt, answer,'网络异常'


def get_hmgpt_response(cid, chat_model,prompt, dialogue_code, config_map,max_length=2048,chat_type=''):
    lora_model = chat_model
    history_list = []
    if chat_model.find('hmgpt')>-1:
        lora_model = 'default'
        if re.search(reg_pattern_treatment, prompt):
            lora_model = 'treatment_model'
        elif re.search(reg_pattern_disease, prompt):
            lora_model = 'disease_model'
        if dialogue_code and lora_model == 'default':
            history_list = get_chat_history(cid, chat_model,dialogue_code, config_map,chat_type=chat_type)
    chat_ip = config_map['app']['chat_cpu_ip']
    chat_port = config_map['app']['chat_cpu_port']
    if chat_model.find('gpu')>-1:
        chat_ip = config_map['app']['chat_gpu_ip']
        chat_port = config_map['app']['chat_gpu_port']
    json_obj = {"prompt": prompt, "max_length": max_length, "lora_model": lora_model, "history": history_list}
    if chat_model.find('mrg')>-1:
        lora_model = 'mrg_model'
        chat_ip = config_map['app']['{}_ip'.format(chat_model.replace('_mrg',''))]
        chat_port = config_map['app']['{}_port'.format(chat_model.replace('_mrg',''))]
        history_list = get_chat_inquiry(cid, chat_model, dialogue_code, config_map)
        if prompt.find('{input}') > -1:
            prompt = prompt.replace('{input}', '\n'.join(history_list))
        else:
            prompt += '\n'.join(history_list)
        json_obj = {"input": prompt, "lora_model": lora_model}
    answer = ''
    error_msg = ''
    try:
        dia_url = 'http://{}:{}/generate'.format(chat_ip, chat_port)
        # return_json = post_url('http://*************:28018/generate', json_obj)
        return_json = post_url(dia_url, json_obj)
        answer = return_json.get('response', '')
        return prompt, answer,error_msg
    except:
        print("异常：")
    return prompt, answer,'网络异常'


def get_vl_chat_response(request_data,config_map, chat_type=''):
    answer = ''
    error_msg = ''
    request_data = json.loads(request_data.decode('utf-8'))
    image_url = request_data.get('image_url', '').strip()
    prompt = request_data.get('prompt', '识别图片中的文字').strip()
    chat_model = request_data.get('chat_model', None)
    image_path = os.path.join(proDir, 'image_path')
    file_path = os.path.join(image_path, image_url.split('/')[-1])
    if os.path.exists(file_path):
        os.remove(file_path)
    urllib.request.urlretrieve(image_url, file_path)
    messages = [{
        'role': 'system',
        'content': [{
            'text': 'You are a helpful assistant.'
        }]
    }, {
        'role':
            'user',
        'content': [
            {
                'image': 'file://{}'.format(file_path)
            },
            {
                'text': prompt
            },
        ]
    }]
    response = MultiModalConversation.call(model='qwen-vl-plus', messages=messages, top_k=1)
    if response.status_code == HTTPStatus.OK:
        content = response.output.choices[0].get("message", {}).get("content", [])
        if len(content) > 0:
            answer = content[0]['text']
            if len(answer) > 0:
                return image_url, prompt, answer, ""
    return image_url, prompt,answer,response

def special_rule(prompt):
    try:
        json_obj = {"prompt": prompt}
        dia_url = 'http://218.205.95.254:8082/special_rule'
        return_json = post_url(dia_url, json_obj)
        answer = return_json.get('response', '')
        code = return_json.get('code', '')
        return answer, code
    except:
        print("异常：")
        return '', 'pass'