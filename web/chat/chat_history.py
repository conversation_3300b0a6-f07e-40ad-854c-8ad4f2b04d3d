# coding:utf-8
from web.web_utils.sql_util import select_by_sql


def get_chat_history(cid, chat_model,dialogue_code, config_map,max_history_len=8000,chat_type=''):
    sql = '''
                select dialogue_code,content,ai_content,chat_model,create_time from gpt_dialogue_content where dialogue_code='{}' ORDER BY create_time desc
        		 '''.format(dialogue_code)
    result = select_by_sql(sql, 'kunlun', config_map)
    history_list = []
    history_len = 0
    for i, v in enumerate(result):
        content = v['content'] if v['content'] else ''
        if i==len(result)-1 and chat_type=='admission':
            content = '问诊信息'
        ai_content = v['ai_content'] if v['ai_content'] else ''
        if not ai_content:
            continue
        history_len += len(ai_content)
        if ai_content and history_len < max_history_len:
            history_list.append({"role": "assistant", "content": ai_content})
        history_len += len(content)
        if content and history_len < max_history_len:
            history_list.append({"role": "user", "content": content})
        # if chat_model=='chatgpt':
        #     history_len += len(ai_content)
        #     if history_len < max_history_len:
        #         history_list.append({"role": "assistant", "content": ai_content})
        #     history_len += len(content)
        #     if history_len < max_history_len:
        #         history_list.append({"role": "user", "content": content})
        # elif chat_model.find('hmgpt')>-1:
        #     history_len += len(content+ai_content)
        #     if history_len < max_history_len:
        #         history_list.append((content, ai_content))

    history_list = history_list[::-1]
    return history_list

def get_chat_inquiry(cid, chat_model,dialogue_code, config_map,max_history_len=8000):
    sql = '''
                select dialogue_code,content,ai_content,chat_model,create_time from gpt_dialogue_content where dialogue_code='{}' ORDER BY create_time
        		 '''.format(dialogue_code)
    result = select_by_sql(sql, 'kunlun', config_map)
    history_list = []
    history_len = 0
    for i, v in enumerate(result):
        if i > 0:
            content = v['content'] if v['content'] else ''
            content = '患者：'+content
            history_len += len(content)
            if history_len < max_history_len:
                history_list.append(content)
        if i < len(result)-1:
            ai_content = v['ai_content'] if v['ai_content'] else ''
            ai_content = '医生：'+ai_content
            history_len += len(ai_content)
            if history_len < max_history_len:
                history_list.append(ai_content)

    return history_list