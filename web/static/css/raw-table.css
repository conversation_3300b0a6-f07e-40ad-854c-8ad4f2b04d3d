@CHARSET "UTF-8";

* {
	margin: 0;
	padding: 0;
}

body {
	padding: 10px 10px;
}

.demo {
	width: 600px;
	margin: 40px auto;
	font-family: 'trebuchet MS', 'Lucida sans', <PERSON><PERSON>;
	font-size: 14px;
	color: #444;
}

table {
	*border-collapse: collapse; /* IE7 and lower */
	border-spacing: 0;
	width: 100%;
	margin-top: 10px;
	table-layout:fixed;
	word-break:break-all;
}
/*========bordered table========*/
.bordered {
	border: solid #ccc 1px;
	-moz-border-radius: 10px;
	-webkit-border-radius: 10px;
	border-radius: 10px;
	-webkit-box-shadow: 0 1px 1px #ccc;
	-moz-box-shadow: 0 1px 1px #ccc;
	box-shadow: 0 1px 1px #ccc;
}

.bordered tr {
	-o-transition: all 0.1s ease-in-out;
	-webkit-transition: all 0.1s ease-in-out;
	-moz-transition: all 0.1s ease-in-out;
	-ms-transition: all 0.1s ease-in-out;
	transition: all 0.1s ease-in-out;
}

.bordered .highlight, /* .bordered tr:hover {
				background: #fbf8e9;
			} */ .bordered td,.bordered th {
	border-left: 1px solid #ccc;
	border-top: 1px solid #ccc;
	padding: 10px;
	text-align: left;
}

.bordered th {
	background-color: #FBFBFC;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#FBFBFC),
		to(#FBFBFC) );
	background-image: -webkit-linear-gradient(top, #FBFBFC, #FBFBFC);
	background-image: -moz-linear-gradient(top, #FBFBFC, #FBFBFC);
	background-image: -ms-linear-gradient(top, #FBFBFC, #FBFBFC);
	background-image: -o-linear-gradient(top, #FBFBFC, #FBFBFC);
	background-image: linear-gradient(top, #FBFBFC, #FBFBFC);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,
		startColorstr=#ebf3fc, endColorstr=#dce9f9 );
	-ms-filter:
		"progid:DXImageTransform.Microsoft.gradient (GradientType=0, startColorstr=#ebf3fc, endColorstr=#dce9f9)";
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
	-moz-box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
	box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
	border-top: none;
	text-shadow: 0 1px 0 rgba(255, 255, 255, .5);
}

.bordered td:first-child,.bordered th:first-child {
	border-left: none;
}

.bordered th:first-child {
	-moz-border-radius: 10px 0 0 10px;
	-webkit-border-radius: 10px 0 0 10px;
	border-radius: 10px 0 0 10px;
}

.bordered th:last-child {
	-moz-border-radius: 0 10px 10px 0;
	-webkit-border-radius: 0 10px 10px 0;
	border-radius: 0 10px 10px 0;
}

.bordered tr:last-child td:first-child {
	-moz-border-radius: 0 0 0 10px;
	-webkit-border-radius: 0 0 0 10px;
	border-radius: 0 0 0 10px;
}

.bordered tr:last-child td:last-child {
	-moz-border-radius: 0 0 10px 0;
	-webkit-border-radius: 0 0 10px 0;
	border-radius: 0 0 10px 0;
}
/*----------------------*/
.zebra td,.zebra th {
	padding: 10px;
	border-bottom: 1px solid #f2f2f2;
}

.zebra .alternate,.zebra tbody tr:nth-child(even) {
	background: #f5f5f5;
	-webkit-box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
	-moz-box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
	box-shadow: 0 1px 0 rgba(255, 255, 255, .8) inset;
}

.zebra th {
	text-align: left;
	text-shadow: 0 1px 0 rgba(255, 255, 255, .5);
	border-bottom: 1px solid #ccc;
	background-color: #eee;
	background-image: -webkit-gradient(linear, left top, left bottom, from(#f5f5f5),
		to(#eee) );
	background-image: -webkit-linear-gradient(top, #f5f5f5, #eee);
	background-image: -moz-linear-gradient(top, #f5f5f5, #eee);
	background-image: -ms-linear-gradient(top, #f5f5f5, #eee);
	background-image: -o-linear-gradient(top, #f5f5f5, #eee);
	background-image: linear-gradient(top, #f5f5f5, #eee);
	filter: progid:DXImageTransform.Microsoft.gradient(GradientType=0,
		startColorstr=#f5f5f5, endColorstr=#eeeeee );
	-ms-filter:
		"progid:DXImageTransform.Microsoft.gradient (GradientType=0, startColorstr=#f5f5f5, endColorstr=#eeeeee)";
}

.zebra th:first-child {
	-moz-border-radius: 10px 0 0 10px;
	-webkit-border-radius: 10px 0 0 10px;
	border-radius: 10px 0 0 10px;
}

.zebra th:last-child {
	-moz-border-radius: 0 10px 10px 0;
	-webkit-border-radius: 0 10px 10px 0;
	border-radius: 0 10px 10px 0;
}

.zebra tfoot td {
	border-bottom: 0;
	border-top: 1px solid #fff;
	background-color: #f1f1f1;
}

.zebra tfoot td:first-child {
	-moz-border-radius: 0 0 0 10px;
	-webkit-border-radius: 0 0 0 10px;
	border-radius: 0 0 0 10px;
}

.zebra tfoot td:last-child {
	-moz-border-radius: 0 0 10px 0;
	-webkit-border-radius: 0 0 10px 0;
	border-radius: 0 0 10px 0;
}

.img_css {
	vertical-align: middle;
	margin-right: 10px;
	margin-bottom: 3px;
}
