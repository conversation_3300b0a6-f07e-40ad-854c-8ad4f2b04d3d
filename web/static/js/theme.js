'use strict';var theme=function(){function handlePreventEmptyLinks(){$('a[href=#]').click(function(event){event.preventDefault();});}
function handleHoverClass(){var hover=$('.thumbnail');hover.hover(function(){$(this).addClass('hover');},function(){$(this).removeClass('hover');});}
function handleSuperFish(){$('ul.sf-menu').superfish();$('ul.sf-menu a').click(function(){$('body').scrollspy('refresh');});$('.menu-toggle').on('click',function(){if($('.navigation').hasClass('opened')){$(this).find('.fa').removeClass('fa-times').addClass('fa-bars');$('.navigation').removeClass('opened').addClass('closed');}else{$(this).find('.fa').removeClass('fa-bars').addClass('fa-times');$('.navigation').removeClass('closed').addClass('opened');}});$('.mobile-submenu').click(function(){$(this).parent().toggleClass('mobile-submenu-open');});$('ul.sf-menu a').click(function(){$('ul.sf-menu li').removeClass('mobile-submenu-open');});}
function handleSmoothScroll(){$('.sf-menu a, .scroll-to').click(function(){if($(this).hasClass('btn-search-toggle')){$('.header-search-wrapper').fadeToggle();$('.header').toggleClass('header-overlay');}
else{var headerH=0;$('.sf-menu a').removeClass('active');$(this).addClass('active');$('html, body').animate({scrollTop:$($(this).attr('href')).offset().top-headerH+'px'},{duration:1200,easing:'easeInOutExpo'});return false;}});}
function handlePrettyPhoto(){$("a[data-gal^='prettyPhoto']").prettyPhoto({theme:'dark_square'});}
function handleToTopButton(){$(window).scroll(function(){if($(this).scrollTop()>1){$('.to-top').css({bottom:'15px'});}else{$('.to-top').css({bottom:'-100px'});}});$('.to-top').click(function(){$('html, body').animate({scrollTop:'0px'},800);return false;});}
$(window).load(function(){$('#status').fadeOut();$('#preloader').delay(200).fadeOut(100);});$(window).load(function(){if($().isotope){$('.isotope.events').isotope({filter:'.festival',itemSelector:'.isotope-item'});$('#filtrable-events a').click(function(){var selector=$(this).attr('data-filter');$('#filtrable-events a').parent().removeClass('current');$(this).parent().addClass('current');$('.isotope.events').isotope({filter:selector});$('.isotope').isotope('reLayout',$.waypoints('refresh'));return false;});}
if($().isotope){$('.isotope.gallery').isotope({itemSelector:'.isotope-item'});var selector=$(this).attr('data-filter');$(this).parent().addClass('current');$('.isotope.gallery').isotope({filter:selector});$('.isotope').isotope('reLayout',$.waypoints('refresh'));return false;}});$(window).resize(function(){if($().isotope){$('.isotope').isotope('reLayout',$.waypoints('refresh'));}});function handleAnimatedHeader(){var header=$('.header.fixed');function refresh(){var scroll=$(window).scrollTop();if(scroll>=99){header.addClass('shrink');}else{header.removeClass('shrink');}
$.waypoints('refresh');};$(window).load(function(){refresh();});$(window).scroll(function(){refresh();});$(window).on('touchstart',function(){refresh();});$(window).on('scrollstart',function(){refresh();});$(window).on('scrollstop',function(){refresh();});$(window).on('touchmove',function(){refresh();});}
function handleTabsFAQ(){if($('#tabs-faq').length){var tabs=$('#tabs-faq');tabs.find('a').on('click',function(){tabs.find('.fa-angle-right').removeClass('fa-angle-right').addClass('fa-plus');$(this).find('.fa').removeClass('fa-plus').addClass('fa-angle-right');});}}
function resizePage(){if($('body').hasClass('boxed')){$('#main-slider').find('.page').each(function(){$(this).removeAttr('style');});}
$('#main-slider').trigger('refresh');$('#testimonials').trigger('refresh');$('.partners-carousel .owl-carousel').trigger('refresh');$('.partners-carousel-2 .owl-carousel').trigger('refresh');$('.carousel-slider .owl-carousel').trigger('refresh');}
return{onResize:function(){resizePage();},init:function(){handlePreventEmptyLinks();handleHoverClass();handleSuperFish();handleSmoothScroll();handlePrettyPhoto();handleToTopButton();handleAnimatedHeader();handleTabsFAQ();},initMainSlider:function(){$('#main-slider').owlCarousel({autoplay:true,autoplayHoverPause:true,loop:true,margin:0,dots:false,nav:true,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"],responsiveRefreshRate:100,responsive:{0:{items:1},479:{items:1},768:{items:1},991:{items:1},1024:{items:1}}});},initImageCarousel:function(){$('.img-carousel').owlCarousel({autoplay:false,loop:true,margin:0,dots:true,nav:true,navText:["<i class='fa fa-angle-left'></i>","<i class='fa fa-angle-right'></i>"],responsiveRefreshRate:100,responsive:{0:{items:1},479:{items:1},768:{items:1},991:{items:1},1024:{items:1}}});},initCorouselSlider4:function(){$('.carousel-slider .owl-carousel.slide-4').owlCarousel({autoplay:true,loop:true,margin:30,dots:false,nav:true,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"],responsive:{0:{items:1},480:{items:2},767:{items:3},991:{items:3},1024:{items:4}}});},initCorouselSlider3:function(){$('.carousel-slider .owl-carousel.slide-3').owlCarousel({autoplay:false,loop:true,margin:30,dots:false,nav:true,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"],responsive:{0:{items:1},480:{items:2},767:{items:3},991:{items:3},1024:{items:4}}});},initPartnerSlider:function(){$('.partners-carousel .owl-carousel').owlCarousel({autoplay:false,loop:true,margin:25,dots:false,nav:true,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"],responsive:{0:{items:1},370:{items:2},768:{items:3},991:{items:5},1024:{items:6}}});},initPartnerSlider2:function(){$('.partners-carousel-2 .owl-carousel').owlCarousel({autoplay:true,loop:true,margin:25,dots:false,nav:true,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"],responsive:{0:{items:1},479:{items:2},768:{items:3},991:{items:5},1024:{items:5}}});},initEventCarousel:function(){$('.event-carousel .owl-carousel').owlCarousel({autoplay:false,loop:false,margin:25,dots:true,nav:true,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"],responsive:{0:{items:1},479:{items:1},768:{items:2},991:{items:3},1024:{items:4}}});},initTestimonials:function(){$('#testimonials').owlCarousel({items:1,autoplay:false,loop:true,dots:true,nav:false,navText:["<i class='fa fa-caret-left'></i>","<i class='fa fa-caret-right'></i>"]});},initAnimation:function(){var isMobile=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);if(isMobile==false){$('*[data-animation]').addClass('animated');$('.animated').waypoint(function(down){var elem=$(this);var animation=elem.data('animation');if(!elem.hasClass('visible')){var animationDelay=elem.data('animation-delay');if(animationDelay){setTimeout(function(){elem.addClass(animation+' visible');},animationDelay);}else{elem.addClass(animation+' visible');}}},{offset:$.waypoints('viewportHeight')});}
$('#tabs-lv1 li a[data-toggle="tab"]').on('shown.bs.tab',function(){$.waypoints('refresh');});$('#tabs-lv2 li a[data-toggle="tab"]').on('shown.bs.tab',function(){$.waypoints('refresh');});},};}();$(document).ready(function(){});