{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <script type="text/javascript">

    </script>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
    <script src="/static/js/bootstrapPager.js"></script>
{% endblock %}

{% block body %}
    <div id="content">
        <div class="container">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
                                    <div class="form-group col-sm-8">
                                        <div class="form-group col-sm-6" style="padding-bottom: 8px">
                                            <select id="select_p_number" title="选择prompt"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                                {% for prompt_type in ret["prompt_type_list"] %}
                                                    <option value="{{ prompt_type }}">{{ prompt_type }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
<!--                                        <div class="form-group col-sm-4" style="padding-bottom: 8px">-->
<!--                                            <select id="select_exist_flag" title="评测状态"-->
<!--                                                    class="selectpicker show-tick form-control"-->
<!--                                                    multiple data-max-options="1"-->
<!--                                                    data-live-search="true">-->
<!--                                                    <option value="0">未评测</option>-->
<!--                                                    <option value="1">已评测</option>-->
<!--                                            </select>-->
<!--                                        </div>-->
                                        <input style="display: none" name="final_select_p_number" id="final_select_p_number">
<!--                                        <input style="display: none" name="final_select_exist_flag" id="final_select_exist_flag">-->
                                    </div>
                                </div>
                                <br><br>
                                <div class="form-group col-sm-12">
<!--                                    <div class="form-group col-sm-6" style="width:50%">-->
<!--                                        <input type="text" class="form-control" name="qa_question" id="qa_question"-->
<!--                                               placeholder="问题" style="width:100%">-->
<!--&lt;!&ndash;                                        <input type="text" class="form-control" name="qa_answer" id="qa_answer"&ndash;&gt;-->
<!--&lt;!&ndash;                                               placeholder="答案">&ndash;&gt;-->
<!--                                    </div>-->
                                    <button type="submit" class="btn btn-default" id="button_find_multi" onclick="find()">检索</button>
<!--                                    <button type="submit" class="btn btn-default" id="button_find_multi2" onclick="update_index()">更新知识库</button>-->
<!--                                    <button type="submit" class="btn btn-default" id="button_find_multi1" onclick="page_post(1,1,1)">强检索</button>-->
<!--                                    <button type="submit" class="btn btn-default" id="button_find_multi3" onclick="page_post(1,1)">新增</button>-->
<!--                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">创建任务</button>-->
<!--                                    <div class="form-group" id="total_num"></div>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                    <table class="bordered" style="width:100%">
                        <thead>
                        <tr id="title2">
                            <td style="width: 10%;">类型</td>
                            <td style="width: 20%;">任务名称</td>
                            <td>recordIds</td>
                            <td style="width: 15%;">创建时间</td>
                            <td style="width: 15%;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="reasonTableBody">
                        </tbody>
                    </table>
                </div>
                <div id="splitPage" class="col-sm-12 text-center"></div>
            </div>
        </div><!-- content -->
        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">创建任务</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                  <form>
                      <input style="display: none" name="edit_prompt_type" id="edit_prompt_type">
                      <div class="form-group">
                        <input type="text" class="form-control" name="task_name" id="task_name"
                                               placeholder="任务名称">
                          <input type="text" class="form-control" name="customer_id" id="customer_id"
                                               placeholder="customer_id">
                      </div>
                      <div class="form-group">
                        <label for="prompt" class="col-form-label">指令:</label>
                          <textarea class="form-control" rows="5" id="prompt" placeholder="指令中必须有{input}"
                                              name="prompt"></textarea>
                      </div>
                      <div class="form-group">
                        <label for="record_ids" class="col-form-label">recordIds:</label>
                          <textarea class="form-control" rows="5" id="record_ids" placeholder="recordIds，如：12,34,56"
                                              name="record_ids"></textarea>
                      </div>
                      <div class="form-group">
                          <input type="text" class="form-control" name="start_num" id="start_num"
                                               placeholder="开始位置(从0开始)">
                        <input type="text" class="form-control" name="total_num" id="total_num"
                               placeholder="数量">
<!--                          <label for="edit_answer" class="col-form-label">答案:</label>-->
<!--                          <textarea class="form-control" rows="5" id="edit_answer" placeholder="输入答案"-->
<!--                                              name="edit_answer"></textarea>-->
                      </div>
                      <div class="form-group">
                          <div class="form-group col-sm-4" style="padding-bottom: 8px">
                                            <select id="select_exist_flag" title="是否随机"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                                    <option value="0">否</option>
                                                    <option value="1">是</option>
                                            </select>
                                        </div>
                          <input style="display: none" name="final_select_exist_flag" id="final_select_exist_flag">
                      </div>

                    </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="button_edit_qa" onclick="run_task()">创建任务</button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal fade" id="exampleModal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document" style="width: 1200px;">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel2">任务详情</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
<div class="col-md-12 right-content" style="margin-bottom: 20px;">
    <table class="bordered" style="width:100%">
                        <thead>
                        <tr id="title1">
                            <td style="width: 15%;">任务名称</td>
                            <td style="width: 15%;">任务时间</td>
                            <td style="width: 10%;">患者ID</td>
                            <td>标注状态</td>
                            <td style="width: 15%;">标注时间</td>
                            <td style="width: 15%;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="meta_table_str" style="display:null;">
                        </tbody>
                    </table>
</div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              </div>
            </div>
          </div>
        </div>

    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
    function set_select_info() {
            $('#final_select_p_number').val($('#select_p_number  option:selected').text());
            $('#final_select_exist_flag').val($('#select_exist_flag  option:selected').text());
        }
    $("#select_p_number").change(function () {
            set_select_info();
        });
        $("#select_exist_flag").change(function () {
            set_select_info();
        });

        function find() {
            var prompt_type = $('#final_select_p_number').val();
            if (prompt_type===""){
                alert("类型不能为空!")
                return
            }
            $('#button_find_multi').attr("disabled", true);
            var testTbody = $("#reasonTableBody");
            testTbody.empty();
            var real_url = "/find_blsc_data";
            var json_dt = {"prompt_type": prompt_type}
            $.ajax({
                type: 'POST',
                url: real_url,
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi').attr("disabled", false);
                    if(data.flag<=0){
                        alert(data.xs)
                        return
                    }
                    xs = data.xs
                    if (xs) {
                        $.each(xs, function (i, list) {
                            var sid = 'row' + i
                            test = '<tr class="child-' + sid + '">' +
                                '<td>' + list.prompt_type + '</td>' +
                                '<td>' + list.task_name + '</td>' +
                                '<td>' + list.record_ids + '</td>' +
                                '<td>' + list.create_date + '</td>' +
                                '<td><button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal2" data-meta_table_str="'+list.task_detail_str+'">查看详情</button></td>' +
                                '</tr>'
                            testTbody.append(test);
                        });
                    }
                },
                error: function (message) {
                    $('#button_find_multi').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

        $('#exampleModal').on('show.bs.modal', function (event) {
          // var button = $(event.relatedTarget)
          // var main_property = button.data('main_property')
          // var range_rule_id = button.data('range_rule_id')
          // var modal = $(this)
          // modal.find('#edit_range_rule_id').val(range_rule_id)
          // modal.find('#edit_main_property').val(main_property)
        })
        $('#exampleModal2').on('show.bs.modal', function (event) {
          var button = $(event.relatedTarget)
          var meta_table_str = button.data('meta_table_str')
          var modal = $(this)
          modal.find('#meta_table_str').html(meta_table_str)
        })

        function run_task() {
            var task_name = $('#task_name').val();
            var start_num = $('#start_num').val();
            var total_num = $('#total_num').val();
            var customer_id = $('#customer_id').val();
            var record_ids = $('#record_ids').val();
            var prompt = $('#prompt').val();
            if (task_name===""){
                alert("任务名称不能为空！")
                return
            }
            if (prompt!=="" && prompt.search("{input}")===-1){
                alert("指令必须包含{input}！")
                return
            }
            var is_rank = $('#final_select_exist_flag').val();
            var prompt_type = $('#final_select_p_number').val();
            if (prompt_type===""){
                alert("请先选择prompt类型!")
                return
            }
            if (is_rank===""){
                alert("请选择是否随机!")
                return
            }
            $('#button_edit_qa').attr("disabled", true);
            var json_dt = {"prompt_type":prompt_type,"prompt":prompt,"is_rank": is_rank, "task_name": task_name,"start_num": start_num, "total_num": total_num,"customer_id":customer_id, "record_ids": record_ids}
            $.ajax({
                type: 'POST',
                url: '/update_task',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert(data.msg);
                    $("#exampleModal2").modal('hide');
                    find();
                },
                error: function (message) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }


    </script>
{% endblock %}