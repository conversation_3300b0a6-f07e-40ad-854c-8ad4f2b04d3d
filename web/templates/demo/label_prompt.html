{% extends "common/base.html" %}
{% block head_file %}
<style xmlns="http://www.w3.org/1999/html">
        body {
            padding-right: 0 !important;
        }
    </style>
    <script type="text/javascript">

    </script>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
    <script src="/static/js/bootstrapPager.js"></script>
{% endblock %}

{% block body %}
    <div id="content">
        <div class="container">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
                                    <div class="form-group col-sm-8">
                                        <div class="form-group col-sm-6" style="padding-bottom: 8px">
                                            <select id="select_department" title="选择科室"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                                {% for department in ret["department2prompt"] %}
                                                    <option value="{{ department }}">{{ department }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group col-sm-4" style="padding-bottom: 8px">
                                            <select id="select_prompt_type" title="选择模板"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                            </select>
                                        </div>
                                        <input style="display: none" name="final_select_department" id="final_select_department">
                                        <input style="display: none" name="final_select_prompt_type" id="final_select_prompt_type">
                                    </div>
                                </div>
                                <br><br>
                                <div class="form-group col-sm-12">
                                    <button type="submit" class="btn btn-default" id="button_find_multi" onclick="find()">检索</button>
                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">创建任务</button>
<!--                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal3">查看当前模版</button>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                    <table class="bordered" style="width:100%">
                        <thead>
                        <tr id="title2">
                            <td style="width: 15%;">科室</td>
                            <td style="width: 10%;">模版</td>
                            <td style="width: 15%;">任务名称</td>
                            <td>recordIds</td>
                            <td style="width: 10%;">是否上线</td>
                            <td style="width: 15%;">创建时间</td>
                            <td style="width: 15%;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="reasonTableBody">
                        </tbody>
                    </table>
                </div>
                <div id="splitPage" class="col-sm-12 text-center"></div>
            </div>
        </div><!-- content -->
        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document" style="width: 1200px;">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">创建任务</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                  <form>
                      <input style="display: none" name="edit_prompt_type" id="edit_prompt_type">
                      <div class="form-group">
                        <input type="text" class="form-control" name="task_name" id="task_name"
                                               placeholder="任务名称">
                          <input type="text" class="form-control" name="customer_id" id="customer_id"
                                               placeholder="customer_id">
                      </div>
                      <div class="form-group">
                        <input type="text" class="form-control" name="record_date" id="record_date"
                                               placeholder="输入生成的日期">
                          <input type="text" class="form-control" name="prompt_extend" id="prompt_extend"
                                               placeholder="输入拟采用的麻醉方式及术式">
                      </div>
                      <div class="form-group">
                        <label for="prompt" class="col-form-label">prompt模版:</label>
                          <textarea class="form-control" rows="8" id="prompt" placeholder="指令中必须有{input}"
                                              name="prompt"></textarea>
                      </div>
                      <div class="form-group">
                        <label for="record_ids" class="col-form-label">recordIds:</label>
                          <textarea class="form-control" rows="2" id="record_ids" placeholder="recordIds，如：12,34,56"
                                              name="record_ids"></textarea>
                      </div>
                      <div class="form-group">
                          <input type="text" class="form-control" name="start_num" id="start_num"
                                               placeholder="开始位置(从0开始)">
                        <input type="text" class="form-control" name="total_num" id="total_num"
                               placeholder="数量">
<!--                          <label for="edit_answer" class="col-form-label">答案:</label>-->
<!--                          <textarea class="form-control" rows="5" id="edit_answer" placeholder="输入答案"-->
<!--                                              name="edit_answer"></textarea>-->
                      </div>
                      <div class="form-group">
                          <div class="form-group col-sm-4" style="padding-bottom: 8px">
                                            <select id="select_is_rank" title="是否随机"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                                    <option value="0">否</option>
                                                    <option value="1">是</option>
                                            </select>
                                        </div>
                          <input style="display: none" name="final_select_is_rank" id="final_select_is_rank">
                          <input style="display: none" name="prompt_type_code" id="prompt_type_code">
                      </div>

                    </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="button_edit_qa" onclick="run_task()">创建任务</button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal fade" id="exampleModal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document" style="width: 1200px;">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel2">任务详情</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
<div class="col-md-12 right-content" style="margin-bottom: 20px;">
    <table class="bordered" style="width:100%">
                        <thead>
                        <tr id="title1">
                            <td style="width: 15%;">任务名称</td>
                            <td style="width: 15%;">任务时间</td>
                            <td style="width: 10%;">患者ID</td>
                            <td>标注状态</td>
                            <td style="width: 15%;">标注时间</td>
                            <td style="width: 15%;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="meta_table_str" style="display:null;">
                        </tbody>
                    </table>
</div>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
              </div>
            </div>
          </div>
        </div>

        <div class="modal fade" id="exampleModal3" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document" style="width: 1200px;">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel3">上线操作</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                  <form>
                      <div class="form-group">
                        <label for="use_departments" class="col-form-label">当前模版上线的科室:</label>
                          <textarea class="form-control" rows="3" id="use_departments"
                                              name="use_departments"></textarea>
                      </div>
                      <input style="display: none" name="online_task_id" id="online_task_id">
                        <select id="select_online_department" title="选择上线或下线的科室"
                                                    class="selectpicker show-tick form-control"
                                                    multiple
                                                    data-live-search="true">
                                                {% for department in ret["department2prompt"] %}
                                                    <option value="{{ department }}">{{ department }}</option>
                                                {% endfor %}
                                            </select>
                      <input style="display: none" name="final_select_online_department" id="final_select_online_department">
                    </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="button_online_1" onclick="apply_dep(1,1)">上线到当前科室</button>
                <button type="button" class="btn btn-primary" id="button_online_2" onclick="apply_dep(1,2)">上线到所有开放科室</button>
                <button type="button" class="btn btn-primary" id="button_online_3" onclick="apply_dep(1,3)">上线到指定科室</button>
                  <br>
                <button type="button" class="btn btn-primary" id="button_offline_1" onclick="apply_dep(2,1)">下线当前科室</button>
                <button type="button" class="btn btn-primary" id="button_offline_2" onclick="apply_dep(2,2)">下线所有开放科室</button>
                <button type="button" class="btn btn-primary" id="button_offline_3" onclick="apply_dep(2,3)">下线指定科室</button>
              </div>
            </div>
          </div>
        </div>

    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
        var data_str = "{{ ret.department2prompt|safe }}";
        data_str = data_str.replace(/'/g, '"');
        var department2prompt = $.parseJSON(data_str);
        function set_select_info() {
            var department = $('#select_department  option:selected').text()
            $('#final_select_department').val(department);
            $("#select_prompt_type").empty();
            if (department!=="") {
                for (var i=0,len=department2prompt[department].length; i<len; i++) {
                    var pt = department2prompt[department][i];
                    $("#select_prompt_type").append("<option value='" + pt + "'>" + pt + "</option>");
                }
            }
            $("#select_prompt_type").selectpicker('refresh');

        }
        function set_select_info2() {
            $('#final_select_prompt_type').val($('#select_prompt_type  option:selected').text());
            $('#final_select_is_rank').val($('#select_is_rank  option:selected').text());
        }
        function set_select_info3() {
            var department_str = "";
            var select_list = $('#select_online_department  option:selected')
            for (var i = 0; i < select_list.length; i++) {
                if (i===0){
                    department_str = select_list[i].value;
                }else {
                    department_str += ","+select_list[i].value;
                }
            }
            $('#final_select_online_department').val(department_str);
        }
        $("#select_department").change(function () {
            set_select_info();
        });
        $("#select_online_department").change(function () {
            set_select_info3();
        });
        $("#select_prompt_type").change(function () {
            set_select_info2();
        });
        $("#select_is_rank").change(function () {
            set_select_info2();
        });
        function find() {
            var department_name = $('#final_select_department').val();
            var prompt_type = $('#final_select_prompt_type').val();
            if (prompt_type===""){
                alert("类型不能为空!")
                return
            }
            $('#button_find_multi').attr("disabled", true);
            var testTbody = $("#reasonTableBody");
            testTbody.empty();
            var real_url = "/find_prompt_task_data";
            var json_dt = {"department_name": department_name, "prompt_type": prompt_type}
            $.ajax({
                type: 'POST',
                url: real_url,
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi').attr("disabled", false);
                    if(data.flag<=0){
                        alert(data.xs)
                        return
                    }
                    xs = data.xs
                    if (xs) {
                        $.each(xs, function (i, list) {
                            var sid = 'row' + i
                            test = '<tr class="child-' + sid + '">' +
                                '<td>' + list.department_name + '</td>' +
                                '<td>' + list.prompt_type + '</td>' +
                                '<td>' + list.task_name + '</td>' +
                                '<td>' + list.record_ids + '</td>' +
                                '<td>' + list.is_use + '</td>' +
                                '<td>' + list.create_date + '</td>' +
                                '<td><button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal2" data-meta_table_str="'+list.task_detail_str+'">查看详情</button><button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal3" data-use_departments="'+list.use_departments+'" data-online_task_id="'+list.task_id+'">上线</button></td>' +
                                '</tr>'
                            testTbody.append(test);
                        });
                    }
                },
                error: function (message) {
                    $('#button_find_multi').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

        $('#exampleModal').on('show.bs.modal', function (event) {
            var department_name = $('#final_select_department').val();
            var prompt_type = $('#final_select_prompt_type').val();
            if (prompt_type===""){
                alert("请先选择prompt类型!")
                return
            }
            var modal = $(this)
            var json_dt = {"department_name":department_name,"prompt_type": prompt_type}
            $.ajax({
                type: 'POST',
                url: '/find_current_prompt',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    modal.find('#prompt').val(data.prompt)
                    modal.find('#prompt_type_code').val(data.prompt_type_code)
                },
                error: function (message) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        })

        $('#exampleModal2').on('show.bs.modal', function (event) {
          var button = $(event.relatedTarget)
          var meta_table_str = button.data('meta_table_str')
          var modal = $(this)
          modal.find('#meta_table_str').html(meta_table_str)
        })
        $('#exampleModal3').on('show.bs.modal', function (event) {
            var button = $(event.relatedTarget)
            var online_task_id = button.data('online_task_id')
            var use_departments = button.data('use_departments')
            var modal = $(this)
            modal.find('#online_task_id').val(online_task_id)
            modal.find('#use_departments').val(use_departments)
        })

        function run_task() {
            var task_name = $('#task_name').val();
            var start_num = $('#start_num').val();
            var total_num = $('#total_num').val();
            var customer_id = $('#customer_id').val();
            var record_ids = $('#record_ids').val();
            var prompt = $('#prompt').val();
            var prompt_extend = $('#prompt_extend').val();
            var record_date = $('#record_date').val();
            var prompt_type_code = $('#prompt_type_code').val();
            if (task_name===""){
                alert("任务名称不能为空！")
                return
            }
            if (prompt!=="" && prompt.search("{input}")===-1){
                alert("指令必须包含{input}！")
                return
            }
            var is_rank = $('#final_select_is_rank').val();
            var department_name = $('#final_select_department').val();
            var prompt_type = $('#final_select_prompt_type').val();
            if (prompt_type===""){
                alert("请先选择prompt类型!")
                return
            }
            if (is_rank===""){
                alert("请选择是否随机!")
                return
            }
            $('#button_edit_qa').attr("disabled", true);
            var json_dt = {"record_date":record_date,"prompt_type_code": prompt_type_code, "prompt_extend":prompt_extend,"department_name":department_name,"prompt_type": prompt_type, "prompt":prompt,"is_rank": is_rank, "task_name": task_name,"start_num": start_num, "total_num": total_num,"customer_id":customer_id, "record_ids": record_ids}
            $.ajax({
                type: 'POST',
                url: '/update_prompt_task',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert(data.msg);
                    $("#exampleModal").modal('hide');
                    find();
                },
                error: function (message) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

        function apply_dep(apply_type,department_type) {
            var departments = $('#final_select_online_department').val();
            if (department_type === 3 && departments==="") {
                alert("科室不能为空!")
                return
            }
            if (apply_type === 1) {
                $('#button_online_'+department_type).attr("disabled", true);
            }else{
                $('#button_offline_'+department_type).attr("disabled", true);
            }
            var prompt_type = $('#final_select_prompt_type').val();
            if (prompt_type===""){
                alert("请先选择prompt类型!")
                return
            }
            var task_id = $('#online_task_id').val();
            var department_name = $('#final_select_department').val();
            if (department_type === 1) {
                departments = department_name
            }
            json_dt = {'task_id':task_id,'apply_type':apply_type,'department_type':department_type,"prompt_type": prompt_type,'departments':departments}
            $.ajax({
                type: 'POST',
                url: '/update_prompt_status',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    if (apply_type === 1) {
                        $('#button_online_'+department_type).attr("disabled", false);
                    }else{
                        $('#button_offline_'+department_type).attr("disabled", false);
                    }
                    alert(data.msg);
                    $("#exampleModal3").modal('hide');
                    find();
                },
                error: function (message) {
                    if (apply_type === 1) {
                        $('#button_online_'+department_type).attr("disabled", false);
                    }else{
                        $('#button_offline_'+department_type).attr("disabled", false);
                    }
                    alert("请求失败，请检查参数");
                }
            });
        }

    </script>
{% endblock %}