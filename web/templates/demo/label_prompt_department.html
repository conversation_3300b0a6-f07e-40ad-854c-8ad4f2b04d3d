{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
{% endblock %}

{% block body %}
    <div id="content">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    {% if ret.is_same == 1 %}
                                    <h4>客户端与大模型开放的科室已同步</h4>
                                    {% endif %}
                                    {% if ret.is_same == 0 %}
                                    <h4>客户端与大模型开放的科室不同，请同步！</h4>
                                    <button type="submit" class="btn btn-default" id="button_find_multi_3" onclick="update_department()">同步大模型科室</button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title22">
                                <td style="width: 50%;">客户端开放的科室</td>
                                <td style="width: 50%;">大模型开放的科室</td>
                            </tr>
                            </thead>
                            <tbody id="reasonTableBody2">
                            <tr>
                                <td>{{ ret.cdss_department_str|safe }}</td>
                                <td>{{ ret.llm_department_str|safe }}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
        function update_department() {
            $('#button_find_multi_3').attr("disabled", true);
            var json_dt = {"cc":"dd"}
            $.ajax({
                type: 'POST',
                url: '/update_prompt_department',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi_3').attr("disabled", false);
                    alert(data.msg);
                },
                error: function (message) {
                    $('#button_find_multi_3').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }
    </script>
{% endblock %}