{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
{% endblock %}

{% block body %}
    <div id="content">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
                                    <div class="form-group col-sm-8">
                                        <div class="form-group col-sm-6" style="padding-bottom: 8px">
                                            <select id="select_p_number" title="选择需要比较的prompt"
                                                    class="selectpicker show-tick form-control"
                                                    multiple
                                                    data-live-search="true">
                                                {% for label_detail in ret.label_detail_list %}
                                                    <option value="{{ label_detail.label_detail_id }}">{{ label_detail.task_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                <div class="col-sm-12">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title2">
                                <td style="width: 10%;">version</td>
                                <td>prompt</td>
                                <td style="width: 30%;">answer</td>
                                <td style="width: 30%;">label answer</td>
                            </tr>
                            </thead>
                            <tbody id="taskDetailBody">
                            {% for label_detail in ret.label_detail_list %}
                            <tr id="tr_{{label_detail.label_detail_id}}">
                                <td>{{ label_detail.prompt_version|safe }}</td>
                                <td>{{ label_detail.instruction|safe }}</td>
                                <td>{{ label_detail.llm_answer|safe }}</td>
                                <td>{{ label_detail.label_answer|safe }}</td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                </div>

        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
        function set_select_info() {
            // var row = $("#grid tr:visible").length; //获取table中所有tr的行数
            var trs = $("#taskDetailBody tr");//grid是table的id，获取table下所有的tr
            trs.hide();
            var select_list = $('#select_p_number  option:selected')
            for (var i = 0; i < select_list.length; i++) {
                var label_detail_id = select_list[i].value;
                $("#tr_"+label_detail_id).show();
            }
        }
    $("#select_p_number").change(function () {
            set_select_info();
        });
    </script>
{% endblock %}