{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
{% endblock %}

{% block body %}
    <div id="content">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
<!--                                    <div class="form-group col-sm-6" style="width:50%">-->
<!--                                        <input type="text" class="form-control" name="qa_question" id="qa_question"-->
<!--                                               placeholder="问题" style="width:100%">-->
<!--&lt;!&ndash;                                        <input type="text" class="form-control" name="qa_answer" id="qa_answer"&ndash;&gt;-->
<!--&lt;!&ndash;                                               placeholder="答案">&ndash;&gt;-->
<!--                                    </div>-->
                                    <button type="submit" {% if ret.task_detail.label_flag == ret.generate_cyjl_list[1].label_flag %} style="color: red;" {% endif %} class="btn btn-default" id="button_find_multi_{{ret.generate_cyjl_list[1].label_flag}}" onclick="label({{ret.task_detail.id}},{{ret.generate_cyjl_list[1].label_flag}})">👈🏻A is better</button>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    <button type="submit" {% if ret.task_detail.label_flag == ret.generate_cyjl_list[2].label_flag %} style="color: red;" {% endif %} class="btn btn-default" id="button_find_multi_{{ret.generate_cyjl_list[2].label_flag}}" onclick="label({{ret.task_detail.id}},{{ret.generate_cyjl_list[2].label_flag}})">👉🏻B is better</button>
                                    &nbsp;&nbsp;&nbsp;&nbsp;
                                    <button type="submit" {% if ret.task_detail.label_flag == 3 %} style="color: red;" {% endif %} class="btn btn-default" id="button_find_multi_3" onclick="label({{ret.task_detail.id}},3)">🤝Tie</button>
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <button type="submit" class="btn btn-default" id="button_find_multi_33" onclick="window.open('/to_task_detail_compare?customer_id={{ret.task_detail.customer_id}}&record_id={{ret.task_detail.record_id}}&prompt_type={{ret.task_detail.prompt_type}}')">对比prompt</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                    <div class="col-sm-6">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title22">
                                <td style="width: 20%;">类型</td>
                                <td style="width: 80%;">内容</td>
                            </tr>
                            </thead>
                            <tbody id="reasonTableBody2">
                            {% for cyjl in ret.generate_cyjl_list %}
                            <tr>
                                <td>{{ cyjl.progress_type_zh|safe }}</td>
                                <td>{{ cyjl.progress_text|safe }}</td>
                            </tr>
                            {% endfor %}
                            <tr id="title332">
                                <td><button type="submit" class="btn btn-default" id="button_find_multi_10" onclick="label({{ret.task_detail.id}},10)">保存</button></td>
                                <td>输入标注的答案：<br/><textarea style="min-width: 95%;max-width: 95%;" rows="5" id="label_answer" placeholder="输入标注的答案"
                                              name="label_answer">{{ret.task_detail.label_answer}}</textarea>
                                    <br/>输入备注信息：<br/>
                                    <textarea style="min-width: 95%;max-width: 95%;" rows="5" id="label_remark" placeholder="输入备注信息"
                                              name="label_remark">{{ret.task_detail.label_remark}}</textarea></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title2">
                                <td style="width: 20%;">文书类型</td>
                                <td style="width: 80%;">原始文书</td>
                            </tr>
                            </thead>
                            <tbody id="reasonTableBody">
                            {% for origin_text in ret.origin_text_list %}
                            <tr>
                                <td>{{ origin_text.progress_type_zh|safe }}</td>
                                <td>{{ origin_text.progress_text|safe }}</td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                </div>

        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
        function label(task_detail_id,label_flag) {
            $('#button_find_multi_'+label_flag).attr("disabled", true);
            var json_dt = {'task_detail_id':task_detail_id,'label_flag':label_flag}
            if (label_flag === 10) {
                var label_remark = $('#label_remark').val();
                var label_answer = $('#label_answer').val();
                json_dt = {'task_detail_id':task_detail_id,'label_remark':label_remark,'label_answer':label_answer}
            }
            $.ajax({
                type: 'POST',
                url: '/update_task_detail',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi_'+label_flag).attr("disabled", false);
                    $('#button_find_multi_'+label_flag).css('color','red');
                    alert(data.msg);
                },
                error: function (message) {
                    $('#button_find_multi_'+label_flag).attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }
    </script>
{% endblock %}