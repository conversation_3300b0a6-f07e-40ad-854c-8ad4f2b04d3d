{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <script type="text/javascript">

    </script>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
{% endblock %}

{% block body %}
    <div id="content">
        <div class="container">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-6">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="col-sm-12" style="padding-top: 20px">
                                    <textarea style="min-width: 90%;max-width: 90%;" rows="20" id="prompt" placeholder="假设你是医生。下面是一份病历，请基于患者症状、病史等信息预测初步诊断、诊断依据、鉴别诊断。主诉：胸痛三天。现病史：。专科情况：。"
                                              name="prompt"></textarea>
                                </div>
                                <div class="form-group col-sm-12">
                                    <input type="text" class="form-control" name="max_length" id="max_length"
                                               placeholder="最大长度" value="2048">
                                    <button type="submit" class="btn btn-default" id="button_find_multi" onclick="find_reason_multi()">生成鉴别诊断</button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
<div class="col-sm-6">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title331">
                                <td style="width: 100%;" id="differential_diagnosis"></td>
                            </tr>

                            </thead>
                        </table>
                    </div>
                </div>
            </div>
        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">

        function find_reason_multi() {
            var prompt = $('#prompt').val();
            var max_length = $('#max_length').val();
            if (prompt===""){
                alert("prompt不为空!")
                return
            }
            $('#button_find_multi').attr("disabled", true);
            var json_dt = {"prompt": prompt, "max_length": max_length}
            $.ajax({
                type: 'POST',
                url: '/find_diagnosis',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi').attr("disabled", false);
                    $("#differential_diagnosis").html(data.differential_diagnosis)
                },
                error: function (message) {
                    $('#button_find_multi').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

    </script>
{% endblock %}