{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
{% endblock %}

{% block body %}
    <div id="content">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-4">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title22">
                                <td style="width: 20%;">出院小结</td>
                                <td style="width: 80%;">生成的内容</td>
                            </tr>
                            </thead>
                            <tbody id="reasonTableBody2">
                            {% for cyjl in ret.generate_cyjl_list %}
                            <tr>
                                <td>{{ cyjl.attribute_name|safe }}</td>
                                <td>{{ cyjl.progress_content|safe }}</td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="col-sm-8">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title2">
                                <td style="width: 20%;">文书类型</td>
                                <td style="width: 80%;">原始文书</td>
                            </tr>
                            </thead>
                            <tbody id="reasonTableBody">
                            {% for origin_text in ret.origin_text_list %}
                            <tr>
                                <td>{{ origin_text.progress_type_zh|safe }}</td>
                                <td>{{ origin_text.progress_text|safe }}</td>
                            </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
    </script>
{% endblock %}