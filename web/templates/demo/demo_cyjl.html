{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <script type="text/javascript">

    </script>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
    <script src="/static/js/bootstrapPager.js"></script>
{% endblock %}

{% block body %}
    <div id="content">
        <div class="container">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
                                    <div class="form-group">
                                        <input type="text" class="form-control" name="record_id" id="record_id"
                                               placeholder="患者recordid">
                                        <input type="text" class="form-control" name="start_time" id="start_time"
                                               placeholder="开始时间">
                                        <input type="text" class="form-control" name="end_time" id="end_time"
                                               placeholder="结束时间">
                                        <input type="text" class="form-control" name="department" id="department"
                                               placeholder="科室名称">
                                    </div>
                                    <button type="submit" class="btn btn-default" id="button_find_multi" onclick="page_post(1,1)">提交</button>
                                    <div class="form-group" id="total_num"></div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                    <table class="bordered" style="width:100%">
                        <thead>
                        <tr id="title2">
                            <td style="width: 10%;">customer_id</td>
                            <td style="width: 10%;">record_id</td>
                            <td style="width: 20%;">科室</td>
                            <td style="width: 10%;">年龄</td>
                            <td style="width: 10%;">单位</td>
                            <td style="width: 10%;">性别</td>
                            <td style="width: 20%;">出院时间</td>
                            <td style="width: 10%;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="reasonTableBody">
                        </tbody>
                    </table>
                </div>
                <div id="splitPage" class="col-sm-12 text-center"></div>
            </div>
        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
    function set_select_info() {
            $('#final_select_p_number').val($('#select_p_number  option:selected').text());
            $('#final_select_exist_flag').val($('#select_exist_flag  option:selected').text());
        }
    $("#select_p_number").change(function () {
            set_select_info();
        });
        $("#select_exist_flag").change(function () {
            set_select_info();
        });

        var page = 0;
        var pageSize = 50;
        var totalCount = 0;
        function page_post(page, count_flag=0) {
            var department = $('#department').val();
            var record_id = $('#record_id').val();
            var start_time = $('#start_time').val();
            var end_time = $('#end_time').val();
            $('#button_find_multi').attr("disabled", true);
            var testTbody = $("#reasonTableBody");
            testTbody.empty();
            var splitPage = $("#splitPage");
            splitPage.empty();
            if(count_flag===1){
                $("#total_num").html("")
            }
            var json_dt = {"start_time": start_time, "end_time": end_time, "record_id": record_id, "department": department, "page": page, "page_size": pageSize, "count_flag": count_flag}
            $.ajax({
                type: 'POST',
                url: '/find_record_data',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi').attr("disabled", false);
                    if(data.flag==0){
                        alert(data.xs)
                        return
                    }
                    xs = data.xs
                    if (xs) {
                        $.each(xs, function (i, list) {
                            var sid = 'row' + i
                            test = '<tr class="child-' + sid + '">' +
                                '<td>' + list.customer_id + '</td>' +
                                '<td>' + list.record_id + '</td>' +
                                '<td>' + list.inpatient_department + '</td>' +
                                '<td>' + list.patient_age + '</td>' +
                                '<td>' + list.patient_age_type + '</td>' +
                                '<td>' + list.patient_gender + '</td>' +
                                '<td>' + list.discharge_time + '</td>' +
                                '<td><a href="/to_demo_cyjl_detail?record_id='+list.record_id+'&customer_id='+list.customer_id+'" target="_blank">生成病历</a></td>' +
                                '</tr>'
                            testTbody.append(test);
                        });
                    }
                    if (data.all_count > -1){
                        totalCount = data.all_count
                        $("#total_num").html("&nbsp;&nbsp;共"+totalCount+"条")
                    }
                    splitPage.html(Pager({
                        totalCount:totalCount, 		//总条数为150
                        pageSize:pageSize,    			//每页显示6条内容，默认10
                        page:page,    			//每页显示6条内容，默认10
                        buttonSize:5,   		//显示6个按钮，默认10
                        pageParam:'page',   		//页码的参数名为'p'，默认为'page'
                        className:'pagination', //分页的样式
                        prevButton:'上一页',     //上一页按钮
                        nextButton:'下一页',     //下一页按钮
                        firstButton:'首页',      //第一页按钮
                        lastButton:'末页',       //最后一页按钮
                    }));
                },
                error: function (message) {
                    $('#button_find_multi').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

    </script>
{% endblock %}