<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>惠每-大模型</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/bootstrap-theme.min.css">
    <link rel="stylesheet" href="/static/css/bootstrap-select.min.css">
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
    <script src="/static/js/bootstrap-select.min.js"></script>
    <script src="/static/js/i18n/defaults-zh_CN.js"></script>


    {% block head_file %}
    {% endblock %}
</head>

<body>
<nav class="navbar navbar-default" role="navigation">
    <div class="container-fluid">
        <div class="navbar-header">
            <a class="navbar-brand" href="/">惠每大模型</a>
        </div>
        <div>
            <ul class="nav navbar-nav" id="head_nav">
<!--                <li {% if request.endpoint and request.endpoint == "to_qa_label" %} class="active" {% endif %}><a href="to_qa_label">知识库</a></li>-->
<!--                <li {% if request.endpoint and request.endpoint == "to_demo_cyjl" %} class="active" {% endif %}><a href="to_demo_cyjl">出院记录demo</a></li>-->
<!--                <li {% if request.endpoint and request.endpoint == "to_demo_diagnosis" %} class="active" {% endif %}><a href="to_demo_diagnosis">鉴别诊断demo</a></li>-->
                <li {% if request.endpoint and request.endpoint == "label_task.to_prompt_department" %} class="active" {% endif %}><a href="to_prompt_department">大模型科室配置</a></li>
<!--                <li {% if request.endpoint and request.endpoint == "label_task.to_label_cyjl" %} class="active" {% endif %}><a href="to_label_cyjl">出院记录评测</a></li>-->
                <li {% if request.endpoint and request.endpoint == "label_task.to_label_prompt" %} class="active" {% endif %}><a href="to_label_prompt">配置科室模板</a></li>
                <li {% if request.endpoint and request.endpoint == "label_task.to_label_blsc" %} class="active" {% endif %}><a href="to_label_blsc">病历生成sbs</a></li>
            </ul>
        </div>
    </div>
</nav>
{#<div id="box" class="box">#}
{#        <div class="box-in"></div>#}
{#    </div>#}

{% block body %}
{% endblock %}


{% block body_file %}
{% endblock %}
</body>
</html>
