<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title>惠每-大模型</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/bootstrap-theme.min.css">
    <link rel="stylesheet" href="/static/css/bootstrap-select.min.css">
    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/bootstrap.min.js"></script>
    <script src="/static/js/bootstrap-select.min.js"></script>
    <script src="/static/js/i18n/defaults-zh_CN.js"></script>
    <script src="/static/js/crypto-js.js"></script>

<style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <script type="text/javascript">

    </script>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
</head>

<body>
    <div id="content">
        <div class="container">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-6">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12 text-center">
                                    <h1>惠每大模型</h1>
                                </div>
                                <br><br><br><br>
                                <div class="form-group col-sm-12">
                                    <div class="form-group">
                                        <label class="sr-only" for="user_name">用户名</label>
                                        <input type="text" class="form-control" name="user_name" id="user_name"
                                               placeholder="用户名">
                                        </br>
                                        <input type="password" class="form-control" name="password" id="password"
                                               placeholder="密码">
                                        </br>
                                        <input type="text" class="form-control" name="customer_id" id="customer_id"
                                               placeholder="customer_id">
                                    </div>
                                    </br>
                                    <button type="submit" class="btn btn-default" id="button_login" onclick="login()">登录</button>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>

            </div>
        </div><!-- content -->
    </div>
    <script type="text/javascript">

        function login() {
            var user_name = $('#user_name').val();
            if (user_name===""){
                alert("用户名不能为空!")
                return
            }
            if ($('#password').val()===""){
                alert("密码不能为空!")
                return
            }
            if ($('#customer_id').val()===""){
                alert("customer_id不能为空!")
                return
            }
            $('#button_login').attr("disabled", true);
            var json_dt = {"user_name": user_name, "password": $('#password').val(), "customer_id": $('#customer_id').val()}
            $.ajax({
                type: 'POST',
                url: '/login',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_login').attr("disabled", false);
                    if(data.flag==0){
                        alert(data.msg)
                        return
                    }
                    window.location.href="/to_prompt_department";
                },
                error: function (message) {
                    $('#button_login').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

    </script>
</body>
</html>