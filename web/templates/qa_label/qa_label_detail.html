{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
{% endblock %}

{% block body %}
    <div id="content">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">

                <div class="col-sm-12">
                    <div class="col-sm-12" style="max-height:800px;overflow:auto">
                        <table class="bordered" style="width:100%">
                            <thead>
                            <tr id="title2">
                                <td style="width: 10%;">类型</td>
                                <td style="width: 40%;">问题</td>
                                <td style="width: 50%;">答案</td>
                            </tr>
                            <tr id="title33">
                                <td>原始</td>
                                <td>{{ret.qa_label_detail.origin_question}}</td>
                                <td>{{ret.qa_label_detail.origin_answer}}</td>
                            </tr>
                            <tr id="title332">
                                <td>修正</td>
                                <td><textarea style="min-width: 95%;max-width: 95%;" rows="5" id="real_question" placeholder="输入备注信息"
                                              name="real_question">{{ret.qa_label_detail.real_question}}</textarea></td>
                                <td><textarea style="min-width: 95%;max-width: 95%;" rows="10" id="real_answer" placeholder="输入备注信息"
                                              name="real_answer">{{ret.qa_label_detail.real_answer}}</textarea></td>
                            </tr>
                            </thead>
                        </table>
                    </div>
<!--                    <div class="col-sm-6" style="max-height:800px;overflow:auto">-->
<!--                        <table class="bordered" style="width:100%">-->
<!--                            <thead>-->
<!--                            <tr id="title330">-->
<!--                                <td style="width: 100%;">{{ret.origin_answer}}</td>-->
<!--                            </tr>-->
<!--                            <tr id="title3322">-->
<!--                                <td style="width: 100%;"><textarea style="min-width: 95%;max-width: 95%;" rows="10" id="real_answer" placeholder="输入备注信息"-->
<!--                                              name="real_answer">{{ret.real_answer}}</textarea></td>-->
<!--                            </tr>-->
<!--                            </thead>-->
<!--                        </table>-->
<!--                    </div>-->
                </div>
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-2" style="padding-bottom: 8px;float:left">
                                </div>
                                <div class="form-group col-sm-2" style="padding-bottom: 8px"></div>
                                <div class="form-group col-sm-2" style="padding-bottom: 8px">
                                    <button type="submit" class="btn btn-default" id="button_edit_qa" onclick="edit_qa({{ret.qa_label_detail.id}})">保存</button>
                                </div>
                                <div class="form-group col-sm-2" style="padding-bottom: 8px">
                                </div>
                                <div class="form-group col-sm-2" style="padding-bottom: 8px"></div>
                                <div class="form-group col-sm-2" style="padding-bottom: 8px;float:right">
                                </div>


                            </div>
                        </div>
                    </div>
                </div>
        </div><!-- content -->
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
        function edit_qa(id) {
            var real_question = $('#real_question').val();
            var real_answer = $('#real_answer').val();
            if (real_question===""){
                alert("问题不能为空！")
                return
            }
            if (real_answer===""){
                alert("答案不能为空！")
                return
            }
            $('#button_edit_qa').attr("disabled", true);
            var json_dt = {"qa_question": real_question, "qa_answer": real_answer, "id": id}
            $.ajax({
                type: 'POST',
                url: '/edit_qa_label',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert(data.msg);
                },
                error: function (message) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

    </script>
{% endblock %}