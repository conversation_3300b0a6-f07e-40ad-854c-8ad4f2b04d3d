{% extends "common/base.html" %}
{% block head_file %}
    <style>
        body {
            padding-right: 0 !important;
        }
    </style>
    <script type="text/javascript">

    </script>
    <link rel="stylesheet" href="/static/css/raw-table.css"/>
    <script src="/static/js/bootstrapPager.js"></script>
{% endblock %}

{% block body %}
    <div id="content">
        <div class="container">
            <div class="col-md-12 right-content" style="margin-bottom: 20px;">
                <div class="col-sm-12">
                    <div class="col-sm-12">
                        <div class="form-inline">
                            <div class="col-sm-12">
                                <div class="form-group col-sm-12">
                                    <div class="form-group col-sm-8">
                                        <div class="form-group col-sm-6" style="padding-bottom: 8px">
                                            <select id="select_p_number" title="知识库类型选择"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                                {% for qa_type in ret["qa_type_list"] %}
                                                    <option value="{{ qa_type }}">{{ qa_type }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="form-group col-sm-4" style="padding-bottom: 8px">
                                            <select id="select_exist_flag" title="评测状态"
                                                    class="selectpicker show-tick form-control"
                                                    multiple data-max-options="1"
                                                    data-live-search="true">
                                                    <option value="0">未评测</option>
                                                    <option value="1">已通过</option>
                                                    <option value="2">未通过</option>
                                            </select>
                                        </div>
                                        <input style="display: none" name="final_select_p_number" id="final_select_p_number">
                                        <input style="display: none" name="final_select_exist_flag" id="final_select_exist_flag">
                                    </div>
                                </div>
                                <br><br>
                                <div class="form-group col-sm-12">
                                    <div class="form-group col-sm-6" style="width:50%">
                                        <input type="text" class="form-control" name="qa_question" id="qa_question"
                                               placeholder="问题" style="width:100%">
<!--                                        <input type="text" class="form-control" name="qa_answer" id="qa_answer"-->
<!--                                               placeholder="答案">-->
                                    </div>
                                    <button type="submit" class="btn btn-default" id="button_find_multi" onclick="page_post(1,1)">检索</button>
                                    <button type="submit" class="btn btn-default" id="button_find_multi2" onclick="update_index()">更新知识库</button>
                                    <button type="submit" class="btn btn-default" id="button_find_multi1" onclick="page_post(1,1,1)">强检索</button>
<!--                                    <button type="submit" class="btn btn-default" id="button_find_multi3" onclick="page_post(1,1)">新增</button>-->
                                    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#exampleModal">新增</button>
                                    <div class="form-group" id="total_num"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12" style="max-height:800px;overflow:auto">
                    <table class="bordered" style="width:100%">
                        <thead>
                        <tr id="title2">
                            <td style="width: 10%;">类型</td>
                            <td style="width: 25%;">问题</td>
                            <td>答案</td>
                            <td style="width: 8%;">状态</td>
                            <td style="width: 10%;">修改时间</td>
                            <td style="width: 8%;">操作</td>
                        </tr>
                        </thead>
                        <tbody id="reasonTableBody">
                        </tbody>
                    </table>
                </div>
                <div id="splitPage" class="col-sm-12 text-center"></div>
            </div>
        </div><!-- content -->
        <div class="modal fade" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">新增问答知识</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                  <form>
                      <input style="display: none" name="edit_main_property" id="edit_main_property">
                      <div class="form-group">
                        <label for="edit_question" class="col-form-label">问题:</label>
                          <textarea class="form-control" rows="5" id="edit_question" placeholder="输入问题"
                                              name="edit_question"></textarea>
                      </div>
                      <div class="form-group">
                          <label for="edit_answer" class="col-form-label">答案:</label>
                          <textarea class="form-control" rows="5" id="edit_answer" placeholder="输入答案"
                                              name="edit_answer"></textarea>
                      </div>
                    </form>
              </div>
              <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="button_edit_qa" onclick="save_qa()">保存</button>
              </div>
            </div>
          </div>
        </div>
    </div>
{% endblock %}

{% block body_file %}
    <script type="text/javascript">
    function set_select_info() {
            $('#final_select_p_number').val($('#select_p_number  option:selected').text());
            $('#final_select_exist_flag').val($('#select_exist_flag  option:selected').text());
        }
    $("#select_p_number").change(function () {
            set_select_info();
        });
        $("#select_exist_flag").change(function () {
            set_select_info();
        });

        var page = 0;
        var pageSize = 50;
        var totalCount = 0;
        function page_post(page, count_flag=0, is_high=0) {
            var qa_type = $('#final_select_p_number').val();
            var review_status = $('#final_select_exist_flag').val();
            if (is_high===0 && qa_type===""){
                alert("类型不能为空!")
                return
            }
            var qa_question = $('#qa_question').val();
            var qa_answer = $('#qa_answer').val();
            $('#button_find_multi').attr("disabled", true);
            $('#button_find_multi1').attr("disabled", true);
            var testTbody = $("#reasonTableBody");
            testTbody.empty();
            var splitPage = $("#splitPage");
            splitPage.empty();
            if(count_flag===1){
                $("#total_num").html("")
            }
            var real_url = "/find_qa_label_data";
            if(is_high===1){
                real_url = "/search";
                if (qa_question===""){
                alert("问题不能为空!")
                return
            }
            }
            var json_dt = {"qa_question": qa_question, "qa_answer": qa_answer, "review_status": review_status, "qa_type": qa_type, "page": page, "page_size": pageSize, "count_flag": count_flag}
            $.ajax({
                type: 'POST',
                url: real_url,
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi').attr("disabled", false);
                    $('#button_find_multi1').attr("disabled", false);
                    if(data.flag<=0){
                        alert(data.xs)
                        return
                    }
                    xs = data.xs
                    if (xs) {
                        $.each(xs, function (i, list) {
                            var sid = 'row' + i
                            test = '<tr class="child-' + sid + '">' +
                                '<td>' + list.qa_type + '</td>' +
                                '<td>' + list.real_question + '</td>' +
                                '<td>' + list.real_answer + '</td>' +
                                '<td>' + list.review_status_str + '</td>' +
                                '<td>' + list.modify_date + '</td>' +
                                '<td><a href="/to_qa_label_detail?id='+list.id+'" target="_blank">查看详情</a></td>' +
                                '</tr>'
                            testTbody.append(test);
                        });
                    }
                    if (data.all_count > -1){
                        totalCount = data.all_count
                        $("#total_num").html("&nbsp;&nbsp;共"+totalCount+"条")
                    }
                    splitPage.html(Pager({
                        totalCount:totalCount, 		//总条数为150
                        pageSize:pageSize,    			//每页显示6条内容，默认10
                        page:page,    			//每页显示6条内容，默认10
                        buttonSize:5,   		//显示6个按钮，默认10
                        pageParam:'page',   		//页码的参数名为'p'，默认为'page'
                        className:'pagination', //分页的样式
                        prevButton:'上一页',     //上一页按钮
                        nextButton:'下一页',     //下一页按钮
                        firstButton:'首页',      //第一页按钮
                        lastButton:'末页',       //最后一页按钮
                    }));
                },
                error: function (message) {
                    $('#button_find_multi').attr("disabled", false);
                    $('#button_find_multi1').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

        $('#exampleModal').on('show.bs.modal', function (event) {
          // var button = $(event.relatedTarget)
          // var main_property = button.data('main_property')
          // var range_rule_id = button.data('range_rule_id')
          // var modal = $(this)
          // modal.find('#edit_range_rule_id').val(range_rule_id)
          // modal.find('#edit_main_property').val(main_property)
        })

        function save_qa() {
            var qa_type = $('#final_select_p_number').val();
            if (qa_type===""){
                alert("类型不能为空!")
                return
            }
            var edit_question = $('#edit_question').val();
            var edit_answer = $('#edit_answer').val();
            if (edit_question===""){
                alert("问题不能为空！")
                return
            }
            if (edit_answer===""){
                alert("答案不能为空！")
                return
            }
            $('#button_edit_qa').attr("disabled", true);
            var json_dt = {"qa_question": edit_question, "qa_answer": edit_answer, "qa_type": qa_type}
            $.ajax({
                type: 'POST',
                url: '/save_qa_label',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert(data.msg);
                },
                error: function (message) {
                    $('#button_edit_qa').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

        function update_index() {
            $('#button_find_multi2').attr("disabled", true);
            var json_dt = {}
            $.ajax({
                type: 'POST',
                url: '/update_index',
                data: JSON.stringify(json_dt),
                contentType: 'application/json;charset=UTF-8',
                dataType: 'json',
                success: function (data) {
                    $('#button_find_multi2').attr("disabled", false);
                    alert(data.msg);
                },
                error: function (message) {
                    $('#button_find_multi2').attr("disabled", false);
                    alert("请求失败，请检查参数");
                }
            });
        }

    </script>
{% endblock %}