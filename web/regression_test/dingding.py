# coding:utf-8
import time
import hmac
import hashlib
import base64
import urllib.parse
import requests,json


def send_dingding_message(webhook, secret, message):
    timestamp = str(round(time.time() * 1000))
    secret_enc = secret.encode('utf-8')
    string_to_sign = '{}\n{}'.format(timestamp, secret)
    string_to_sign_enc = string_to_sign.encode('utf-8')
    hmac_code = hmac.new(secret_enc, string_to_sign_enc, digestmod=hashlib.sha256).digest()
    sign = urllib.parse.quote(base64.b64encode(hmac_code))
    headers={'Content-Type': 'application/json'}
    webhook = webhook + '&timestamp='+timestamp+"&sign="+sign
    data = {
        "msgtype": "text",
        "text": {"content": message},
        "at": {
                "atMobiles": [
                ],
                "isAtAll": 0                                        #如果需要@所有人，这些写1
            }}
    res = requests.post(webhook, data=json.dumps(data), headers=headers)
    print(message, res.text)


if __name__ == '__main__':
    webhook = 'https://oapi.dingtalk.com/robot/send?access_token=2f4f62b1fb345154d6817c0647fb58ce98a989affa47ee8b54b8ba54110da660'
    secret = 'SEC9dd5b657903083bc53501b69f97a3b400f701274bda645e24a908dd037022224'
    send_dingding_message(webhook,secret,'测试一下......')
