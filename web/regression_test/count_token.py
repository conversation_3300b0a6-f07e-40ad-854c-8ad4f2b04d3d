# coding:utf-8
from web.mrg_service.discharge_summary_llm import *
import json


from transformers import AutoTokenizer

glm_model_path = "/data/share_model/glm-4-9b-chat"
qwen_model_path = "/data/share_model/Qwen2-7B-Instruct"
# glm_model_path = "/Users/<USER>/huggingface_model/glm-4-9b-chat"
# qwen_model_path = "/Users/<USER>/huggingface_model/Qwen2-7B-Instruct"

# glm_tokenizer = AutoTokenizer.from_pretrained(glm_model_path, trust_remote_code=True)
# qwen_tokenizer = AutoTokenizer.from_pretrained(qwen_model_path)


def get_token_len(text):
    # glm_model_inputs = glm_tokenizer.apply_chat_template([{"role": "user", "content": text}],
    #                                                      add_generation_prompt=True,
    #                                                      tokenize=True,
    #                                                      return_tensors="pt",
    #                                                      return_dict=True
    #                                                      )
    # glm_model_inputs = glm_tokenizer([text], return_tensors="pt")
    # glm_token_len = glm_model_inputs['input_ids'].shape[1]
    # qwen_model_inputs = qwen_tokenizer([text], return_tensors="pt")
    # qwen_token_len = qwen_model_inputs['input_ids'].shape[1]
    # return glm_token_len, qwen_token_len
    return 0,0

def count_token(config_map):
    rid2type2token = gen_token_map(config_map)
    with open('./rid2type2token.json', 'w', encoding='utf-8') as f:
        json.dump(rid2type2token, f, ensure_ascii=False)
    # rid2type2token = json.load(open('./rid2type2token.json', "rb"))
    # result_list = []
    # for x in xs:
    #     result_list.append(['{}\n{}'.format(x['instruction'], x['input']), x['output']])
    # write_pd = pd.DataFrame(result_list, columns=['prompt', 'label'])
    # write_pd.to_excel('{}.xlsx'.format('tttt'))

def get_record_type_map(cid, config_map):
    sql = '''
            select system_id,record_type,record_name,remark,data_type from ms_system_record_type
            '''
    xs = sql_query(cid, sql, config_map)
    record_type_map = {}
    for x in xs:
        record_type_map[str(x['record_type'])] = x['record_name']
    return record_type_map

def count_token_gen_excel(config_map):
    record_type_map = get_record_type_map(1480, config_map)
    record_type_map['100001'] = '病理报告'
    record_type_map['100002'] = 'lis报告'
    record_type_map['100003'] = 'ris报告'
    record_type_map['100004'] = '医嘱数据'
    rid2type2token = json.load(open('/Users/<USER>/Downloads/rid2type2token_v2.json', "rb"))
    type_map = {}
    for rid in rid2type2token:
        for t in rid2type2token[rid]:
            type_map[t] = 1
    type_list = [str(tt) for tt in sorted([int(t) for t in type_map])]
    columns = ['record_id']
    for t in type_list:
        columns.append('{}-{}-文书数量'.format(t, record_type_map[t]))
        columns.append('{}-{}-char-len'.format(t, record_type_map[t]))
        columns.append('{}-{}-glm-token-len'.format(t, record_type_map[t]))
        columns.append('{}-{}-qwen-token-len'.format(t, record_type_map[t]))

    columns.append('文书总数量')
    columns.append('sum-char-len')
    columns.append('sum-glm-token-len')
    columns.append('sum-qwen-token-len')
    columns.append('glm-token-rate')
    columns.append('qwen-token-rate')

    result_list = []
    type2token_map = {}
    for rid in rid2type2token:
        temp_list = [rid]
        char_len_list = []
        glm_token_len_list = []
        qwen_token_len_list = []
        for t in type_list:
            if t not in type2token_map:
                type2token_map[t] = {'char_len_list': [], 'glm_token_len_list':[], 'qwen_token_len_list':[]}
            if t in rid2type2token[rid]:
                sub_char_len_list = []
                sub_glm_token_len_list = []
                sub_qwen_token_len_list = []
                for len_map in rid2type2token[rid][t]:
                    sub_char_len = len_map['char_len']
                    sub_glm_token_len = len_map['glm_token_len']
                    sub_qwen_token_len = len_map['qwen_token_len']
                    if sub_char_len:
                        sub_char_len_list.append(sub_char_len)
                        sub_glm_token_len_list.append(sub_glm_token_len)
                        sub_qwen_token_len_list.append(sub_qwen_token_len)
                if sub_char_len_list:
                    # char_len = int(sum(sub_char_len_list) / len(sub_char_len_list))
                    # glm_token_len = int(sum(sub_glm_token_len_list) / len(sub_glm_token_len_list))
                    # qwen_token_len = int(sum(sub_qwen_token_len_list) / len(sub_qwen_token_len_list))
                    char_len = sum(sub_char_len_list)
                    glm_token_len = sum(sub_glm_token_len_list)
                    qwen_token_len = sum(sub_qwen_token_len_list)
                    temp_list.extend([len(sub_char_len_list), char_len, glm_token_len, qwen_token_len])
                    char_len_list.append(char_len)
                    glm_token_len_list.append(glm_token_len)
                    qwen_token_len_list.append(qwen_token_len)
                    type2token_map[t]['char_len_list'].append(char_len)
                    type2token_map[t]['glm_token_len_list'].append(glm_token_len)
                    type2token_map[t]['qwen_token_len_list'].append(qwen_token_len)
                else:
                    temp_list.extend([0, 0, 0, 0])
            else:
                temp_list.extend([0, 0,0,0])
        tt_list = [temp_list[i] for i in range(1, len(temp_list), 4)]
        temp_list.append(sum(tt_list))
        temp_list.append(sum(char_len_list))
        temp_list.append(sum(glm_token_len_list))
        temp_list.append(sum(qwen_token_len_list))
        temp_list.append(int(sum(char_len_list)/sum(glm_token_len_list)*100)/100)
        temp_list.append(int(sum(char_len_list)/sum(qwen_token_len_list)*100)/100)
        result_list.append(temp_list)
    temp_list = ['avg']
    for t in type_list:
        len_map = type2token_map[t]
        char_len_list = len_map['char_len_list']
        glm_token_len_list = len_map['glm_token_len_list']
        qwen_token_len_list = len_map['qwen_token_len_list']
        temp_list.append(0)
        temp_list.append(int(sum(char_len_list) / len(char_len_list)))
        temp_list.append(int(sum(glm_token_len_list) / len(glm_token_len_list)))
        temp_list.append(int(sum(qwen_token_len_list) / len(qwen_token_len_list)))
    temp_list.extend([0,0,0,0,0,0])
    result_list.append(temp_list)
    write_pd = pd.DataFrame(result_list, columns=columns)
    write_pd.to_excel('{}.xlsx'.format('tttt'))


def anaysis(result_list):
    new_dataset_df = result_list.to_pandas()
    # new_dataset_df = dataset_df.dropna(axis='index', how='all', subset=['output'])
    new_dataset_df['input_len'] = new_dataset_df['instruction'].astype(str).map(len)
    new_dataset_df['target_len'] = new_dataset_df['output'].astype(str).map(len)
    print(new_dataset_df['sum-char-len'].describe(percentiles=[0.25, 0.5, 0.75, 0.9, 0.95]))
    print(new_dataset_df['sum-glm-token-len'].describe(percentiles=[0.25, 0.5, 0.75, 0.9, 0.95]))
    print(new_dataset_df['sum-qwen-token-len'].describe(percentiles=[0.25, 0.5, 0.75, 0.9, 0.95]))
    print(new_dataset_df['glm-token-rate'].describe(percentiles=[0.25, 0.5, 0.75, 0.9, 0.95]))
    print(new_dataset_df['qwen-token-rate'].describe(percentiles=[0.25, 0.5, 0.75, 0.9, 0.95]))

def gen_token_map(config_map):
    cid=1480
    # 生成出院小结
    sql = '''select rr.id record_id,rr.patient_guid,rr.customer_id,rr.patient_age,rr.patient_age_type,rr.inpatient_department,rr.patient_gender,date_format(dd.discharge_time,'%Y-%m-%d %H:%i:%s') discharge_time from mt_patient_record rr
                inner join mt_patient_discharge_record dd on dd.record_id=rr.id
            inner join mt_patient_progress pp on pp.record_id=rr.id
        where rr.record_type = 2 and pp.progress_type=10 
    '''
    xs = sql_query(cid, sql, config_map)
    record_id_list = []
    for x in xs:
        record_id_list.append(int(x['record_id']))
        cid = x['customer_id']
    print('id len:{}'.format(len(record_id_list)))
    rid2type2token={}
    for i, rid in enumerate(record_id_list):
        print('{}/{}'.format(i, len(record_id_list)))
        rid2type2token[rid] = {}
        raw_data = download_data(cid, rid, config_map)
        for x in raw_data:
            progress_type = x['progress_type']
            progress_text = re.sub(r'\s+', ' ', str(x['progress_text']))
            if progress_type not in rid2type2token[rid]:
                rid2type2token[rid][progress_type] = []
            glm_token_len, qwen_token_len = get_token_len(progress_text)
            rid2type2token[rid][progress_type].append({'char_len':len(progress_text),'glm_token_len':glm_token_len, 'qwen_token_len':qwen_token_len})
        mechanism_str = get_zljg_mechanism(cid, rid, config_map)
        glm_token_len, qwen_token_len = get_token_len(mechanism_str)
        rid2type2token[rid][100001] = [
            {'char_len': len(mechanism_str), 'glm_token_len': glm_token_len, 'qwen_token_len': qwen_token_len}]
        test_str = get_zljg_test(cid, rid, config_map)
        glm_token_len, qwen_token_len = get_token_len(test_str)
        rid2type2token[rid][100002] = [
            {'char_len': len(test_str), 'glm_token_len': glm_token_len, 'qwen_token_len': qwen_token_len}]
        exam_str = get_zljg_exam(cid, rid, config_map)
        glm_token_len, qwen_token_len = get_token_len(exam_str)
        rid2type2token[rid][100003] = [
            {'char_len': len(exam_str), 'glm_token_len': glm_token_len, 'qwen_token_len': qwen_token_len}]
        order_str = get_zljg_medical_order(cid, rid, config_map)
        glm_token_len, qwen_token_len = get_token_len(order_str)
        rid2type2token[rid][100004]=[{'char_len': len(order_str), 'glm_token_len':glm_token_len, 'qwen_token_len':qwen_token_len}]

    return rid2type2token



def get_zljg_mechanism(cid, rid, config_map):
    prompt_input = ''
    sql = '''select examination_name,examination_result,record_time as examination_time from mt_patient_mechanism_record
                    where customer_id={} and record_id={} 
                    order by record_time'''.format(cid, rid)
    sql_rlt = sql_query(cid, sql, config_map)
    rlt = []
    for it in sql_rlt:
        examination_time = it["examination_time"].strftime("%Y-%m-%d")
        row = "{} {}：{}".format(examination_time, it["examination_name"], it["examination_result"])
        rlt.append(markdown_ft(row))
    if len(rlt) > 0:
        prompt_input += '病理检查：' + '\n'.join(rlt) + '\n'
    return prompt_input



def get_zljg_test(cid, rid, config_map):
    # 每天的异常检验
    prompt_input = ''
    sql = '''select ii.create_date, ii.test_item, ii.test_result, ii.test_value_unit, ii.test_value_change,ii.qualitative_result_value,tt.test_name,tt.report_time test_date_time,tt.create_date
                from {}  ii inner join mt_patient_test tt on ii.test_id=tt.id
                where ii.customer_id={} and ii.record_id={} order by tt.report_time;'''.format(f"mt_patient_test_item_{rid % 10}", cid, rid)
    sql_rlt = sql_query(cid, sql, config_map)
    exist_map = {}

    test_last_map = {}
    for it in sql_rlt:
        test_item = normalize_name(it["test_item"])
        test_date_time = it["test_date_time"].strftime("%Y-%m-%d")

        value_change = ''
        if str(it['qualitative_result_value']).lower() == 'h' or str(it['test_value_change']).lower() == 'h' or str(it['test_value_change']).find('高')>-1:
            value_change = '↑'
        elif str(it['qualitative_result_value']).lower() == 'l' or str(it['test_value_change']).lower() == 'l' or str(it['test_value_change']).find('低')>-1:
            value_change = '↓'
        test_value_unit = it["test_value_unit"] if it["test_value_unit"] else ""
        if not test_value_unit:
            value_change = ''

        key = '{}_{}'.format(test_date_time, test_item)
        if key in exist_map:
            continue
        exist_map[key] = 1

        if test_date_time not in test_last_map:
            test_last_map[test_date_time] = {}
        if it['test_name'] not in test_last_map[test_date_time]:
            test_last_map[test_date_time][it['test_name']] = {}
        test_last_map[test_date_time][it['test_name']][test_item] = '{} {}'.format(it["test_item"], it[
            "test_result"] + test_value_unit + value_change)
    test_result = ''
    for test_date_time in sorted(test_last_map):
        for test_name in test_last_map[test_date_time]:
            new_item_list = sorted([_v for _, _v in test_last_map[test_date_time][test_name].items()])
            if len(new_item_list) == 0:
                continue
            test_result += '{} {}：{}。\n'.format(test_date_time, test_name, '，'.join(new_item_list))

    if test_result:
        prompt_input += '检验：' + test_result
    return prompt_input


def get_zljg_exam(cid, rid, config_map):
    prompt_input = ''
    sql = '''select record_time_format as examination_time, examination_name, examination_method,examination_part, examination_result
                from `mt_patient_examination` 
                where customer_id={} and record_id={} 
                order by record_time_format'''.format(cid, rid)
    sql_rlt = sql_query(cid, sql, config_map)
    rlt = []
    for it in sql_rlt:
        examination_time = it["examination_time"].strftime("%Y-%m-%d")
        row = "{} {}：{}".format(examination_time, it["examination_name"], it["examination_result"])
        rlt.append(markdown_ft(row))
    if len(rlt)>0:
        prompt_input += '检查：'+'\n'.join(rlt)
    return prompt_input

def get_zljg_medical_order(cid, rid, config_map):
    prompt_input = ''
    sql = '''
                    select oo.customer_id,oo.record_id,oo.order_create_time,oo.order_type,oo.order_content,oo.pathway,oo.dosage, oo.unit, oo.frequency,oo.description  from mt_patient_medical_order oo 
                where oo.record_id = {} and oo.order_type in (3,6,7,11) and oo.order_flag<4 and oo.status=1
                    '''.format(rid)
    sql_rlt = sql_query(cid, sql, config_map)
    order_time_map = {}
    for v in sql_rlt:
        order_time = v["order_create_time"].strftime("%Y-%m-%d")
        if order_time not in order_time_map:
            order_time_map[order_time] = []
        order_time_map[order_time].append(
            '{} {} {} {} {}'.format(v['order_content'], v['dosage'], v['unit'], v['frequency'], v['pathway']).replace(
                'None', '').replace('NULL',''))
    order_result = ''
    for order_time in sorted(order_time_map):
        order_result += '{}：{}。\n'.format(order_time, '；'.join(order_time_map[order_time]))
    if order_result:
        prompt_input += '治疗医嘱：' + order_result
    return prompt_input
