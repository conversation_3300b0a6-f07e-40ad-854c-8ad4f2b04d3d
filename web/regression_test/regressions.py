# coding:utf-8
import json
import os
from web.mrg_service.differential_diagnosis import get_differential_diagnosis,generate_differential_diagnosis
from web.regression_test.dingding import send_dingding_message
proDir = os.path.split(os.path.realpath(__file__))[0]


def regression_disease(config_map):
    webhook = 'https://oapi.dingtalk.com/robot/send?access_token=7cbf81158a7e8f2bb814bc39f3011542bb324ad211b33afda353f9b76c24ee9b'
    secret = 'SECe26881ad947f14fd988b665c5ba16133477aaa04ee2f44abb0a458cdc6620059'
    error_list = []
    request_file = os.path.join(proDir, 'differential_diagnosis_request.jsonl')
    with open(request_file, "r") as f_r:
        for k, line in enumerate(f_r.readlines()):
            x = json.loads(line)
            if "prompt" in x:
                generate_text = get_differential_diagnosis(x["prompt"], 'disease_model_1', config_map)
                for i, t in enumerate(generate_text):
                    if len(t['progress_content']) < 3+i*2:
                        error_list.append("模型实时结果：请求串{}返回缺失数据：{}".format(k, t['attribute_name']))
            else:
                for it in x["contents"]:
                    customer_id = it.get("customer_id", 1001)
                    record_id = it.get("record_id")
                    medical_record = it.get("medical_record")
                    medical_record_360 = it.get("medical_record_360")
                    generate_text_list, reason = generate_differential_diagnosis(customer_id, record_id, medical_record,
                                                                                 medical_record_360, config_map)
                    for _, generate_text in enumerate(generate_text_list):
                        for i, t in enumerate(generate_text['generate_text']):
                            if len(t['progress_content']) < 3+i*2:
                                error_list.append("模型离线结果：请求串{}返回缺失数据：{}".format(k, t['attribute_name']))
    error_str = '\n'.join(error_list)
    if error_str:
        send_dingding_message(webhook, secret, error_str)
    return error_str
