# coding:utf-8
import sys
sys.path.append("../../")
import configparser
import numpy as np
import json
import pandas as pd
from web.web_utils.text_util import normalize_name
import os
from web.web_utils.sql_util import sql_query


proDir = os.path.split(os.path.realpath(__file__))[0]
# 在当前文件路径下查找.ini文件
configPath = os.path.join(proDir, '../config.ini')
con = configparser.RawConfigParser()
con.read(configPath, encoding='utf-8')
sections = con.sections()
config_map = {}
for sec in sections:
    items = dict(con.items(sec))
    config_map[sec] = items
cid=1001

def get_disease_map():
    disease_sql = '''SELECT distinct dd.disease_name,dd.record_id from mt_patient_disease dd 
inner join mt_patient_record rr on dd.record_id=rr.id
where rr.inpatient_department regexp '呼吸'
'''
    sql_rlt = sql_query(cid, disease_sql, config_map)
    disease2record_map = {}
    for it in sql_rlt:
        disease_name = normalize_name(it["disease_name"])
        if not disease_name:
            continue
        if disease_name not in disease2record_map:
            disease2record_map[disease_name] = {}
        disease2record_map[disease_name][str(it['record_id'])] = 1
    return disease2record_map

def get_test_item_map(record_ids):
    for i in range(10):
        sql = '''
        select test_item,record_id from mt_patient_test_item_{}
    where record_id in ({})
    '''.format(i, record_ids)
        sql_rlt = sql_query(cid, sql, config_map)
        test_item2record_map = {}
        for it in sql_rlt:
            test_item = normalize_name(it["test_item"])
            if not test_item:
                continue
            if test_item not in test_item2record_map:
                test_item2record_map[test_item] = {}
            test_item2record_map[test_item][str(it['record_id'])] = 1
    return test_item2record_map

def get_test_and_item_map(record_ids):
    for i in range(10):
        sql = '''
        select ii.test_item,ii.record_id,tt.test_name from mt_patient_test_item_{}} ii 
INNER JOIN mt_patient_test tt on ii.test_id=tt.id 
where ii.record_id in ({}})
    '''.format(i, record_ids)
        sql_rlt = sql_query(cid, sql, config_map)
        test_item2record_map = {}
        for it in sql_rlt:
            test_and_item = normalize_name('{}_{}'.format(it['test_name'],it["test_item"]))
            if not test_and_item:
                continue
            if test_and_item not in test_item2record_map:
                test_item2record_map[test_and_item] = [it['test_name'],it["test_item"]]
    return test_item2record_map

def get_order_drug_map(record_ids):
    sql = '''
    select oo.customer_id,oo.record_id,oo.order_create_time,oo.order_type,oo.order_content,oo.pathway,oo.dosage, oo.unit, oo.frequency from mt_patient_medical_order oo 
		where oo.record_id in ({})
		 and oo.order_class=1 and oo.order_type =3 and oo.order_flag<4 and oo.status=1
'''.format(record_ids)
    sql_rlt = sql_query(cid, sql, config_map)
    order_content2record_map = {}
    for it in sql_rlt:
        order_content = normalize_name(it["order_content"])
        if not order_content:
            continue
        if order_content not in order_content2record_map:
            order_content2record_map[order_content] = {}
        order_content2record_map[order_content][str(it['record_id'])] = 1
    return order_content2record_map

def get_exam_map(record_ids):
    sql = '''
    select examination_name,record_id from mt_patient_examination
		where record_id in ({})
'''.format(record_ids)
    sql_rlt = sql_query(cid, sql, config_map)
    exam2record_map = {}
    for it in sql_rlt:
        examination_name = normalize_name(it["examination_name"])
        if not examination_name:
            continue
        if examination_name not in exam2record_map:
            exam2record_map[examination_name] = {}
        exam2record_map[examination_name][str(it['record_id'])] = 1
    return exam2record_map

def count_fre(item_map,record_count):
    new_item_map = {}
    for it in item_map:
        new_item_map[it] = [len(item_map[it])/float(record_count),len(item_map[it]),record_count]
    return new_item_map

def gen_test_and_drug():
    disease2count = {}
    disease2record_map = get_disease_map()
    disease2record_list = sorted(disease2record_map.items(), key=lambda kv: len(kv[1]), reverse=True)
    # for disease in disease2record_map:
    for i in range(10):
        disease = disease2record_list[i][0]
        record_count = len(disease2record_map[disease])
        record_ids = ','.join(disease2record_map[disease])
        test_item2record_map = get_test_item_map(record_ids)
        exam2record_map = get_exam_map(record_ids)
        drug2record_map = get_order_drug_map(record_ids)
        disease2count[disease] = {'test_item': count_fre(test_item2record_map, record_count),
                                  'exam': count_fre(exam2record_map, record_count),
                                  'drug': count_fre(drug2record_map, record_count)}
    print(disease2count)
    result = []
    for disease in disease2count:
        for map_type in disease2count[disease]:
            for it in disease2count[disease][map_type]:
                result.append([disease, map_type, it] + disease2count[disease][map_type][it])
    # result.append(result[0])
    data_node = pd.DataFrame(result, columns=['disease', 'map_type', 'name', 'fre', 'name_count', 'record_count'])
    data_node.to_csv("disease2count.csv", index=False)

def gen_test():
    disease2count = {}
    disease2record_map = get_disease_map()
    disease2record_list = sorted(disease2record_map.items(), key=lambda kv: len(kv[1]), reverse=True)
    # for disease in disease2record_map:
    for i in range(10):
        disease = disease2record_list[i][0]
        record_ids = ','.join(disease2record_map[disease])
        test_item2record_map = get_test_and_item_map(record_ids)
        disease2count[disease] = {'test_item': test_item2record_map}
    print(disease2count)
    result = []
    for disease in disease2count:
        for map_type in disease2count[disease]:
            for it in disease2count[disease][map_type]:
                result.append([disease, map_type, it] + disease2count[disease][map_type][it])
    # result.append(result[0])
    data_node = pd.DataFrame(result, columns=['disease', 'map_type', 'name', 'test_name', 'test_item'])
    data_node.to_csv("disease2test.csv", index=False)
if __name__ == '__main__':
    gen_test()
    print('ok')
