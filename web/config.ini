[app]
app_ip = 本机IP
app_port = 8800
#is_hospital-->现场环境还是公司环境(现场为1，公司测试环境为0，其他环境为1)
is_hospital = 0
pangoo_ip = hmslb
[openai]
openai_api_key=
openai_api_base=https://ark.cn-beijing.volces.com/api/v3
qc_chat_model=deepseek-v3-241226
[llm_api]
llm_qc_stream_ip = hmslb
[llm_qc_model]
is_run = 1
qc_chat_ip = 127.0.0.1
qc_chat_port = 8001
qc_chat_model = /hm/share_model/Qwen1.5-14B-Chat
text_limit = -1
text_limit_str = @@@输入文本过长@@@
[llm_model]
is_run = 1
stream_overtime = 20
llm_model_path = /hm/share_model/Qwen1.5-14B-Chat
api_base = http://大模型api的IP:端口/v1
api_key = 大模型api key，默认为EMPTY
dify_ip = dify服务IP
dify_port = 9900
dify_max_concurrent_tasks = 50
dify_max_queue_size = 500
#pre_think-->华为现场为1，其他为0
pre_think = 0
chip_type = gpu
input_max_len = 根据现场大模型进行配置输入长度限制，如10000/20000/30000
custom_prompt = 0
[llm_diagnosis]
chat_diagnosis_ip = 127.0.0.1
chat_diagnosis_port = 9596
[scheduler_discharge]
day = *
hour = 22
minute = 00
second = 45
is_run = 0
department = 呼吸
[scheduler_regression]
day = *
hour = 23
minute = 00
second = 45
is_run = 0
[1001]
db = hmcdss2
sub_db = hmcdss2
[hmcdss2]
sql_host = hmmysql.db.hmcom
sql_user = algo_user
sql_pswd = bwXKLKspMMtSQpPx^hJgE2wS5V#sr8Uh
sql_db = hmcdss2
sql_port = 3306
[hm_llm]
sql_host = hmmysql.db.hmcom
sql_user = algo_user
sql_pswd = bwXKLKspMMtSQpPx^hJgE2wS5V#sr8Uh
sql_db = hm_llm
sql_port = 3306
[redis]
redis_addr = pangoo.hmredis.hmcom:6379
redis_password = 6dbc0460f9a34cf9:Huimei2015
redis_db = 9
[eureka]
eureka_addr =http://node1.hmeureka.hmcom:28810/eureka/,http://node2.hmeureka.hmcom:28810/eureka/
app_name = AIGC-SERVICE
