# coding:utf-8
from web.config import *
from web.mrg_service.differential_diagnosis import generate_differential_diagnosis
from web.mrg_service.cdss_ddx import *
from web.mrg_service.admission_summary import assemble_admission_summary, assemble_admission_summary_offline
from web.web_utils.text_util import get_uuid
from web.mrg_service.model_offline import *
from web.api.cdss_llm_api import loop
import json
import copy
import concurrent.futures
import re
from itertools import chain
import asyncio
from dashscope import AioGeneration


mrg_api = Blueprint('mrg_api', __name__, template_folder='templates')


@mrg_api.route('/admission_summary', methods=['POST'])
def get_admission_summary():
    rlt = {
        "contents": [],
        "error": 0,
        "status": 200
    }
    request_data = json.loads(request.get_data().decode('utf-8'))
    if 'contents' in request_data:
        for it in request_data["contents"]:
            customer_id = it.get("customer_id", 1001)
            record_id = it.get("record_id")
            medical_record = it.get("medical_record")
            generate_text, uuid, reason, cid, rid = assemble_admission_summary_offline(customer_id, record_id,
                                                                                       config_map,
                                                                                       medical_record=medical_record)
            generate_text_list = []
            if len(generate_text) > 0:
                lora_model = 'admission_model'
                # lora_model2feedback = get_feedback_result(rid, lora_model, config_map)
                # feedback = lora_model2feedback.get(lora_model, '')
                generate_text_list = [{'model_name': '模型', 'lora_model': lora_model, 'feedback': "",
                                       'dadian': {'bizCode': '85', 'moduleCode': '204', 'ruleId': uuid},
                                       'dadian_content': '入院记录', "generate_text": generate_text}]
            item = {
                "customer_id": cid,
                "record_id": rid,
                "generate_text_list": generate_text_list,
                "record_type": 1,
                "reason": reason
            }
            rlt["contents"].append(item)
    else:
        generate_text_list, reason = assemble_admission_summary(request.get_data(), config_map)
        item = {
            "customer_id": -1,
            "record_id": -1,
            "record_type": 1,
            "generate_text_list": generate_text_list,
            "reason": reason
        }
        rlt["contents"].append(item)
    return rlt


@mrg_api.route('/differential_diagnosis', methods=['POST'])
def get_differential_diagnosis():
    # text = request.get_json()
    text = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "contents": [],
        "error": 0,
        "status": 200
    }
    for it in text["contents"]:
        customer_id = it.get("customer_id", 1001)
        record_id = it.get("record_id")
        medical_record = it.get("medical_record")
        medical_record_360 = it.get("medical_record_360")
        generate_text_list, reason = generate_differential_diagnosis(customer_id, record_id, medical_record,
                                                                     medical_record_360, config_map)
        item = {
            "customer_id": customer_id,
            "record_id": record_id,
            "record_type": 1,
            "generate_text_list": generate_text_list,
            "reason": reason
        }
        rlt["contents"].append(item)
    return rlt

def remove_parentheses(text):
    pattern = re.compile(r'([^（）]+)(?:（([^）]+)）)?')
    match = pattern.match(text)
    if match:
        return [group for group in match.groups() if group]
    return [text]

blood_test_related_patterns = [
    r'红细胞(增多|减少)',
    r'血红蛋白浓度|低血红蛋白',
    r'红细胞[压体]积',
    r'平均红细胞体积',
    r'平均红细胞血红蛋白含量|红细胞平均血红蛋白含量',
    r'平均红细胞血红蛋白浓度|红细胞平均血红蛋白浓度',
    r'红细胞分布宽度',
    r'白细胞(增多|减少)',
    r'白细胞分类',
    r'(淋巴|中性粒|单核|嗜酸性粒|嗜碱性粒|幼稚粒)细胞(增多|减少)',
    r'(淋巴|中性粒|单核|嗜酸性粒|嗜碱性粒|幼稚粒)细胞百分比(降低|升高)',
    r'血小板(增多|减少)',
    r'平均血小板体积',
    r'血小板(体积)?分布宽度',
    r'高N末端B型脑钠肽前体|N末端B型脑钠肽前体升高|高NT-proBNP血症',
    r'严重低二氧化碳结合力'
]
blood_test_related_regexes = [re.compile(pattern, re.IGNORECASE) for pattern in blood_test_related_patterns]

def disease_in_set(disease_name, disease_set):
    return any(disease_name in tmp or tmp in disease_name for tmp in disease_set)

@mrg_api.route('/differential_diagnosis_lst', methods=['POST'])
def get_differential_diagnosis_lst():
    text = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "contents": [],
        "error": 0,
        "status": 200
    }
    medical_record = text.get("medical_record")
    
    with concurrent.futures.ThreadPoolExecutor() as executor:
        future_lst = executor.submit(generate_differential_diagnosis_lst, medical_record, config_map)
        future_exam = executor.submit(generate_differential_diagnosis_exam, medical_record, config_map)
        
        generate_lst, reason_lst = future_lst.result()
        generate_exam, reason_exam = future_exam.result()
    
    new_generate_exam = []
    if len(generate_exam) < 1:
        generate_main_desc = [{"disease_name": d["disease_name"], "is_critical_disease": d["is_critical_disease"]} for d in generate_lst]
    else:
        tmp_diseases_set = set(chain.from_iterable(remove_parentheses(d['disease_name']) for d in generate_lst if d['has_relation'] == "是"))
        tmp_exam_diseases_set = set(chain.from_iterable(remove_parentheses(d['disease_name']) for d in generate_lst if d['has_relation'] != "是"))

        generate_main_desc = [{"disease_name": d["disease_name"], "is_critical_disease": d["is_critical_disease"]} for d in generate_lst if d['has_relation'] == "是"]
        new_generate_exam = [{"disease_name": d["disease_name"], "is_critical_disease": d["is_critical_disease"]} for d in generate_lst if d['has_relation'] != "是" and d["is_critical_disease"] == "是"]

        for d in generate_exam:
            disease_name = d['disease_name']
            if disease_in_set(disease_name, tmp_diseases_set) or any(regex.search(disease_name) for regex in blood_test_related_regexes) or disease_in_set(disease_name, tmp_exam_diseases_set):
                continue
            is_repetitive = repetitive_diagnose(new_generate_exam, disease_name) or repetitive_diagnose(generate_main_desc, disease_name)
            if d['has_relation'] == "是":
                if not is_repetitive:
                    generate_main_desc.append({"disease_name": d["disease_name"], "is_critical_disease": d["is_critical_disease"]})
            elif d["is_critical_disease"] == "是":
                if not is_repetitive:
                    new_generate_exam.append({"disease_name": d["disease_name"], "is_critical_disease": d["is_critical_disease"]})

    item_lst = {
        "generate_list": generate_main_desc,
        "reason": reason_lst
    }
    item_exam = {
        "generate_list": new_generate_exam,
        "reason": reason_exam
    }
    
    rlt["contents"].append(item_lst)
    rlt["contents"].append(item_exam)
    
    return rlt

def repetitive_diagnose(group_a, disease_name):
    filter_lst = [
        ["肺炎", "双肺炎症", "肺部感染", "社区获得性肺炎", "双肺炎性病变"],
        ["急性肾盂肾炎", "泌尿系统感染", "泌尿系感染"],
        ["电解质紊乱", "电解质紊乱（低钾血症）", "低钾血症"],
        ["电解质紊乱", "电解质紊乱（高钾血症）", "高钾血症"],
        ["电解质紊乱", "低钾血症", "低氯血症", "高钾血症", "低钠血症", "高钠血症"],
        ["急性脑卒中（缺血性或出血性）", "急性脑血管事件（如脑卒中）"],
        ["急性心肌梗死", "急性冠状动脉综合征", "急性冠脉综合征", "冠状动脉综合征"],
        ["肺栓塞", "肺动脉栓塞"],
        ["脑梗死", "急性脑血管事件", "急性脑血管事件（如脑梗死或脑出血）", "急性脑梗死", "脑梗死急性期"],
        ["急性心力衰竭", "心功能不全"],
        ["室性心动过速", "宽QRS波心动过速（室速）", "室速"],
        ["高N末端B型脑钠肽前体", "心功能不全"]
    ]
    for filter_group in filter_lst:
        if any(diagnosis in [d["disease_name"] for d in group_a] for diagnosis in filter_group):
            if disease_name in filter_group:
                return True
    return False

stream_code_map_dis = {}

@mrg_api.route('/differential_diagnosis_basis', methods=['POST'])
def get_differential_diagnosis_basis():
    text = json.loads(request.get_data(as_text=True))
    rlt = {
        "head": {"error": 0},
        "code": 200,
        "body": {},
        "message": '操作成功',
        "has_error": False,
        "stream_code": ""
    }
    medical_record = text.get("medical_record")
    disease = text.get("disease")
    source = str(text.get("sourceFlag"))
    serial_number = text.get("medical_record").get("serialNumber", "")
    stream_code = f"{serial_number}-{disease}-{source}"
    rlt['stream_code'] = stream_code
    
    if stream_code and stream_code in stream_code_map_dis:
        rlt['body'] = copy.deepcopy(stream_code_map_dis[stream_code])
        if stream_code_map_dis[stream_code]['dialogue_end_flag']:
            stream_code_map_dis.pop(stream_code)
        return jsonify(rlt)

    content = ''
    if not content:
        stream_code_map_dis[stream_code] = {"content": '', "dialogue_error_flag": False, "dialogue_end_flag": False}
        asyncio.run_coroutine_threadsafe(generate_differential_diagnosis_basis(medical_record, disease, source, config_map), loop)
        rlt['body'] = {"content": content, "dialogue_error_flag": False, "dialogue_end_flag": False}
    return jsonify(rlt)

async def generate_differential_diagnosis_basis(medical_record, disease, source, config_map):
    prompt, cid, rid = get_diagnosis_basis_one_case_prompt(medical_record, disease, source, config_map)
    serial_number = medical_record.get("serialNumber", "")
    stream_code = f"{serial_number}-{disease}-{source}"

    if not prompt:
        stream_code_map_dis[stream_code]["content"] = "病历信息无内容或诊断列表为空!"
        stream_code_map_dis[stream_code]["dialogue_end_flag"] = True
        return
    
    prompt_hash = hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest()
    record2prompt = get_model_offline_result([str(rid)], 'llm_diagnosis', config_map)

    if rid in record2prompt and prompt_hash in record2prompt[rid]:
        for model_name in record2prompt[rid][prompt_hash]:
            generate_text_str, _, _ = record2prompt[rid][prompt_hash][model_name]
            stream_code_map_dis[stream_code]["content"] = generate_text_str
            stream_code_map_dis[stream_code]["dialogue_end_flag"] = True
            return
    
    final_result = []
    message = [
        {
            "role": "user",
            "content": prompt
        }
    ]
    responses = await AioGeneration.call(
        model='qwen2.5-14b-instruct',
        messages=message,
        result_format='message',
        top_k=1,
        stream=True,
        incremental_output=True
    )
    async for response in responses:
        if response.status_code == HTTPStatus.OK:
            output = str(response.output.choices[0]['message']['content'])
            if response.output.choices[0]['finish_reason'] in ['stop', 'length']:
                output = "请求结束"
            if output == "请求结束":
                stream_code_map_dis[stream_code]["dialogue_end_flag"] = True
            else:
                final_result.append(output)
                stream_code_map_dis[stream_code]["content"] += output
    
    final_output = ''.join(final_result)
    xs = []
    uuid = get_uuid()
    xs.append([prompt_hash, prompt, final_output, uuid, 'Qwen1.5-14B-Chat', cid, rid, '诊断解释', ''])
    insert_model_result(xs, 'llm_diagnosis', config_map)
    return final_output

stream_code_ddx = {}
@mrg_api.route('/differential_diagnosis_lst_stream', methods=['POST'])
def get_differential_diagnosis_lst_stream():
    text = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "head": {"error": 0},
        "code": 200,
        "body": {},
        "message": '操作成功',
        "has_error": False,
        "stream_code": ""
    }
    medical_record = text.get("medical_record")
    serial_number = text.get("medical_record").get("serialNumber", "")
    source = str(text.get("sourceFlag", 3))
    stream_code = f"{serial_number}-{source}"
    rlt['stream_code'] = stream_code
    if stream_code and stream_code in stream_code_ddx:
        rlt['body'] = copy.deepcopy(stream_code_ddx[stream_code])
        if stream_code_ddx[stream_code]['dialogue_end_flag']:
            stream_code_ddx.pop(stream_code)
        return jsonify(rlt)

    content = ''
    if not content:
        stream_code_ddx[stream_code] = {"content": '', "dialogue_error_flag": False, "dialogue_end_flag": False}
        asyncio.run_coroutine_threadsafe(generate_differential_diagnosis_stream(medical_record, source, config_map), loop)
        rlt['body'] = {"content": content, "dialogue_error_flag": False, "dialogue_end_flag": False}
    return jsonify(rlt)

async def generate_differential_diagnosis_stream(medical_record, source, config_map):
    prompt, cid, rid = get_diagnosis_prompt(medical_record, config_map)
    serial_number = medical_record.get("serialNumber", "")
    stream_code = f"{serial_number}-{source}"

    if not prompt:
        stream_code_ddx[stream_code]["content"] = "病历信息无内容!"
        stream_code_ddx[stream_code]["dialogue_end_flag"] = True
        return
    
    prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
    record2prompt = get_model_offline_result([str(rid)], 'llm_diagnosis', config_map)

    if rid in record2prompt and prompt_hash in record2prompt[rid]:
        for model_name, (generate_text_str, _, _) in record2prompt[rid][prompt_hash].items():
            generate_content = simple_diagnosis_extraction(generate_text_str, True)
            stream_code_ddx[stream_code]["content"] = generate_content
            stream_code_ddx[stream_code]["dialogue_end_flag"] = True
            return

    json_str = ""
    buffer = ""
    inside_diagnosis = False
    message = [
        {
            "role": "user",
            "content": prompt
        }
    ]
    responses = await AioGeneration.call(
        model='qwen2.5-14b-instruct',
        messages=message,
        result_format='message',
        top_k=1,
        stream=True,
        incremental_output=True
    )
    async for response in responses:
        if response.status_code == HTTPStatus.OK:
            output = str(response.output.choices[0]['message']['content'])
            if response.output.choices[0]['finish_reason'] in ['stop', 'length']:
                output = "请求结束"
            if output == "请求结束":
                stream_code_ddx[stream_code]["dialogue_end_flag"] = True
            else:
                if "[" in output:
                    inside_diagnosis = True
                if inside_diagnosis:
                    json_str += output
                    if '{' in json_str and '}' in json_str:
                        start = json_str.find('{')
                        end = json_str.find('}') + 1

                        buffer = json_str[start:end].strip()
                        json_str = json_str[end:].strip()

                        if buffer:
                            try:
                                diagnosis = json.loads(buffer)
                                serious = "危重" if diagnosis["危重"] == "是" else "非危重"
                                one_case = f"{diagnosis['诊断']}：{serious}\n"
                                stream_code_ddx[stream_code]["content"] += one_case
                            except json.JSONDecodeError:
                                continue
    return

@mrg_api.route('/doctor_feedback', methods=['POST'])
def doctor_feedback():
    text = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "error": 0,
        "status": 200
    }
    customer_id = text.get("customer_id", 1001)
    record_id = text.get("record_id")
    lora_model = text.get("lora_model")
    answer_uuid = text.get("answer_uuid")
    doctor_name = text.get("doctor_name", '')
    feedback = text.get("feedback", '')
    xs = [[lora_model, answer_uuid, doctor_name, customer_id, record_id, feedback]]
    re = insert_feedback_result(xs, config_map)
    if re < 0:
        rlt['error'] = -1
    return rlt


@mrg_api.route('/doctor_feedback_msg', methods=['POST'])
def doctor_feedback_msg():
    text = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "error": 0,
        "status": 200
    }
    record_id = text.get("record_id")
    lora_model = text.get("lora_model")
    lora_model2feedback = get_feedback_result(record_id, lora_model, config_map)
    rlt['feedback'] = lora_model2feedback.get(lora_model, '')
    rlt['lora_model'] = lora_model
    return rlt
