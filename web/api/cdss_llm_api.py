# coding:utf-8
import asyncio
import copy
import json
import logging
import threading
import time
import aiohttp
from web.api.cdss_call_helper import (get_api_key_info, process_api_key,
                                      refresh_dify_result)
from web.config import *
from web.dify.helper import remove_dify_error_header
from web.mrg_service.cdss_llm import (get_cdss_chat_history,
                                      get_dialogue_content,
                                      get_dialogue_content_list,
                                      get_dialogue_list,
                                      get_input_by_progress_id,
                                      get_input_by_prompt_type,
                                      get_llm_prompt_by_id,
                                      get_llm_prompt_list,
                                      get_llm_qc_stream_result,
                                      get_path_by_name, get_record_tree,
                                      save_dialogue_content,
                                      update_dialogue_content,
                                      update_dialogue_feedback)
from web.mrg_service.cdss_llm_agent import (get_llm_agent_by_code,
                                            get_llm_agent_list,
                                            update_agent_attention)
from web.mrg_service.cdss_llm_input import (get_cdss_before_admission_input,
                                            get_llm_prompt_input,
                                            get_progress_references,
                                            get_progress_references_discharge,
                                            get_rid_list_by_guid, get_sssj,
                                            get_vital_signs_input)
from web.mrg_service.get_hmcdss_data import get_record_id
from web.prometheus_monitor import CASE_GENERATE, g_monitor
from web.web_utils.redis_util import redis_cache
from web.web_utils.text_util import merge_and_deduplicate, normalize_text
from web.web_utils.api_util import post_url
from web.web_utils.sql_util import sql_query
import hashlib
from datetime import datetime


logging = logging.getLogger(__name__)

loop = asyncio.get_event_loop()
threading.Thread(target=loop.run_forever).start()
stream_code_map = {}
dialogue_code2chat_map = {}


cdss_llm_api = Blueprint('cdss_llm_api', __name__, template_folder='templates')
# openai_api_base = "http://{}:{}/v1".format(config_map['llm_model']['llm_model_ip'], config_map['llm_model']['llm_model_port'])
openai_api_base = config_map['llm_model']['api_base']
api_key = config_map['llm_model']['api_key']
# aclient = AsyncOpenAI(api_key=api_key, base_url=openai_api_base)
# client = OpenAI(api_key=api_key, base_url=openai_api_base)


def auth_user(user_code, customer_id):
    """
    用户鉴权
    
    Args:
        user_code: 用户代码
        customer_id: 客户ID
        
    Returns:
        鉴权成功返回查询结果，失败返回None
    """
    if not user_code or not customer_id:
        return None
    
    sql = f'''
SELECT * FROM gpt_agent_config WHERE  type = 11 and  flag = 1 and  `status` = 2 and user_code in ("{user_code}","agent_admin")
UNION
SELECT * FROM gpt_agent_config_customer WHERE type = 11 and  flag = 1 and  `status` = 2 and user_code in ("{user_code}","agent_admin") and customer_id = {customer_id}
'''
    return sql_query(customer_id, sql, config_map)

@cdss_llm_api.route('/llm_agent', methods=['POST'])
def llm_agent():
    request_data = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "error": 0,
        "status": 200
    }
    llm_agent_list = get_llm_agent_list(request_data, config_map)
    rlt['llm_agent_list'] = llm_agent_list
    return rlt


@cdss_llm_api.route('/cdss_chat_feedback', methods=['POST'])
def cdss_chat_feedback():
    request_data = json.loads(request.get_data().decode('utf-8'))
    rlt = {
        "error": 0,
        "status": 200
    }
    re = update_dialogue_feedback(request_data, config_map)
    if re < 0:
        rlt['error'] = -1
    return rlt


@cdss_llm_api.route('/dialogue_list', methods=['POST'])
def dialogue_list():
    request_data = json.loads(request.get_data().decode('utf-8'))
    rlt = {
            "error": 0,
            "status": 200,
            "dialogue_list": get_dialogue_list(request_data, config_map)
    }
    return rlt


@cdss_llm_api.route('/dialogue_contents', methods=['POST'])
def dialogue_contents():
    request_data = json.loads(request.get_data().decode('utf-8'))
    dialogue_content_list, dialogue_medical_record = get_dialogue_content_list(request_data, config_map)
    rlt = {
            "error": 0,
            "status": 200,
            "dialogue_content_list": dialogue_content_list,
            "dialogue_medical_record": dialogue_medical_record
    }
    return rlt

# 请求形式: /cdss_call_html?cache_key=cdss_call:dify_json:**********---**********---html
@cdss_llm_api.route('/cdss_call_html', methods=['GET'])
def cdss_call_html():
    """获取缓存中的HTML内容并返回"""
    cache_key = request.args.get('cache_key', '')
    if not cache_key:
        return jsonify({"error": 1, "message": "cache_key不能为空", "html": ""})
    
    html = redis_cache.get(cache_key)
    if html is None:
        return jsonify({"error": 1, "message": "未找到对应的缓存内容", "html": ""})
    
    return jsonify({"error": 0, "message": "获取成功", "html": html})

@cdss_llm_api.route('/clear_cdss_call_cache', methods=['DELETE'])
def clear_cdss_call_cache():
    uuid = request.args.get('uuid', '')
    if not uuid:
        return jsonify({"error": 1, "message": "uuid不能为空"})
    cache_key_status = f"cdss_call:status:{uuid}"
    cache_key_result = f"cdss_call:result:{uuid}"
    cache_key_dify_map = f"cdss_call:dify_map:{uuid}"
    redis_cache.delete(cache_key_status)
    redis_cache.delete(cache_key_result)
    redis_cache.delete(cache_key_dify_map)
    return jsonify({"error": 0, "message": "清除成功"})

@cdss_llm_api.route('/cdss_call', methods=['POST'])
def cdss_call():
    """
    获取CDSS分析结果的API接口
    
    接口支持高并发请求，通过Redis缓存进行状态和结果的管理
    """
    # 解析请求数据
    ret_data = json.loads(request.get_data().decode('utf-8'))
    logging.info(f"cdss_call request_data: {ret_data}")
    # 校验必要参数
    record_id = ret_data.get("record_id")
    customer_id = ret_data.get("customer_id")
    user_code = ret_data.get("doctor_guid", None)
    # 用户鉴权
    xs = auth_user(user_code, customer_id)
    if not xs:
        return jsonify(None)
    
    if None in [record_id, customer_id]:
        return jsonify({"error": 1, "message": "customer_id 或 record_id为空"})
    
    # 添加服务器信息
    ret_data["app_ip_port"] = f"{config_map['app']['app_ip']}:{config_map['app']['app_port']}"
    
    # 创建唯一标识符
    uuid = f"{customer_id}_{record_id}"
    # 获取API密钥信息并根据api_key_list过滤
    api_key_info = get_api_key_info(customer_id, xs)
    api_key_2_other_info = {}
    
    # 如果指定了api_key_list,只保留列表中的密钥
    if ret_data.get('api_key_list'):
        api_key_2_other_info = {k: v for k, v in api_key_info.items() if k in ret_data['api_key_list']}
    else:
        # 否则保留所有密钥
        api_key_2_other_info = api_key_info
    
    # 收集所有dify结果
    dify_list = []
    
    # 处理每个API密钥
    for api_key, other_info in api_key_2_other_info.items():
        # 定义缓存键
        cache_key_result = f"cdss_call:result:{uuid}-{api_key}"
        dify_result, success, error = process_api_key(ret_data, uuid, api_key, other_info, loop)
        if not success:
            cached_dify_result = redis_cache.get(cache_key_result)
            if cached_dify_result:
                if error:
                    cached_dify_result['error'] = error
                dify_list.append(cached_dify_result)
            else:
                dify_list.append(dify_result)
        else:
            refresh_dify_result(ret_data, uuid, api_key, loop)
            dify_list.append(dify_result)
        
        redis_cache.set(cache_key_result, dify_result, expire=86400)
    # 更新返回数据
    ret_data['dify_list'] = dify_list
    return jsonify(ret_data)


# def refresh_dify_result(ret_data, uuid, api_key):
#     """异步刷新指定API密钥的dify结果"""
#     cache_key_dify_map = f"cdss_call:dify_map:{uuid}-{api_key}"
#     run_async_task(async_process_cdss_call(ret_data, uuid, api_key, 0, cache_key_dify_map))


def has_dify_success(uuid, api_key):
    cache_key_dify_map = f"cdss_call:dify_map:{uuid}-{api_key}"
    dify_map = redis_cache.get(cache_key_dify_map)
    return dify_map and dify_map.get('status', '') == 'succeeded'


# def process_api_key(ret_data, uuid, api_key, other_info):
#     """处理单个API密钥的请求和结果"""
#     # 设置API密钥的缓存键
#     cache_key_dify_map = f"cdss_call:dify_map:{uuid}-{api_key}"
    
#     # 获取缓存中的dify_map
#     dify_map = redis_cache.get(cache_key_dify_map)
    
#     # 判断是否需要发起新请求
#     if should_make_new_request(dify_map):
#         # 发起新请求
#         run_async_task(async_process_cdss_call(ret_data, uuid, api_key, 0, cache_key_dify_map))
        
#         # 返回初始状态
#         error = ''
#         if dify_map:
#             error = dify_map.get('error', '')
#             if not error:
#                 if dify_map.get('status') == 'running' and time.time() - dify_map.get("start_time", time.time()) > 3600:
#                     error = 'dify超时1小时'
#         return create_initial_result(api_key, dify_map, other_info), False, error
    
#     # 检查message_id
#     message_id = dify_map.get("message_id")
#     if not message_id:
#         return create_initial_result(api_key, dify_map, other_info), False, ''
    
#     # 检查状态
#     status = dify_map.get("status")
#     # 处理成功的结果
#     result = process_successful_result(api_key, dify_map, other_info)
#     return result, True if status == 'succeeded' else False, dify_map.get('error', '')


def should_make_new_request(dify_map):
    """判断是否需要发起新请求"""
    return (
        not dify_map or 
        dify_map.get('status') == 'failed' or (
            dify_map.get('status') == 'running' and time.time() - dify_map.get("start_time", time.time()) > 3600 # 1小时超时
        )
    )


def create_initial_result(api_key, dify_map, agent_info):
    """创建初始状态的结果对象"""
    return {
        "api_key": api_key,
        "status": "running" if not dify_map else dify_map.get("status", "running"),
        "error": "" if not dify_map else dify_map.get("error", ""),
        "message_id": "" if not dify_map else dify_map.get("message_id", ""),
        "conversation_id": "" if not dify_map else dify_map.get("conversation_id", ""),
        "agent_code": agent_info.get('agent_code', ''),
        "warn_type": agent_info.get('warn_type', ''),
        "app_id": agent_info.get('app_id', '')
    }


def process_successful_result(api_key, dify_map, agent_info):
    """处理成功的结果"""
    # 获取基本信息
    message_id = dify_map.get("message_id", "")
    conversation_id = dify_map.get("conversation_id", "")
    status = dify_map.get("status", "")
    key_data = dify_map.get('data', {})
    error = dify_map.get('error', '')
    
    # 处理数据内容
    id_2_contents = defaultdict(list)
    for node_id, node_content in key_data.items():
        content = node_content.get('content', '')
        if '<begin id=' not in content:
            continue
            
        # 提取id
        match = re.search(r'<begin id=([0-9]+)', content)
        if not match:
            continue
            
        id_ = match.group(1)
        id_2_contents[id_].append(content)
    
    # 处理每个内容块
    data = []
    for id_, contents in id_2_contents.items():
        try:
            html, think, dify_json = _parse_dify_contents(contents)
            
            result_item = {
                'conversation_id': conversation_id,
                'message_id': message_id,
                'dify_json': dify_json
            }
            
            # 处理dify_json内容
            if dify_json:
                # 添加唯一标识符
                for i, row in enumerate(dify_json):
                    row['id'] = f"{conversation_id}---{id_}---{i}"
                
                result_item['dify_json'] = dify_json
                
                # 缓存HTML内容
                if html:
                    cache_key = f"cdss_call:dify_json:{conversation_id}---{dify_json[0]['id']}---html"
                    redis_cache.set(cache_key, html, expire=86400)
                    result_item['cacheKey'] = cache_key
                if think:
                    cache_key = f"cdss_call:dify_json:{conversation_id}---{dify_json[0]['id']}---think"
                    redis_cache.set(cache_key, think, expire=86400)
                    result_item['cache_key_think'] = cache_key
            
            data.append(result_item)
        except Exception as e:
            logging.error(f"处理内容块出错: {str(e)}, id: {id_}")
    
    # 返回处理结果
    return {
        "api_key": api_key,
        "message_id": message_id,
        "conversation_id": conversation_id,
        "data": data,
        "status": status,
        "agent_code": agent_info.get('agent_code', ''),
        "warn_type": agent_info.get('warn_type', ''),
        "app_id": agent_info.get('app_id', ''),
        "error": error
    }


def update_success_cache(cache_key_status, cache_key_result, ret_data, uuid, api_key_list):
    """更新成功状态的缓存"""
    # 设置成功状态，一天有效期
    redis_cache.set(cache_key_status, "succeeded", expire=86400)
    redis_cache.set(cache_key_result, ret_data, expire=86400)
    
    # 清空API密钥相关的缓存
    for api_key in api_key_list:
        cache_key_dify_map = f"cdss_call:dify_map:{uuid}-{api_key}"
        redis_cache.delete(cache_key_dify_map)

async def async_process_cdss_call(ret_data, uuid, api_key, index, cache_key_dify_map):
    """异步处理单个请求
    
    Args:
        ret_data: 请求数据
        uuid: 用户唯一标识
        api_key: API密钥
        index: 索引值
        cache_key_dify_map: 缓存键名
    """
    # 有效的事件类型集合
    valid_event = {"workflow_started", "workflow_finished", "node_started", "node_finished"}
    
    # 创建初始结果
    initial_result = {
        'error': "",
        "message_id": None, 
        "conversation_id": None, 
        "status": "running", 
        "start_time": time.time(), 
        'data': {},
        'api_key': api_key
    }
    node_data = initial_result['data']
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)  # 1天过期
    try:
        # 设置客户端超时
        timeout = aiohttp.ClientTimeout(total=60*60, connect=30*60)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            url = f'http://{config_map["llm_model"]["dify_ip"]}:{config_map["llm_model"]["dify_port"]}/v1/chat-messages'
            
            logging.info(f"开始调用dify接口, uuid:{uuid}, api_key:{api_key}")
            
            try:
                async with session.post(
                    url=url,
                    json={
                        "inputs": ret_data,
                        "query": "1",
                        "response_mode": "streaming", 
                        "user": f"user-{uuid}"  # 使用uuid作为用户标识，提高可追踪性
                    },
                    headers={"Authorization": f"Bearer {api_key}"}
                ) as response:
                    if response.status != 200:
                        await handle_error_response(response, initial_result, cache_key_dify_map, uuid, api_key)
                        return
                    
                    logging.info(f"dify接口返回200响应, 开始处理流数据, uuid:{uuid}, api_key:{api_key}")
                    message_id_received = False
                    
                    # 处理响应流
                    async for chunk in response.content:
                        if not chunk.startswith(b'data: '):
                            continue
                            
                        chunk = chunk[6:].strip()
                        
                        # 解析JSON
                        try:
                            record_obj = json.loads(chunk)
                        except Exception:
                            continue
                            
                        event = record_obj.get("event", "")
                        if event not in valid_event:
                            continue
                            
                        # 处理workflow_started事件
                        if event == "workflow_started":
                            message_id_received = await handle_workflow_started(
                                record_obj, initial_result, api_key, cache_key_dify_map, uuid
                            )
                            
                        # 处理node_started事件
                        elif event == 'node_started':
                            await handle_node_started(
                                record_obj, node_data
                            )
                            
                        # 处理node_finished事件
                        elif event == 'node_finished':
                            await handle_node_finished(
                                record_obj, initial_result, node_data, cache_key_dify_map
                            )
                            
                        # 处理workflow_finished事件
                        elif event == 'workflow_finished':
                            await handle_workflow_finished(
                                record_obj, initial_result, node_data, api_key, cache_key_dify_map, uuid
                            )
                    
                    # 检查是否获取到message_id
                    if not message_id_received and initial_result.get("message_id") is None:
                        logging.error(f"未获取到message_id, uuid:{uuid}, api_key:{api_key}")
                        initial_result["status"] = "failed"
                        initial_result["error"] = "未能获取到message_id"
                        # 更新Redis缓存
                        redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
            
            except asyncio.TimeoutError:
                logging.error(f"请求超时, uuid:{uuid}, api_key:{api_key}")
                initial_result["status"] = "failed"
                initial_result["error"] = "请求超时"
                redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
                
            except aiohttp.ClientError as e:
                logging.error(f"网络请求错误: {e}, uuid:{uuid}, api_key:{api_key}")
                initial_result["status"] = "failed"
                initial_result["error"] = f"网络请求错误: {str(e)}"
                redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    
    except Exception as e:
        logging.error(f"异步处理dify接口失败: {e}, uuid:{uuid}, api_key:{api_key}")
        # 获取最新的dify_map并更新错误状态
        initial_result["status"] = "failed"
        initial_result["error"] = f"处理异常: {str(e)}"
        redis_cache.set(cache_key_dify_map, initial_result, expire=86400)

# 辅助函数：处理错误响应
async def handle_error_response(response, initial_result, cache_key_dify_map, uuid, api_key):
    """处理HTTP错误响应"""
    try:
        error_json = await response.json()
    except Exception:
        error_json = {"code": "unknown", "message": f"HTTP错误: {response.status}"}
    
    initial_result["code"] = error_json.get("code", "unknown")
    initial_result["error"] = error_json.get("message", "Unknown error")
    initial_result["status"] = "failed"
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    
    logging.error(f"调用dify接口失败, uuid:{uuid}, api_key:{api_key}, 状态码:{response.status}, 错误:{error_json}")

# 辅助函数：处理workflow_started事件
async def handle_workflow_started(record_obj, initial_result, api_key, cache_key_dify_map, uuid):
    """处理workflow_started事件"""
    message_id = record_obj.get("message_id", "")
    logging.info(f"获取到message_id, uuid:{uuid}, api_key:{api_key}, message_id:{message_id}")
    initial_result["message_id"] = message_id
    initial_result["status"] = "running"
    initial_result["conversation_id"] = record_obj.get("conversation_id", "")
    # 更新Redis缓存
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    return True  # 表示已获取message_id

# 辅助函数：处理node_started事件
async def handle_node_started(record_obj, node_data):
    """处理node_started事件"""
    node_id = record_obj.get('data', {}).get('node_id', '')
    title = record_obj.get('data', {}).get('title', '')
    node_type = record_obj.get('data', {}).get('node_type', '')
    
    # 只处理answer类型节点
    if node_type != 'answer':
        return
        
    # 创建节点数据
    node_data[node_id] = {
        "title": title,
        "node_type": node_type,
        "content": "",
        "status": "running",
        "error": ""
    }

# 辅助函数：处理node_finished事件
async def handle_node_finished(record_obj, initial_result, node_data, cache_key_dify_map):
    """处理node_finished事件"""
    node_id = record_obj.get('data', {}).get('node_id', '')
    
    # 检查节点是否存在
    if node_id not in node_data:
        return
        
    # 更新节点数据
    node_data[node_id]['status'] = record_obj.get('data', {}).get('status', '')
    node_data[node_id]['error'] = record_obj.get('data', {}).get('error', '')
    node_data[node_id]['content'] = record_obj.get('data', {}).get('outputs', {}).get('answer', '')
    
    # 更新结果
    initial_result['data'] = node_data
    
    # 更新Redis缓存(每个节点完成时都更新)
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)

# 辅助函数：处理workflow_finished事件
async def handle_workflow_finished(record_obj, initial_result, node_data, api_key, cache_key_dify_map, uuid):
    """处理workflow_finished事件"""
    message_id = record_obj.get("message_id", "")
    status = record_obj.get('data', {}).get('status', '')
    error = record_obj.get('data', {}).get('error', '')
    
    # 更新结果
    initial_result["message_id"] = message_id
    initial_result["status"] = status or "succeeded"  # 默认为成功
    
    # 处理错误情况
    if error:
        initial_result["error"] = error
        initial_result["status"] = "failed"
        
    # 确保数据被包含
    initial_result['data'] = node_data
    
    # 更新Redis缓存
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    
    logging.info(f"工作流完成, uuid:{uuid}, api_key:{api_key}, status:{initial_result['status']}, error:{initial_result['error']}")



@cdss_llm_api.route('/cdss_stream_chat', methods=['POST'])
def cdss_stream_chat():
    rlt = {
        "head": {"error": 0},
        "code": 200,
        "body":{},
        "message": '操作成功',
        "has_error":False
    }
    input_max_len = int(config_map['llm_model']['input_max_len'])
    request_data = json.loads(request.get_data().decode('utf-8'))
    prompt = request_data.get('prompt', '').strip()
    prompt_id = int(request_data.get('prompt_id', -1))
    prompt_type = request_data.get('prompt_type', '')
    prompt_type_code = request_data.get('prompt_type_code', '')
    customer_id = request_data.get('customer_id', 1001)
    record_id = request_data.get('record_id', None)
    serial_number = request_data.get('serial_number', None)
    if not record_id and serial_number:
        _, record_id, _ = get_record_id(customer_id, serial_number, config_map)
    progress_id_list = request_data.get('progress_id_list', [])
    record_date = request_data.get('record_date', '')
    dialogue_code = request_data.get('dialogue_code', '')
    session_id = request_data.get('session_id', '')
    stream_code = request_data.get('stream_code', '')
    user_code = request_data.get('user_code', '')
    user_name = request_data.get('user_name', '')
    department_code = request_data.get('department_code', '')
    department_name = request_data.get('department_name', '')
    new_chat = request_data.get('new_chat', 0)
    dialogue_stop = request_data.get('dialogue_stop', 0)

    agent_id = int(request_data.get('agent_id', -1))
    agent_name = request_data.get('agent_name', '')
    agent_code = request_data.get('agent_code', '')
    need_think = request_data.get('need_think', 1)
    conversation_id = request_data.get('conversation_id', '')
    cache_key = request_data.get('cache_key_think', '')
    if cache_key:
        # dify_url = f"http://{config_map['llm_model']['dify_ip']}:{config_map['llm_model']['dify_port']}/console/api/apps/{message_id}/message"
        # headers = { 'Authorization': 'Bearer **********' }
        # payload = {}
        # response = requests.request("GET", dify_url, headers=headers, data=payload)
        content = redis_cache.get(cache_key)
        if content:
            rlt['body']['content'] = content
            rlt['body']["dialogue_error_flag"]= False
            rlt['body']["dialogue_end_flag"]= True
        # rlt['body']['last_content'] = content
        else:
            rlt['body']['content'] = "未查询到缓存信息"
        return rlt


    access_flag = request_data.get("access_flag", 'm')
    cdss_type = 'mt'
    if access_flag == 'c':
        cdss_type = 'ot'

    chat_param = {"prompt":prompt,"record_date":record_date}
    rlt['stream_code'] = stream_code
    if stream_code and prompt_type_code and prompt_type_code == 'llm_cdss_qc' and not prompt:
        return get_llm_qc_stream_result(prompt_type,prompt_type_code, stream_code, dialogue_code, user_code,user_name,department_code,department_name,customer_id,record_id,config_map)
    if stream_code and stream_code in stream_code_map:
        stream_code_map[stream_code]['dialogue_error_flag'] = False
        # 请求多次无返回
        if not stream_code_map[stream_code]['origin_content'] and not stream_code_map[stream_code]['content'] and not stream_code_map[stream_code]['agent_query']:
            if stream_code_map[stream_code].get('req_num', 0) > int(config_map['llm_model'].get('stream_overtime', 20))//2:
                rlt['body'] = {"content":"您好，现在使用人数较多，请稍后重试！", "dialogue_error_flag":True, "dialogue_end_flag":True}
                dify_id = stream_code_map[stream_code].get('dify_id', "")
                g_monitor.set_agent_fail_gauage(agent_name,dify_id, 503)
                stream_code_map.pop(stream_code)
                dialogue_update_param = {'flag': 2, 'record_id': record_id, 'stream_code': stream_code}
                update_dialogue_content(dialogue_update_param, config_map)
                dify_id = stream_code_map[stream_code].get('dify_id', '')
                g_monitor.set_agent_fail_gauage(agent_name, dify_id, 503)
                if 'contents' not in rlt['body']:
                    rlt['body']['contents'] = [{"attribute_name": "llm_mrg", "content": rlt['body']['content']}]
                return rlt
            stream_code_map[stream_code]['req_num'] = stream_code_map[stream_code].get('req_num', 0)+1
        # if prompt_type_code.startswith('llm_rag'):
        #     rag_index_post_process(customer_id, record_id, stream_code)
        if dialogue_stop == 1:
            # 主动停止生成
            rlt['body'] = {"content": stream_code_map[stream_code]['content'], "dialogue_error_flag": False,
                           "dialogue_end_flag": True, "dialogue_stop": 1}
            dialogue_update_param = {'flag': 3, 'record_id': record_id, 'stream_code': stream_code, 'ai_content': stream_code_map[stream_code]['content']}
            stream_code_map.pop(stream_code)
            update_dialogue_content(dialogue_update_param, config_map)
            if 'contents' not in rlt['body']:
                rlt['body']['contents'] = [{"attribute_name": "llm_mrg", "content": rlt['body']['content']}]
            return rlt
        rlt['body'] = copy.deepcopy(stream_code_map[stream_code])
        if stream_code_map[stream_code]['dialogue_end_flag']:
            if 'reference_param' in stream_code_map[stream_code]:
                progress_references = []
                if 'progress_list' in stream_code_map[stream_code]['reference_param']:
                    chat_param = {"prompt": stream_code_map[stream_code]['reference_param']['prompt'], "record_date": record_date,
                                  "progress_list": stream_code_map[stream_code]['reference_param']['progress_list']}
                    dialogue_update_param = {'flag': stream_code_map[stream_code].get('flag', 0), 'chat_param': chat_param, 'record_id': record_id,
                                             'stream_code': stream_code, 'conversation_id':stream_code_map[stream_code].get('conversation_id''')}
                    update_dialogue_content(dialogue_update_param, config_map)
                    progress_references = get_progress_references(customer_id, record_id, stream_code_map[stream_code]['reference_param']['prompt'], stream_code_map[stream_code]['reference_param']['progress_list'], config_map)
                elif 'prompt_progress_map' in stream_code_map[stream_code]['reference_param']:
                    progress_references = get_progress_references_discharge(customer_id, record_id, stream_code_map[stream_code]['reference_param']['prompt_progress_map'],
                                                                        stream_code_map[stream_code]['reference_param']['real_idx_map'], config_map)
                if progress_references:
                    rlt['body']['progress_references'] = progress_references
            stream_code_map.pop(stream_code)
        rlt['body']['reference_param'] = {}
        if 'contents' not in rlt['body']:
            rlt['body']['contents'] = [{"attribute_name": "llm_mrg", "content": rlt['body']['content']}]
        return rlt

    # agent start
    if record_id and agent_code:
        agent_name, agent_code, dify_api, secret_key, dify_id = get_llm_agent_by_code(agent_code, config_map, customer_id)
        if not secret_key:
            rlt['body'] = {"agent_query": '请检查对应的agent是否已上线！',"content": '请检查对应的agent是否已上线！', "dialogue_error_flag": True, "dialogue_end_flag": True}
            return rlt
        # agent_name, agent_code, dify_api, secret_key = 'test', 'xxx', 'http://172.16.7.10/v1/chat-messages', 'app-wyEvn4zWlx3l1JeaF1XeFyaD'
        stream_code_map[stream_code] = {"content": '',"agent_query": '', "dialogue_error_flag": False, "dialogue_end_flag": False}
        use_history = 0
        conversation_id = ''
        parent_message_id = ''
        pre_query = ''
        class_name = ''
        if dialogue_code in dialogue_code2chat_map:
            use_history = dialogue_code2chat_map[dialogue_code].get('use_history', 0)
            conversation_id = dialogue_code2chat_map[dialogue_code].get('conversation_id', '')
            parent_message_id = dialogue_code2chat_map[dialogue_code].get('parent_message_id', '')
            pre_query = dialogue_code2chat_map[dialogue_code].get('pre_query', '')
            class_name = dialogue_code2chat_map[dialogue_code].get('class_name', '')
        if not progress_id_list:
            progress_id_list = stream_code_map[stream_code].get('progress_id_list', [])
        else:
            stream_code_map[stream_code]['progress_id_list'] = progress_id_list
        llm_param = {
            "use_history": use_history,
            "new_chat": new_chat,
            "prompt": prompt,
            "agent_id": agent_id,
            "dify_id":dify_id,
            "agent_name": agent_name,
            "agent_code": agent_code,
            "progress_id_list": progress_id_list,
            "dify_api": dify_api,
            "secret_key": secret_key,
            "conversation_id": conversation_id,
            "parent_message_id": parent_message_id,
            "pre_query": pre_query,
            "class_name": class_name,
            "prompt_type": prompt_type,
            "prompt_type_code": prompt_type_code,
            "history_list": [],
            "session_id": session_id,
            "stream_code": stream_code,
            "dialogue_code": dialogue_code,
            "user_code": user_code,
            "user_name": user_name,
            "department_code": department_code,
            "department_name": department_name,
            "customer_id": customer_id,
            "record_id": record_id,
            "record_date": record_date,
            "cdss_type": cdss_type,
            "need_think": need_think,
            "chat_param": chat_param
        }
        llm_param = merge_and_deduplicate(llm_param, request_data)
        asyncio.run(task_call_with_stream_qwen(llm_param, loop))
        if prompt:
            if dialogue_code not in dialogue_code2chat_map:
                dialogue_code2chat_map[dialogue_code] = {}
            dialogue_code2chat_map['pre_query'] = prompt
        rlt['body'] = {"content": '', "dialogue_error_flag": False, "dialogue_end_flag": False}
        return rlt
    # agent end

    history_list = []
    record2prompt = {}
    prompt_input = ''
    if not new_chat and dialogue_code:
        history_list = get_cdss_chat_history(dialogue_code, config_map, input_max_len)
    # if (new_chat and prompt_type != '生成术前讨论记录') or (len(history_list) == 0 and prompt_type == '生成术前讨论记录'):
    if record_id and not prompt_type_code.startswith('llm_rag') and prompt_type_code not in ['llm_cdss_gen_cyjl2']:
        if (new_chat and prompt_type_code not in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_ssjl']) or (len(history_list) == 0 and prompt_type_code in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_ssjl']):
            if prompt_id > 0:
                prompt_template, need_date = get_llm_prompt_by_id(prompt_id, config_map,customer_id)
                prompt_input, progress_list = get_input_by_prompt_type(customer_id, record_id, prompt_type_code, record_date, config_map, input_max_len)
                chat_param['progress_list'] = progress_list
                if prompt_type_code in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_ssjl']:
                    prompt_input = '拟采用的麻醉方式及术式:{}\n'.format(prompt) + prompt_input
                prompt = prompt_template.replace('{input}', prompt_input[:input_max_len]).replace('{record_date}', record_date)
                record2prompt = get_dialogue_content(record_id, config_map)
            if len(progress_id_list) > 0:
                prompt_template, _ = get_llm_prompt_by_id(6, config_map, customer_id,custom_prompt_flag=False)
                prompt_input, progress_list = get_input_by_progress_id(customer_id, progress_id_list, config_map, input_max_len,cdss_type)
                prompt = prompt_template.replace('{input}', prompt_input[:input_max_len]).replace('{question}',prompt)
                # prompt = prompt_input+'\n'+prompt
                chat_param['progress_list'] = progress_list
                prompt_type = '基于病历的自由问答'
                prompt_type_code = 'llm_medical_chat'
    prompt = re.sub(r'\x20+', ' ', prompt)
    if prompt_type_code.startswith('llm_rag'):
        prompt = prompt.replace('@{}，'.format(prompt_type), '')
    if record_id in record2prompt:
        prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
        if prompt_hash in record2prompt[record_id]:
            stream_code, ai_content = record2prompt[record_id][prompt_hash]
            rlt['body'] = {"content": ai_content, "dialogue_error_flag": False, "dialogue_end_flag": True}
            if 'progress_list' in chat_param:
                progress_references = get_progress_references(customer_id, record_id,prompt,chat_param['progress_list'], config_map)
                if progress_references:
                    rlt['body']['progress_references'] = progress_references
            rlt['stream_code'] = stream_code
            if 'contents' not in rlt['body']:
                rlt['body']['contents'] = [{"attribute_name": "llm_mrg", "content": rlt['body']['content']}]
            return rlt

    content = ''
    if prompt_type_code.startswith('llm_rag'):
        content = ''
    elif new_chat and prompt_type_code in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_ssjl'] and not request_data.get('prompt', '').strip():
        content = '请输入拟采用的麻醉方式及术式'
    elif new_chat and prompt_type_code and prompt_type_code not in ['llm_cdss_gen_cyjl2'] and not prompt_input:
        content = '很抱歉，未检索到患者近1月内的就诊信息，无法{}。'.format(prompt_type)
    if not content:
        stream_code_map[stream_code] = {"content": '',"agent_query": '', "dialogue_error_flag": False, "dialogue_end_flag": False}
        if 'progress_list' in chat_param:
            stream_code_map[stream_code]['reference_param'] = {'progress_list':chat_param['progress_list'], 'prompt':prompt}
            # stream_code_map[stream_code]['progress_list'] = chat_param['progress_list']
            # stream_code_map[stream_code]['prompt'] = prompt
        if not prompt_type:
            prompt_type = '自由问答'
            prompt_type_code = 'llm_chat'
        llm_param = {
            "new_chat": new_chat,
            "prompt": prompt,
            "prompt_type": prompt_type,
            "prompt_type_code": prompt_type_code,
            "history_list": history_list,
            "session_id": session_id,
            "stream_code": stream_code,
            "dialogue_code": dialogue_code,
            "user_code": user_code,
            "user_name": user_name,
            "department_code": department_code,
            "department_name": department_name,
            "customer_id": customer_id,
            "record_id": record_id,
            "record_date": record_date,
            "chat_param": chat_param
        }
        asyncio.run(task_call_with_stream_qwen(llm_param,loop))
        rlt['body'] = {"content": content, "dialogue_error_flag": False, "dialogue_end_flag": False}
    else:
        rlt['body'] = {"content":content, "dialogue_error_flag":False, "dialogue_end_flag":True}
    if 'contents' not in rlt['body']:
        rlt['body']['contents'] = [{"attribute_name": "llm_mrg", "content": rlt['body']['content']}]
    return rlt


async def task_call_with_stream_qwen(llm_param,loop):
    try:
        prompt = llm_param["prompt"]
        agent_code = llm_param.get("agent_code","")
        dialogue_param = {'dialogue_code': llm_param["dialogue_code"],
                          'stream_code': llm_param["stream_code"],
                          'user_code': llm_param["user_code"],
                          'user_name': llm_param["user_name"],
                          'department_code': llm_param["department_code"],
                          'department_name': llm_param["department_name"],
                          'prompt': prompt,
                          'prompt_type': llm_param["prompt_type"],
                          'prompt_type_code': llm_param["prompt_type_code"],
                          'ai_content': '',
                          'chat_model': '',
                          'customer_id': llm_param["customer_id"],
                          'record_id': llm_param["record_id"],
                          'doc_references': [],
                          'chat_param': llm_param['chat_param']}
        if agent_code:
            dialogue_param['agent_code'] = agent_code
            dialogue_param['agent_name'] = llm_param["agent_name"]
            save_dialogue_content(dialogue_param, config_map)
            asyncio.run_coroutine_threadsafe(call_with_stream_dify_agent(llm_param), loop)
    except Exception as e:
        logging.error(f"大模型发生未预期的错误: {e}, agent_name:{llm_param.get('agent_name','')}, prompt_type_code:{llm_param.get('prompt_type_code','')}", exc_info=True)


async def call_with_stream_dify_agent(llm_param):
    user_code = llm_param["user_code"]
    stream_code = llm_param["stream_code"]
    dialogue_code = llm_param["dialogue_code"]
    prompt = llm_param["prompt"]
    dify_id = llm_param.get("dify_id", "")
    dify_api = llm_param["dify_api"]
    secret_key = llm_param["secret_key"]
    conversation_id = llm_param["conversation_id"]
    parent_message_id = llm_param["parent_message_id"]
    agent_query = llm_param.get('agent_query', '')
    agent_id = llm_param.get('agent_id', '')
    dify_id = llm_param.get("dify_id")


    headers = {
        'Authorization': f'Bearer {secret_key}',
        'Content-Type': 'application/json'
    }
    progress_ids=''
    if llm_param['progress_id_list']:
        progress_ids = ','.join([str(x) for x in llm_param['progress_id_list']])
    input_param = {"use_history": str(llm_param["use_history"]),
                "new_chat": str(llm_param["new_chat"]),
                "prompt": prompt,"customer_id": str(llm_param["customer_id"]),
                "record_id": str(llm_param["record_id"]),
                "record_date": llm_param["record_date"],
                "progress_ids": progress_ids,
                "stream_code": stream_code,
                "dialogue_code": dialogue_code,
                "pre_query": llm_param["pre_query"],
                "class_name": llm_param["class_name"],
                "app_ip_port": '{}:{}'.format(config_map['app']['app_ip'], config_map['app']['app_port'])
                   }
    input_param = merge_and_deduplicate(input_param, llm_param)
    payload = {
        "inputs": input_param,
        "query": ' ' if not prompt else prompt,
        "response_mode": "streaming",
        "conversation_id": conversation_id,
        "user": user_code
    }
    if parent_message_id:
        payload["parent_message_id"] = parent_message_id
    print(json.dumps(payload, indent=4, ensure_ascii=False))
    payload_list = [payload]

    # 给dify api的输入
    if 'llm_sub_param_list' in llm_param:
        payload_list = []
        for k_v in llm_param['llm_sub_param_list']:
            input_param2 = copy.deepcopy(input_param)
            for k in k_v:
                input_param2[k] = k_v[k]
            prompt = input_param2.get('prompt', '')
            payload = {
                "inputs": input_param2,
                "query": ' ' if not prompt else prompt,
                "response_mode": "streaming",
                "conversation_id": conversation_id,
                "user": user_code
            }
            payload_list.append(payload)
    answer_info_type = 'content'
    try:
        for idx, payload in enumerate(payload_list):
            # 智能体promethues监控
            g_monitor.set_agent_count(agent_name, dify_id)
            async with aiohttp.ClientSession() as session:
                async with session.post(dify_api, headers=headers, json=payload) as response:
                    # start_time = time.time()
                    async for chunk in response.content.iter_any():
                        # print(chunk.decode("utf-8"))
                        if stream_code not in stream_code_map:
                            return
                        if stream_code_map[stream_code]['dialogue_error_flag']:
                            if time.time() - start_time > int(config_map['llm_model'].get('stream_overtime', 20)):
                                dialogue_update_param = {'flag': 4, 'record_id': llm_param["record_id"],
                                                         'stream_code': stream_code,
                                                         'ai_content': stream_code_map[stream_code]['content']}
                                stream_code_map.pop(stream_code)
                                update_dialogue_content(dialogue_update_param, config_map)
                                return
                        else:
                            start_time = time.time()
                            stream_code_map[stream_code]['dialogue_error_flag'] = True
                        chunk2 = chunk.decode("utf-8")
                        # print(chunk2)
                        # if chunk2.find('invalid_param')>-1 or chunk2.startswith('Invalid'):
                        #     stream_code_map[stream_code]['dialogue_error_flag'] = True
                        #     stream_code_map[stream_code]['dialogue_end_flag'] = True
                        #     stream_code_map[stream_code]['content'] = chunk2
                        #     break
                        if chunk2.find('data: {') == -1 or not chunk2:
                            continue
                        chunk_list = chunk2.split('data: ')
                        for chunk3 in chunk_list:
                            chunk3 = chunk3.strip()
                            if chunk3.startswith('{') and chunk3.endswith('}'):
                                res = json.loads(chunk3)
                                if res['event'] in ['message', 'agent_message']:
                                    output = res['answer']
                                    if not output:
                                        continue
                                    # print(output)
                                    if config_map['llm_model']['pre_think']== '1' and len(stream_code_map[stream_code]['origin_content'])==0:
                                        stream_code_map[stream_code]['origin_content'] = stream_code_map[stream_code]['origin_content']+'<think>'
                                    if 'info_type' not in res:
                                        res['info_type'] = 'content'
                                    if answer_info_type=='content' and res['info_type'] and res['info_type']=='think':
                                        answer_info_type = res['info_type']
                                        if len(stream_code_map[stream_code]['origin_content']) == 0:
                                            stream_code_map[stream_code]['origin_content'] = stream_code_map[stream_code]['origin_content'] + '<think>'
                                    elif answer_info_type=='think' and res['info_type'] and res['info_type']=='content':
                                        answer_info_type = res['info_type']
                                        stream_code_map[stream_code]['origin_content'] = stream_code_map[stream_code]['origin_content'] + '</think>'

                                    output = output.replace('<agent_query>', '').replace('</agent_query>', '')
                                    stream_code_map[stream_code]['origin_content'] = stream_code_map[stream_code]['origin_content'] + output


                                    if agent_query:
                                        # output = output.replace('<agent_query>', '').replace('</agent_query>', '')
                                        # stream_code_map[stream_code]['origin_content'] = stream_code_map[stream_code]['origin_content'] + output
                                        if 'info_type' in res and res['info_type']=='think':
                                            continue
                                        if stream_code_map[stream_code]['origin_content'].find('<think>')>-1:
                                            if stream_code_map[stream_code]['origin_content'].find('</think>') > -1:
                                                content_start = stream_code_map[stream_code]['origin_content'].find('</think>')+len('</think>')
                                                stream_code_map[stream_code]['agent_query'] = str(stream_code_map[stream_code]['origin_content'][content_start:]).lstrip()
                                            continue
                                        stream_code_map[stream_code]['agent_query'] = str(stream_code_map[stream_code]['agent_query'] + output).lstrip()
                                        continue
                                    if res['conversation_id']:
                                        if dialogue_code not in dialogue_code2chat_map:
                                            dialogue_code2chat_map[dialogue_code] = {
                                                'conversation_id': res['conversation_id']}
                                        else:
                                            dialogue_code2chat_map[dialogue_code]['conversation_id'] = res[
                                                'conversation_id']
                                    if res['message_id']:
                                        if dialogue_code not in dialogue_code2chat_map:
                                            dialogue_code2chat_map[dialogue_code] = {
                                                'parent_message_id': res['message_id']}
                                        else:
                                            dialogue_code2chat_map[dialogue_code]['parent_message_id'] = res[
                                                'message_id']
                                    stream_code_map[stream_code]['conversation_id'] = res['conversation_id']
                                    if output.find('###timeframe_flag###') > -1 or output.find('###record_tree_flag###')>-1:
                                        if output.find('###timeframe_flag###')>-1:
                                            stream_code_map[stream_code]['timeframe_flag'] = True
                                            output = output.replace('###timeframe_flag###', '')
                                        elif output.find('###record_tree_flag###')>-1:
                                            stream_code_map[stream_code]['record_tree_flag'] = True
                                            output = output.replace('###record_tree_flag###', '')
                                            stream_code_map[stream_code]['record_tree'] = get_record_tree(llm_param["customer_id"], llm_param["record_id"], config_map, llm_param.get('cdss_type','mt'))
                                        stream_code_map[stream_code]['content'] = stream_code_map[stream_code]['content'] + output
                                        stream_code_map[stream_code]['dialogue_end_flag'] = True
                                        # 超时
                                        g_monitor.set_agent_fail_gauage(agent_name,dify_id, 503)
                                        break
                                    # if output.find('###class_name:')>-1:
                                    #     start = output.find('###class_name:')+len('###class_name:')
                                    #     end = start + output[start:].find('###')
                                    #     class_name = output[start:end]
                                    #     if dialogue_code not in dialogue_code2chat_map:
                                    #         dialogue_code2chat_map[dialogue_code] = {}
                                    #     dialogue_code2chat_map['class_name'] = class_name
                                    #     output = output.replace('###class_name:{}###'.format(class_name), '')

                                    # if output.find('<agent_query>')>-1:
                                    #     start = output.find('<agent_query>')+len('<agent_query>')
                                    #     end = start + output[start:].find('</agent_query>')
                                    #     agent_query = output[start:end]
                                    #     stream_code_map[stream_code]['agent_query'] = agent_query
                                    #     continue
                                    if llm_param.get('need_think', 1)==0 and stream_code_map[stream_code]['origin_content'].find('<think>')>-1:
                                        if stream_code_map[stream_code]['origin_content'].find('</think>') > -1:
                                            content_start = stream_code_map[stream_code]['origin_content'].find('</think>')+len('</think>')
                                            stream_code_map[stream_code]['content'] = str(stream_code_map[stream_code]['origin_content'][content_start:]).lstrip()
                                    else:
                                        stream_code_map[stream_code]['content'] = stream_code_map[stream_code]['origin_content']
                                elif res['event'] in ['tts_message_end','message_end'] and idx == len(payload_list)-1:
                                    stream_code_map[stream_code]['dialogue_end_flag'] = True
                                    stream_code_map[stream_code]['conversation_id'] = res['conversation_id']
                                    dialogue_update_param = {'flag': 1, 'record_id': llm_param["record_id"],
                                                             'stream_code': stream_code,
                                                             'ai_content': stream_code_map[stream_code]['content'],
                                                             'images': stream_code_map[stream_code].get('images',[]),
                                                             'conversation_id':res['conversation_id']}
                                    update_dialogue_content(dialogue_update_param, config_map)
                                elif res['event'] == 'message_file':
                                    if res['type'] == 'image':
                                        image_url = '{}{}'.format(dify_api.replace('/v1/chat-messages/','').replace('/v1/chat-messages',''),res['url'])
                                        if 'images' not in stream_code_map[stream_code]:
                                            stream_code_map[stream_code]['images'] = []
                                        stream_code_map[stream_code]['images'].append(image_url)
                                elif res['event'] == 'error':
                                    if res['conversation_id']:
                                        if dialogue_code not in dialogue_code2chat_map:
                                            dialogue_code2chat_map[dialogue_code] = {
                                                'conversation_id': res['conversation_id']}
                                        else:
                                            dialogue_code2chat_map[dialogue_code]['conversation_id'] = res[
                                                'conversation_id']
                                    if res['message_id']:
                                        if dialogue_code not in dialogue_code2chat_map:
                                            dialogue_code2chat_map[dialogue_code] = {
                                                'parent_message_id': res['message_id']}
                                        else:
                                            dialogue_code2chat_map[dialogue_code]['parent_message_id'] = res[
                                                'message_id']
                                    stream_code_map[stream_code]['conversation_id'] = res['conversation_id']
                                    raw_res = res.get("message", "Execution failed, terminating process.")
                                    g_monitor.set_agent_fail_gauage(agent_name,dify_id, 500)
                                    
                                    raw_res = remove_dify_error_header(raw_res)
                                    stream_code_map[stream_code]['dialogue_error_flag'] = True
                                    stream_code_map[stream_code]['dialogue_end_flag'] = True
                                    stream_code_map[stream_code]['content'] = stream_code_map[stream_code]['content'] + raw_res
    except Exception as e:
        logging.error(f"调用Dify API时发生未预期的错误: {e}, agent_name:{llm_param.get('agent_name','')}", exc_info=True)
        stream_code_map[stream_code]['dialogue_error_flag'] = True
        stream_code_map[stream_code]['dialogue_end_flag'] = True
        stream_code_map[stream_code]['content'] = chunk2


@cdss_llm_api.route('/llm_prompt_input', methods=['POST'])
def llm_prompt_input():
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    dialogue_code = request_data.get('dialogue_code', '')
    stream_code = request_data.get('stream_code', '')
    cdss_type = request_data.get('cdss_type', 'mt')
    if dialogue_code not in dialogue_code2chat_map:
        dialogue_code2chat_map[dialogue_code] = {'use_history': 1}
    else:
        dialogue_code2chat_map[dialogue_code]['use_history'] = 1
        dialogue_code2chat_map[dialogue_code]['class_name'] = ''

    input_param_list = []
    if "input_param_list" not in request_data:
        input_param_list.append({'input_type':'text',
                                 'start_date':request_data.get('start_date', ''),
                                 'end_date':request_data.get('end_date', ''),
                                 'progress_type_in':request_data.get('progress_type_in', ''),
                                 'progress_type_not_in':request_data.get('progress_type_not_in', ''),
                                 'progress_type_filter':request_data.get('progress_type_filter', ''),
                                 })
        if request_data.get('need_format_data', 0):
            for input_type in ['order', 'exam', 'test']:
                input_param_list.append({'input_type':input_type,
                                 'start_date':request_data.get('start_date', ''),
                                 'end_date':request_data.get('end_date', '')
                                 })
    else:
        input_param_list = request_data.get('input_param_list', [])
    prompt_input = ''
    progress_list = []
    prompt_input, progress_list = get_llm_prompt_input(prompt_input, progress_list, input_param_list, cid, rid, cdss_type, config_map)

    if stream_code and stream_code in stream_code_map:
        stream_code_map[stream_code]['reference_param'] = {'progress_list': progress_list, 'prompt': prompt_input}
    if prompt_input in ["", None]:
        # 报错 code 400
        return jsonify("患者病历数据为空。"), 400
    return prompt_input


@cdss_llm_api.route('/llm_prompt_input_by_progress_id', methods=['POST'])
def llm_prompt_input_by_progress_id():
    max_length = int(config_map['llm_model']['input_max_len'])
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    progress_ids = request_data.get('progress_ids', '')
    dialogue_code = request_data.get('dialogue_code', '')
    stream_code = request_data.get('stream_code', '')
    cdss_type = request_data.get('cdss_type', 'mt')

    if dialogue_code not in dialogue_code2chat_map:
        dialogue_code2chat_map[dialogue_code] = {'use_history': 1}
    else:
        dialogue_code2chat_map[dialogue_code]['use_history'] = 1
        dialogue_code2chat_map[dialogue_code]['class_name'] = ''
    progress_id_list = []
    if progress_ids:
        progress_id_list = progress_ids.split(',')

    prompt_input, progress_list = get_input_by_progress_id(cid, progress_id_list, config_map, max_length,cdss_type)
    prompt_input = prompt_input[:max_length]
    if stream_code and stream_code in stream_code_map:
        stream_code_map[stream_code]['reference_param'] = {'progress_list': progress_list, 'prompt': prompt_input}
    return prompt_input


@cdss_llm_api.route('/llm_prompt_offline_answer', methods=['POST'])
def llm_prompt_offline_answer():
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    query = request_data.get('query', '')
    prompt = request_data.get('prompt', '')
    record_date = request_data.get('record_date', '')
    stream_code = request_data.get('stream_code', '')
    record2prompt = get_dialogue_content(rid, config_map)
    if rid in record2prompt:
        prompt_hash = str(hashlib.sha1(normalize_text(prompt).encode('utf-8')).hexdigest())
        if prompt_hash in record2prompt[rid]:
            stream_code, ai_content = record2prompt[rid][prompt_hash]
            return ai_content
    if stream_code and stream_code in stream_code_map:
        chat_param = {"prompt":query,"record_date":record_date, "progress_list":stream_code_map[stream_code].get('reference_param',{}).get('progress_list',[])}
        dialogue_update_param = {'flag': 0, 'chat_param': chat_param, 'record_id': rid, 'stream_code': stream_code,
                                 'content': prompt}
        update_dialogue_content(dialogue_update_param, config_map)
    return ''


@cdss_llm_api.route('/llm_prompt_content_update', methods=['POST'])
def llm_prompt_content_update():
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    query = request_data.get('query', '')
    prompt = request_data.get('prompt', '')
    record_date = request_data.get('record_date', '')
    stream_code = request_data.get('stream_code', '')
    if stream_code and stream_code in stream_code_map:
        chat_param = {"prompt":query,"record_date":record_date, "progress_list":stream_code_map[stream_code].get('reference_param',{}).get('progress_list',[])}
        dialogue_update_param = {'flag': 0, 'chat_param': chat_param, 'record_id': rid, 'stream_code': stream_code,
                                 'content': prompt}
        update_dialogue_content(dialogue_update_param, config_map)
    return ''


@cdss_llm_api.route('/cdss_agent_chat', methods=['POST'])
def cdss_agent_chat():
    rlt = {
        "head": {"error": 0},
        "code": 200,
        "body":{},
        "message": '操作成功',
        "has_error":False
    }
    request_data = json.loads(request.get_data().decode('utf-8'))
    prompt = request_data.get('prompt', '').strip()
    prompt_type = request_data.get('prompt_type', '')
    prompt_type_code = request_data.get('prompt_type_code', '')
    customer_id = request_data.get('customer_id', 1001)
    record_id = request_data.get('record_id', None)
    progress_id_list = request_data.get('progress_id_list', [])
    record_date = request_data.get('record_date', '')
    dialogue_code = request_data.get('dialogue_code', '')
    session_id = request_data.get('session_id', '')
    stream_code = request_data.get('stream_code', '')
    user_code = request_data.get('user_code', '')
    user_name = request_data.get('user_name', '')
    department_code = request_data.get('department_code', '')
    department_name = request_data.get('department_name', '')
    new_chat = request_data.get('new_chat', 0)

    agent_id = int(request_data.get('agent_id', -1))
    agent_name = request_data.get('agent_name', '')
    agent_code = request_data.get('agent_code', '')
    access_flag = request_data.get("access_flag", 'm')
    cdss_type = 'mt'
    if access_flag == 'c':
        cdss_type = 'ot'

    # agent start
    if record_id and agent_code:
        agent_name, agent_code, dify_api, secret_key, dify_id = get_llm_agent_by_code(agent_code, config_map, customer_id)
        if not secret_key:
            rlt['body'] = {"agent_query": '请检查对应的agent是否已上线！', "content": '请检查对应的agent是否已上线！',
                           "dialogue_error_flag": True, "dialogue_end_flag": True}
            return rlt
        use_history = 0
        conversation_id = ''
        parent_message_id = ''
        pre_query = ''
        class_name = ''
        llm_param = {
            "use_history": use_history,
            "new_chat": new_chat,
            "prompt": prompt,
            "agent_id": agent_id,
            "dify_id": dify_id,
            "agent_name": agent_name,
            "agent_code": agent_code,
            "progress_id_list": progress_id_list,
            "dify_api": dify_api,
            "secret_key": secret_key,
            "conversation_id": conversation_id,
            "parent_message_id": parent_message_id,
            "pre_query": pre_query,
            "class_name": class_name,
            "prompt_type": prompt_type,
            "prompt_type_code": prompt_type_code,
            "history_list": [],
            "session_id": session_id,
            "stream_code": stream_code,
            "dialogue_code": dialogue_code,
            "user_code": user_code,
            "user_name": user_name,
            "department_code": department_code,
            "department_name": department_name,
            "customer_id": customer_id,
            "record_id": record_id,
            "record_date": record_date,
            "cdss_type": cdss_type,
            "dify_id": dify_id,
        }
        llm_param = merge_and_deduplicate(llm_param, request_data)
        return call_with_block_dify_agent_sync(llm_param)
    return rlt


def call_with_block_dify_agent_sync(llm_param):
    rlt = {
        "head": {"error": 0},
        "code": 200,
        "body": {},
        "message": '操作成功',
        "has_error": False
    }
    rlt['body'] = {"content": '','agent_query':'', "dialogue_error_flag": False, "dialogue_end_flag": False}
    user_code = llm_param["user_code"]
    stream_code = llm_param["stream_code"]
    dialogue_code = llm_param["dialogue_code"]
    prompt = llm_param["prompt"]
    dify_api = llm_param["dify_api"]
    secret_key = llm_param["secret_key"]
    agent_query = llm_param.get('agent_query', '')

    headers = {
        'Authorization': f'Bearer {secret_key}',
        'Content-Type': 'application/json'
    }
    progress_ids=''
    if llm_param['progress_id_list']:
        progress_ids = ','.join([str(x) for x in llm_param['progress_id_list']])
    input_param = {"use_history": str(llm_param["use_history"]),
                "new_chat": str(llm_param["new_chat"]),
                "prompt": prompt,"customer_id": str(llm_param["customer_id"]),
                "record_id": str(llm_param["record_id"]),
                "record_date": llm_param["record_date"],
                "progress_ids": progress_ids,
                "stream_code": stream_code,
                "dialogue_code": dialogue_code,
                "pre_query": llm_param["pre_query"],
                "class_name": llm_param["class_name"],
                "app_ip_port": '{}:{}'.format(config_map['app']['app_ip'], config_map['app']['app_port'])
                   }
    input_param = merge_and_deduplicate(input_param, llm_param)
    payload = {
        "inputs": input_param,
        "query": ' ' if not prompt else prompt,
        "response_mode": "blocking",
        "conversation_id": '',
        "user": user_code
    }
    print(json.dumps(payload, indent=4, ensure_ascii=False))
    payload_list = [payload]
    if 'llm_sub_param_list' in llm_param:
        payload_list = []
        for k_v in llm_param['llm_sub_param_list']:
            input_param2 = copy.deepcopy(input_param)
            for k in k_v:
                input_param2[k] = k_v[k]
            prompt = input_param2.get('prompt', '')
            payload = {
                "inputs": input_param2,
                "query": ' ' if not prompt else prompt,
                "response_mode": "blocking",
                "conversation_id": '',
                "user": user_code
            }
            payload_list.append(payload)
    # 智能体promethues监控
    g_monitor.set_agent_count(agent_name, dify_id)
    for idx, payload in enumerate(payload_list):
        return_json = post_url(dify_api, payload, headers=headers)
        if 'answer' in return_json:
            output = return_json['answer']
            if agent_query:
                output = output.replace('<agent_query>', '').replace('</agent_query>', '')
                rlt['body']['agent_query'] += output
                if idx < len(payload_list) - 1:
                    rlt['body']['agent_query'] += '\n\n'
            else:
                rlt['body']['content'] += output
                if idx < len(payload_list) - 1:
                    rlt['body']['content'] += '\n\n'
            # if output.find('<agent_query>') > -1:
            #     start = output.find('<agent_query>') + len('<agent_query>')
            #     end = start + output[start:].find('</agent_query>')
            #     agent_query = output[start:end]
            #     rlt['body']['agent_query'] += agent_query
            #     if idx < len(payload_list) - 1:
            #         rlt['body']['agent_query'] += '\n\n'
        else:
            if return_json.get("status", 200) == 400 and return_json.get("message", "") == "Agent Chat App does not support blocking mode":
                g_monitor.set_agent_fail_gauage(agent_name, dify_id, return_json.get("status", 500))
                rlt['body']['agent_query'] = llm_param.get("agent_name", "")
            else:
                message = remove_dify_error_header(return_json.get('message', ''))
                rlt['body']['agent_query'] = message

    rlt['body']['dialogue_end_flag'] = True
    return rlt


@cdss_llm_api.route('/llm_prompt_input_admission', methods=['POST'])
def llm_prompt_input_admission():
    max_length = int(config_map['llm_model']['input_max_len'])
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    dialogue_code = request_data.get('dialogue_code', '')
    stream_code = request_data.get('stream_code', '')
    prompt_input = ''
    progress_list = []
    prompt_input, progress_list = get_cdss_before_admission_input(prompt_input, progress_list, cid, rid, max_length,
                                                                  config_map)
    if stream_code and stream_code in stream_code_map:
        stream_code_map[stream_code]['reference_param'] = {'progress_list': progress_list, 'prompt': prompt_input}
    if prompt_input in ["", None]:
        # 报错 code 400
        return jsonify("患者病历数据为空。"), 400
    return prompt_input


@cdss_llm_api.route('/llm_prompt_input_sssj', methods=['POST'])
def llm_prompt_input_sssj():
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    sssj = get_sssj(cid, rid, config_map)
    return sssj


@cdss_llm_api.route('/llm_prompt_input_by_guid', methods=['POST'])
def llm_prompt_input_by_guid():
    request_data = json.loads(request.get_data().decode('utf-8'))
    cid = int(request_data.get('customer_id', '1001'))
    visit_times = request_data.get('visit_times', None)
    rid = request_data.get('record_id', None)
    if rid:
        rid = int(rid)
    dialogue_code = request_data.get('dialogue_code', '')
    stream_code = request_data.get('stream_code', '')
    cdss_type = request_data.get('cdss_type', '')
    start_date_str = request_data.get('start_date', '')
    end_date_str = request_data.get('end_date', '')
    need_current = request_data.get('need_current', '')
    input_param_list = request_data.get('input_param_list', [])
    prompt_input = ''
    progress_list = []
    rid_list = get_rid_list_by_guid(cid, rid, config_map)
    tt = 0
    for x in rid_list:
        if rid == x['id'] and not need_current:
            continue
        if cdss_type=='mt' and x['record_type'] != 2:
            continue
        if cdss_type=='ot' and x['record_type'] != 1:
            continue
        real_time = datetime.fromisoformat(str(x['real_time']))
        if start_date_str:
            start_date = datetime.fromisoformat(start_date_str)
            if start_date > real_time:
                continue
        if end_date_str:
            end_date = datetime.fromisoformat(end_date_str)
            if end_date < real_time:
                continue
        cdss_type_new = 'mt'
        if x['record_type'] == 1:
            cdss_type_new = 'ot'
        prompt_input, progress_list = get_llm_prompt_input(prompt_input, progress_list, input_param_list, cid, x['id'], cdss_type_new,config_map)
        tt += 1
        if visit_times and tt >= int(visit_times):
            break

    if stream_code and stream_code in stream_code_map:
        stream_code_map[stream_code]['reference_param'] = {'progress_list': progress_list, 'prompt': prompt_input}
    if prompt_input in ["", None]:
        # 报错 code 400
        return jsonify("患者病历数据为空。"), 400
    return prompt_input


# 定义用于清理超时stream_code_map条目的函数
def check_stream_code_timeout():
    """
    定期检查stream_code_map中的条目是否超时，每2秒执行一次
    超时的条目会被清理并更新对话状态
    """
    try:
        current_time = time.time()
        # print(current_time)
        to_remove = []

        for stream_code in stream_code_map:
            # 检查是否有last_active_time字段，如果没有，添加当前时间
            if "last_active_time" not in stream_code_map[stream_code]:
                stream_code_map[stream_code]["last_active_time"] = current_time
                continue

            # 如果已经标记为结束，则不需要处理
            if stream_code_map[stream_code].get("dialogue_end_flag", False):
                continue

            # 检查是否超过30分钟未活动（1800秒）
            time_diff = current_time - stream_code_map[stream_code]["last_active_time"]
            if time_diff > int(config_map['llm_model'].get('stream_overtime', 20)):
                stream_code_map[stream_code]['dialogue_error_flag'] = True
                stream_code_map[stream_code]['dialogue_end_flag'] = True
                stream_code_map[stream_code]['flag'] = 4
                stream_code_map[stream_code]['content'] = stream_code_map[stream_code]['content'] + '\n\n大模型超时！！！'

            if time_diff > 1800:  # 30分钟超时
                to_remove.append(stream_code)
                # 记录日志
                logging.info(f"Stream {stream_code} 已超过30分钟无活动，正在清理")

        # 删除超时的条目
        for stream_code in to_remove:
            if stream_code in stream_code_map:
                stream_code_map.pop(stream_code)
    except Exception as e:
        logging.error(f"检查stream_code_map超时时发生错误: {e}", exc_info=True)

