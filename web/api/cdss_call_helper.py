import asyncio
import json
import logging
import re
import time
import threading
from asyncio.events import Abstract<PERSON><PERSON><PERSON>oop
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from typing import List, <PERSON>ple

import aiohttp

from web.config import config_map
from web.web_utils.redis_util import redis_cache

# 创建全局线程池用于管理任务执行队列
# 从配置文件读取最大工作线程数
_max_workers = int(config_map.get('llm_model', {}).get('dify_max_concurrent_tasks', 50))
_max_queue_size = int(config_map.get('llm_model', {}).get('dify_max_queue_size', 500))
_task_executor = ThreadPoolExecutor(max_workers=_max_workers, thread_name_prefix='cdss-task')
_queue_semaphore = threading.Semaphore(_max_queue_size)

logging.info(f"CDSS任务线程池初始化完成，最大并发任务数: {_max_workers}")

def run_async_task(coroutine, loop: AbstractEventLoop):
    """在事件循环中运行异步任务"""
    return asyncio.run_coroutine_threadsafe(coroutine, loop)

def run_async_task_in_executor(coroutine, loop: AbstractEventLoop):
    """在线程池中运行异步任务，形成执行队列"""
    if not _queue_semaphore.acquire(blocking=False):
        raise Exception("任务队列已满，请稍后重试")
    
    def _run_coroutine():
        try:
            future = asyncio.run_coroutine_threadsafe(coroutine, loop)
            return future.result()
        finally:
            _queue_semaphore.release()
    
    try:
        return _task_executor.submit(_run_coroutine)
    except Exception:
        _queue_semaphore.release()
        raise

# 辅助函数：处理错误响应
async def handle_error_response(response, initial_result, cache_key_dify_map, uuid, api_key):
    """处理HTTP错误响应"""
    try:
        error_json = await response.json()
    except Exception:
        error_json = {"code": "unknown", "message": f"HTTP错误: {response.status}"}
    
    initial_result["code"] = error_json.get("code", "unknown")
    initial_result["error"] = error_json.get("message", "Unknown error")
    initial_result["status"] = "failed"
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    
    logging.error(f"调用dify接口失败, uuid:{uuid}, api_key:{api_key}, 状态码:{response.status}, 错误:{error_json}")

# 辅助函数：处理workflow_started事件
async def handle_workflow_started(record_obj, initial_result, api_key, cache_key_dify_map, uuid):
    """处理workflow_started事件"""
    message_id = record_obj.get("message_id", "")
    logging.info(f"获取到message_id, uuid:{uuid}, api_key:{api_key}, message_id:{message_id}")
    initial_result["message_id"] = message_id
    initial_result["status"] = "running"
    initial_result["conversation_id"] = record_obj.get("conversation_id", "")
    # 更新Redis缓存
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    return True  # 表示已获取message_id

# 辅助函数：处理node_started事件
async def handle_node_started(record_obj, node_data):
    """处理node_started事件"""
    node_id = record_obj.get('data', {}).get('node_id', '')
    title = record_obj.get('data', {}).get('title', '')
    node_type = record_obj.get('data', {}).get('node_type', '')
    
    # 只处理answer类型节点
    if node_type != 'answer':
        return
        
    # 创建节点数据
    node_data[node_id] = {
        "title": title,
        "node_type": node_type,
        "content": "",
        "status": "running",
        "error": ""
    }

# 辅助函数：处理node_finished事件
async def handle_node_finished(record_obj, initial_result, node_data, cache_key_dify_map):
    """处理node_finished事件"""
    node_id = record_obj.get('data', {}).get('node_id', '')
    
    # 检查节点是否存在
    if node_id not in node_data:
        return
        
    # 更新节点数据
    node_data[node_id]['status'] = record_obj.get('data', {}).get('status', '')
    node_data[node_id]['error'] = record_obj.get('data', {}).get('error', '')
    node_data[node_id]['content'] = record_obj.get('data', {}).get('outputs', {}).get('answer', '')
    
    # 更新结果
    initial_result['data'] = node_data
    
    # 更新Redis缓存(每个节点完成时都更新)
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)

# 辅助函数：处理workflow_finished事件
async def handle_workflow_finished(record_obj, initial_result, node_data, api_key, cache_key_dify_map, uuid):
    """处理workflow_finished事件"""
    message_id = record_obj.get("message_id", "")
    status = record_obj.get('data', {}).get('status', '')
    error = record_obj.get('data', {}).get('error', '')
    
    # 更新结果
    initial_result["message_id"] = message_id
    initial_result["status"] = status or "succeeded"  # 默认为成功
    
    # 处理错误情况
    if error:
        initial_result["error"] = error
        initial_result["status"] = "failed"
        
    # 确保数据被包含
    initial_result['data'] = node_data
    
    # 更新Redis缓存
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    
    logging.info(f"工作流完成, uuid:{uuid}, api_key:{api_key}, status:{initial_result['status']}, error:{initial_result['error']}")


async def async_process_cdss_call(ret_data, uuid, api_key, index, cache_key_dify_map):
    """异步处理单个请求
    
    Args:
        ret_data: 请求数据
        uuid: 用户唯一标识
        api_key: API密钥
        index: 索引值
        cache_key_dify_map: 缓存键名
    """
    # 有效的事件类型集合
    valid_event = {"workflow_started", "workflow_finished", "node_started", "node_finished"}
    
    # 创建初始结果
    initial_result = {
        'error': "",
        "message_id": None, 
        "conversation_id": None, 
        "status": "running", 
        "start_time": time.time(), 
        'data': {},
        'api_key': api_key
    }
    node_data = initial_result['data']
    redis_cache.set(cache_key_dify_map, initial_result, expire=86400)  # 1天过期
    try:
        # 设置客户端超时和连接器配置
        timeout = aiohttp.ClientTimeout(total=60*60, connect=30*60)
        # 增加读取缓冲区大小来处理大数据块
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        
        async with aiohttp.ClientSession(
            timeout=timeout, 
            connector=connector,
            read_bufsize=1024*1024*10  # 10MB读取缓冲区
        ) as session:
            url = f'http://{config_map["llm_model"]["dify_ip"]}:{config_map["llm_model"]["dify_port"]}/v1/chat-messages'
            
            logging.info(f"开始调用dify接口, uuid:{uuid}, api_key:{api_key}")
            
            try:
                async with session.post(
                    url=url,
                    json={
                        "inputs": ret_data,
                        "query": "1",
                        "response_mode": "streaming", 
                        "user": f"user-{uuid}"  # 使用uuid作为用户标识，提高可追踪性
                    },
                    headers={"Authorization": f"Bearer {api_key}"}
                ) as response:
                    if response.status != 200:
                        await handle_error_response(response, initial_result, cache_key_dify_map, uuid, api_key)
                        return
                    
                    logging.info(f"dify接口返回200响应, 开始处理流数据, uuid:{uuid}, api_key:{api_key}")
                    message_id_received = False
                    
                    # 处理响应流
                    async for chunk in response.content:
                        if not chunk.startswith(b'data: '):
                            continue
                            
                        chunk = chunk[6:].strip()
                        
                        # 解析JSON
                        try:
                            record_obj = json.loads(chunk)
                        except Exception:
                            continue
                            
                        event = record_obj.get("event", "")
                        if event not in valid_event:
                            continue
                            
                        # 处理workflow_started事件
                        if event == "workflow_started":
                            message_id_received = await handle_workflow_started(
                                record_obj, initial_result, api_key, cache_key_dify_map, uuid
                            )
                            
                        # 处理node_started事件
                        elif event == 'node_started':
                            await handle_node_started(
                                record_obj, node_data
                            )
                            
                        # 处理node_finished事件
                        elif event == 'node_finished':
                            await handle_node_finished(
                                record_obj, initial_result, node_data, cache_key_dify_map
                            )
                            
                        # 处理workflow_finished事件
                        elif event == 'workflow_finished':
                            await handle_workflow_finished(
                                record_obj, initial_result, node_data, api_key, cache_key_dify_map, uuid
                            )
                    
                    # 检查是否获取到message_id
                    if not message_id_received and initial_result.get("message_id") is None:
                        logging.error(f"未获取到message_id, uuid:{uuid}, api_key:{api_key}")
                        initial_result["status"] = "failed"
                        initial_result["error"] = "未能获取到message_id"
                        # 更新Redis缓存
                        redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
            
            except asyncio.TimeoutError:
                logging.error(f"请求超时, uuid:{uuid}, api_key:{api_key}")
                initial_result["status"] = "failed"
                initial_result["error"] = "请求超时"
                redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
                
            except aiohttp.ClientError as e:
                logging.error(f"网络请求错误: {e}, uuid:{uuid}, api_key:{api_key}")
                initial_result["status"] = "failed"
                initial_result["error"] = f"网络请求错误: {str(e)}"
                redis_cache.set(cache_key_dify_map, initial_result, expire=86400)
    
    except Exception as e:
        error_msg = str(e)
        if "Chunk too big" in error_msg:
            logging.error(f"数据块过大错误: {e}, uuid:{uuid}, api_key:{api_key}")
            initial_result["status"] = "failed"
            initial_result["error"] = "返回数据过大，请减少输入内容或联系管理员"
        else:
            logging.error(f"异步处理dify接口失败: {e}, uuid:{uuid}, api_key:{api_key}")
            initial_result["status"] = "failed"
            initial_result["error"] = f"处理异常: {str(e)}"
        redis_cache.set(cache_key_dify_map, initial_result, expire=86400)


def refresh_dify_result(ret_data, uuid, api_key, loop):
    """异步刷新指定API密钥的dify结果"""
    cache_key_dify_map = f"cdss_call:dify_map:{uuid}-{api_key}"
    run_async_task_in_executor(async_process_cdss_call(ret_data, uuid, api_key, 0, cache_key_dify_map), loop)


def should_make_new_request(dify_map):
    """判断是否需要发起新请求"""
    return (
        not dify_map or 
        dify_map.get('status') == 'failed' or (
            dify_map.get('status') == 'running' and time.time() - dify_map.get("start_time", time.time()) > 3600 # 1小时超时
        )
    )


def create_initial_result(api_key, dify_map, agent_info):
    """创建初始状态的结果对象"""
    return {
        "api_key": api_key,
        "status": "running" if not dify_map else dify_map.get("status", "running"),
        "error": "" if not dify_map else dify_map.get("error", ""),
        "message_id": "" if not dify_map else dify_map.get("message_id", ""),
        "conversation_id": "" if not dify_map else dify_map.get("conversation_id", ""),
        "agent_code": agent_info.get('agent_code', ''),
        "warn_type": agent_info.get('warn_type', ''),
        "app_id": agent_info.get('app_id', '')
    }

def _parse_dify_contents(contents: List[str]) -> Tuple[str, str, List[dict]]:
    """解析dify内容, 返回html, think, dify_json"""
    if not contents:
        return None, None, []
    html, think, dify_json = '', '', []
    for content in contents:
        id_, class_, content = _parse_dify_content(content)
        if not class_:
            continue 
        if class_ == 'HTML':
            html = content 
        elif class_ == 'AI':
            think = content 
        else:
            try:
                json_data = _parse_json(content)
                if 'difyJson' in json_data:
                    dify_json = json_data['difyJson'] 
                else:
                    dify_json.append(json_data)
            except Exception as e:
                logging.error(f"解析dify_json失败: {e}, content: {content}")
    return html, think, dify_json


def _parse_json(content: str) -> dict:
    """解析json内容, 返回dict"""
    if not content:
        return {}
    match = re.search(r'```(json|JSON|js|JS)(.*?)```', content, re.DOTALL)
    if not match:
        if '{' in content:
            return json.loads(content)
        return {}
    return json.loads(match.group(2))

def _parse_dify_content(content: str) -> Tuple[str, str, str]:
    """解析dify内容, 返回dify_json, html, think"""
    if not content:
        return None, None, None 
    match = re.search(r'<begin id=([0-9]+) class=(JSON|HTML|AI)>(.*?)<end>', content, re.DOTALL)
    if not match:
        return None, None, None 
    id_ = match.group(1)
    class_ = match.group(2)
    content = match.group(3)
    return id_, class_, content
        
def get_api_key_info(customer_id, results=None):
    """获取API密钥列表及相关信息"""
    api_key_2_other_info = defaultdict(dict)
    try:
        for x in results:
            api_key = x.get('secret_key')
            if not api_key:
                continue
            api_key_2_other_info[api_key] = {
                "warn_type": x.get('warn_type', ''),
                "app_id": x.get("dify_id", ''),
                "agent_code": x.get("code", '')
            }
    except Exception as e:
        logging.error(f"获取API密钥列表出错: {e}")
    return api_key_2_other_info

def process_successful_result(api_key, dify_map, agent_info):
    """处理成功的结果"""
    # 获取基本信息
    message_id = dify_map.get("message_id", "")
    conversation_id = dify_map.get("conversation_id", "")
    status = dify_map.get("status", "")
    key_data = dify_map.get('data', {})
    error = dify_map.get('error', '')
    
    # 处理数据内容
    id_2_contents = defaultdict(list)
    for node_id, node_content in key_data.items():
        content = node_content.get('content', '')
        if '<begin id=' not in content:
            continue
            
        # 提取id
        match = re.search(r'<begin id=([0-9]+)', content)
        if not match:
            continue
            
        id_ = match.group(1)
        id_2_contents[id_].append(content)
    
    # 处理每个内容块
    data = []
    for id_, contents in id_2_contents.items():
        try:
            html, think, dify_json = _parse_dify_contents(contents)
            
            result_item = {
                'conversation_id': conversation_id,
                'message_id': message_id,
                'dify_json': dify_json
            }
            
            # 处理dify_json内容
            if dify_json:
                # 添加唯一标识符
                for i, row in enumerate(dify_json):
                    row['id'] = f"{agent_info.get('app_id', '')}---{id_}---{i}"
                
                result_item['dify_json'] = dify_json
                
                # 缓存HTML内容
                if html:
                    cache_key = f"cdss_call:dify_json:{conversation_id}---{dify_json[0]['id']}---html"
                    redis_cache.set(cache_key, html, expire=86400)
                    result_item['cacheKey'] = cache_key
                if think:
                    cache_key = f"cdss_call:dify_json:{conversation_id}---{dify_json[0]['id']}---think"
                    redis_cache.set(cache_key, think, expire=86400)
                    result_item['cache_key_think'] = cache_key
            
            data.append(result_item)
        except Exception as e:
            logging.error(f"处理内容块出错: {str(e)}, id: {id_}")
    
    # 返回处理结果
    return {
        "api_key": api_key,
        "message_id": message_id,
        "conversation_id": conversation_id,
        "data": data,
        "status": status,
        "agent_code": agent_info.get('agent_code', ''),
        "warn_type": agent_info.get('warn_type', ''),
        "app_id": agent_info.get('app_id', ''),
        "error": error
    }

def process_api_key(ret_data, uuid, api_key, other_info, loop):
    """处理单个API密钥的请求和结果"""
    # 设置API密钥的缓存键
    cache_key_dify_map = f"cdss_call:dify_map:{uuid}-{api_key}"
    
    # 获取缓存中的dify_map
    dify_map = redis_cache.get(cache_key_dify_map)
    
    # 判断是否需要发起新请求
    if should_make_new_request(dify_map):
        try:
            # 发起新请求 - 使用线程池管理执行队列
            run_async_task_in_executor(async_process_cdss_call(ret_data, uuid, api_key, 0, cache_key_dify_map), loop)
        except Exception as e:
            if "队列已满" in str(e):
                logging.warning(f"任务队列已满，uuid:{uuid}, api_key:{api_key}")
                result = create_initial_result(api_key, dify_map, other_info)
                result["status"] = "failed"
                result["error"] = "系统繁忙，请稍后重试"
                return result, False, str(e)
            raise
        
        # 返回初始状态
        error = ''
        if dify_map:
            error = dify_map.get('error', '')
            if not error:
                if dify_map.get('status') == 'running' and time.time() - dify_map.get("start_time", time.time()) > 3600:
                    error = 'dify超时1小时'
        return create_initial_result(api_key, dify_map, other_info), False, error
    
    # 检查message_id
    message_id = dify_map.get("message_id")
    if not message_id:
        return create_initial_result(api_key, dify_map, other_info), False, ''
    
    # 检查状态
    status = dify_map.get("status")
    # 处理成功的结果
    result = process_successful_result(api_key, dify_map, other_info)
    return result, True if status == 'succeeded' else False, dify_map.get('error', '')
