# coding:utf-8
from web.config import *
import json
import time
from loguru import logger
from prometheus_client import Counter
from web.qc.qc_v2 import qc_v2
from web.web_utils.api_util import post_url
from web.prometheus_monitor import g_monitor, QUALITY_CONTROL
from concurrent.futures import ThreadPoolExecutor, as_completed

qc_api = Blueprint('qc_api', __name__, template_folder='templates')
httpCounter = Counter("aigc_http_request", "count http request num", ['url'])
httpErrorCounter = Counter("aigc_http_error", "count http error num", ['url'])
QC_V2 = qc_v2()


def hw_messages(messages=[]) -> list:
    concat_msg = ""
    for message in messages:
        concat_msg = concat_msg + f"<|im_start|>{message['role']}\n{message['content']}<|im_end|>\n"
    concat_msg = concat_msg + "<|im_start|>assistant\n"

    return [{"content": concat_msg}]


@qc_api.route('/hmgpt_qc', methods=['POST', 'GET'])
def hmgpt_qc():
    dt = {"error": 0, "status": 200}
    request_data = json.loads(request.get_data().decode('utf-8'))
    prompt = request_data.get('prompt', '').strip()
    customer_id = request_data.get('customer_id', None)
    record_id = request_data.get('record_id', None)
    message_list = [{"role": "user", "content": prompt}]
    chat_ip = config_map['app']['chat_cpu_ip']
    chat_port = config_map['app']['chat_cpu_port']
    lora_model = 'hmgpt_qc'
    chat_port = '8016'

    openai.api_type = "open_ai"
    openai.api_base = "http://{}:{}/v1".format(chat_ip, chat_port)
    openai.api_key = "none"
    response = openai.ChatCompletion.create(model="hmgpt-cpu", lora_model=lora_model, messages=message_list, top_p=0,
                                            stream=False)
    choices = response.get('choices', [])
    answer = ''
    error_msg = ''
    if len(choices) > 0:
        finish_reason = choices[0].get("finish_reason", '')
        if finish_reason == 'stop':
            answer = choices[0].get("message", {}).get("content", '')
    else:
        error_msg = '请稍后重试！！！'
    dt['response'] = answer
    dt['prompt'] = prompt
    dt['error_msg'] = error_msg
    if error_msg:
        dt['error'] = -1
    return jsonify(dt)


@qc_api.route('/hmgpt_qc_chat', methods=['POST'])
def hmgpt_qc_chat():
    httpCounter.labels(url='/hmgpt_qc_chat').inc()
    # try:
    request_data = json.loads(request.get_data().decode('utf-8'))
    # input string
    input = request_data.get('input', '').strip()
    # history [[Q1, A1], [Q2, A2], ..., [Q_last_turn, A_last_turn]]
    history = request_data.get('history', [])
    chat_model = request_data.get('chat_model', '')
    customer_id = request_data.get('customer_id', None)
    record_id = request_data.get('record_id', None)
    rule_code = request_data.get('rule_code', None)
    is_hospital = QC_V2.config_map['llm_qc_model']['is_run']  # True 生产环境
    def gen_msg(input, history):
        # messages = [{"role": "system", "content": "You are a helpful assistant."}]
        messages = [{"role": "system",
                     "content": "你是惠每云科技开发的临床辅助决策助手，你可以辅助临床医生完成诊疗过程中的相关问题。"}]

        for _history_pair in history:
            usr_msg = _history_pair[0]
            ass_msg = _history_pair[1]
            messages += [
                {
                    "role": "user",
                    "content": usr_msg
                },
                {
                    "role": "assistant",
                    "content": ass_msg
                }
            ]
        messages.append({
            "role": "user",
            "content": input
        })
        return messages

    def qwen_generate():
        messages = gen_msg(input, history)
        responses = Generation.call(
            Generation.Models.qwen_turbo,
            # model='qwen-max-1201',
            messages=messages,
            result_format='message',  # set the result to be "message" format.
            stream=True,
            incremental_output=True  # get streaming output incrementally
        )

        for response in responses:
            if response.status_code == HTTPStatus.OK:
                output = response.output.choices[0]['message']['content']
                yield output

    def hmgpt_predict():
        messages = gen_msg(input, history)
        # 新接口调用
        ip = qc_v2.config_map["llm_qc_model"]["qc_chat_ip"]
        port = qc_v2.config_map["llm_qc_model"]["qc_chat_port"]
        qc_chat_model = qc_v2.config_map["llm_qc_model"]["qc_chat_model"]
        chat_url = 'http://{}:{}/v1/chat/completions'.format(ip, port)

        data = {
            'model': qc_chat_model,
            'messages': messages,
            'tools': [],
            'do_sample': False,
            'temperature': 0,
            'top_p': 0,
            'n': 1,
            'stream': False
        }
        result = post_url(chat_url, data)

        content = result['choices'][0]['message']['content']

        return content

    def huawei_generate_v1():
        chat_ip = config_map['llm_qc_model']['qc_chat_ip']
        chat_port = config_map['llm_qc_model']['qc_chat_port']
        qc_chat_model = config_map["llm_qc_model"]["qc_chat_model"]
        message_list = gen_msg(input, history)

        openai_api_key = "EMPTY"
        openai_api_base = "http://{}:{}/v1".format(chat_ip, chat_port)
        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
        )
        # if chat_model == 'qwen_1_5':
        #     for chunk in client.chat.completions.create(model='/data/share_model/Qwen1.5-14B-Chat',
        #                                                 messages=message_list,
        #                                                 top_p=0.01, stream=True):
        #         if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
        #             output = str(chunk.choices[0].delta.content)
        #             # if output.find('\n') > -1:
        #             #     output = re.sub(r'\n+', '\n\n', output)
        #             yield output
        #
        # print("before",message_list)
        # message_list = hw_messages(message_list)
        # print("after",message_list)
        before_empty = False
        for chunk in client.chat.completions.create(model=qc_chat_model,
                                                    messages=message_list,
                                                    top_p=1,
                                                    max_tokens=2048,
                                                    stream=True,
                                                    stop=['<|im_end|>', '<|im_start|>']):
            if chunk.choices[0].finish_reason is not None:
                break
            content = chunk.choices[0].delta.content
            str_len = len(content)
            if str_len == 0:
                continue

            if before_empty is True:
                before_empty = str_len == 1 and ord(content) == 10
                if before_empty is False:
                    yield content
                    # print(content, end="", flush=True)
                else:
                    break
            else:
                before_empty = str_len == 1 and ord(content) == 10
                yield content
                # print(content, end="", flush=True)

    def huawei_generate_v0():
        chat_ip = config_map['llm_qc_model']['qc_chat_ip']
        chat_port = config_map['llm_qc_model']['qc_chat_port']
        qc_chat_model = config_map["llm_qc_model"]["qc_chat_model"]
        openai.api_type = "open_ai"
        openai.api_base = "http://{}:{}/v1".format(chat_ip, chat_port)
        openai.api_key = "none"
        message_list = gen_msg(input, history)
        # print("before",message_list)
        # message_list = hw_messages(message_list)
        # print("after",message_list)
        before_empty = False
        for chunk in openai.ChatCompletion.create(model=qc_chat_model,
                                                  messages=message_list,
                                                  top_p=1,
                                                  max_tokens=2048,
                                                  stream=True,
                                                  stop=['<|im_end|>', '<|im_start|>']):
            if chunk.choices[0].finish_reason is not None:
                break
            content = chunk.choices[0].delta.content
            str_len = len(content)
            if str_len == 0:
                continue

            if before_empty is True:
                before_empty = str_len == 1 and ord(content) == 10
                if before_empty is False:
                    yield content
                    # print(content, end="", flush=True)
                else:
                    break
            else:
                before_empty = str_len == 1 and ord(content) == 10
                yield content
                # print(content, end="", flush=True)

    def hmgpt_generate():
        # 大模型qps统计
        g_monitor.set_prometheus_qps_count(QUALITY_CONTROL)

        message_list = gen_msg(input, history)
        chat_ip = config_map['llm_qc_model']['qc_chat_ip']
        chat_port = config_map['llm_qc_model']['qc_chat_port']
        qc_chat_model = config_map["llm_qc_model"]["qc_chat_model"]

        openai_api_key = "EMPTY"
        openai_api_base = "http://{}:{}/v1".format(chat_ip, chat_port)
        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
        )
        req_length = 0
        resp_length = 0
        for msg in message_list:
            req_length += len(msg.get("content", 0))

        start_time = time.time()
        try:
            for chunk in client.chat.completions.create(model=qc_chat_model,
                                                        messages=message_list,
                                                        top_p=0.01, stream=True):
                if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
                    output = str(chunk.choices[0].delta.content)
                    resp_length += len(output)
                    # if output.find('\n') > -1:
                    #     output = re.sub(r'\n+', '\n\n', output)
                    yield output
        except Exception as e:
            print("QC model query Error:", e)
            g_monitor.set_prometheus_fail_count(QUALITY_CONTROL)
            
        time_last = time.time() - start_time
        # 统计耗时和输入输出
        g_monitor.set_model_time_historgram(QUALITY_CONTROL, time_last)
        g_monitor.set_model_length_summary(QUALITY_CONTROL, req_length, resp_length)

    headers = {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'X-Accel-Buffering': 'no',
    }
    if is_hospital == '1':
        # 生成环境
        generate = hmgpt_generate()
    else:
        generate = qwen_generate()

    return Response(generate, mimetype="text/event-stream", headers=headers)


import time


@qc_api.route('/hmgpt_qc_v2', methods=['POST', 'GET'])
def hmgpt_qc_v2():
    httpCounter.labels(url='/hmgpt_qc_v2').inc()
    try:
        start_time = time.time()
        dt = QC_V2.generate_qc_answer(request.get_json())
        if config_map['dbg'] == '1':
            logger.info(json.dumps(json.loads(request.get_data()), ensure_ascii=False))
            logger.info(dt)
            execution_time = time.time() - start_time
            print(f"执行时间: {execution_time} 秒")
        return jsonify(dt)
    except Exception as e:
        logger.info("error hmgpt_qc_v2")
        logger.info(json.dumps(json.loads(request.get_data()), ensure_ascii=False))
        logger.error(e)
        httpErrorCounter.labels(url='/hmgpt_qc_v2').inc()


@qc_api.route('/hmgpt_qc_batch', methods=['POST'])
def hmgpt_qc_batch():
    httpCounter.labels(url='/hmgpt_qc_batch').inc()
    #try:
    if 1:
        start_time = time.time()
        request_data = request.get_json()
        if QC_V2.debug_print:
            logger.info("--------------------------------")
            logger.info("request_data")
            logger.info(request_data)
            logger.info("--------------------------------")
        # Extract request parameters
        customer_id = request_data.get('customerId')
        record_ids = request_data.get('recordIds', [])
        rules = request_data.get('rules', [])

        if not record_ids or not rules:
            return jsonify({"error": -1, "message": "recordIds and rules are required"}), 400

        # Process each record
        results = []
        
        def process_rule(record_id, rule):
            rule_code = rule.get('ruleCode')
            metadata = rule.get('metadata', [])
            logic = rule.get('logic', '')
            output_requirement = rule.get('outputRequirement', '')

            # Prepare data for QC processing
            qc_data = {
                "customer_id": customer_id,
                "record_id": record_id,
                "rule_code": rule_code,
                "metadata": metadata,
                "logic": logic,
                "output_requirement": output_requirement
            }
            patient_data = QC_V2.get_patient_data(qc_data)
            if QC_V2.debug_print:
                logger.info("--------------------------------")
                logger.info("patient_data")
                logger.info(patient_data)
                logger.info("--------------------------------")
            result = QC_V2.generate_response(qc_data, patient_data)
            
            return {
                "recordId": record_id,
                "ruleCode": rule_code,
                "result": 2 if result['result'] == '有缺陷' else 1,  # 1 表示成功，2 表示失败
                "content": result['content']
            }
            
        for record_id in record_ids:
            record_results = {"recordId": record_id, "results": []}
            
            # Process rules concurrently for this record
            with ThreadPoolExecutor(max_workers=min(10, len(rules))) as executor:
                futures = [executor.submit(process_rule, record_id, rule) for rule in rules]
                
                for future in as_completed(futures):
                    try:
                        rule_result = future.result()
                        # Add rule result to record results
                        record_results["results"].append({
                            "ruleCode": rule_result["ruleCode"],
                            "result": rule_result["result"],
                            "content": rule_result["content"]
                        })
                    except Exception as e:
                        logger.error(f"Error processing rule: {str(e)}")
                        continue
            
            results.append(record_results)

        if config_map['dbg'] == '1':
            logger.info(f"Request: {json.dumps(request_data, ensure_ascii=False)}")
            logger.info(f"Response: {json.dumps(results, ensure_ascii=False)}")
            execution_time = time.time() - start_time
            logger.info(f"Execution time: {execution_time} seconds")

        return jsonify(results)

    # except Exception as e:
    #     logger.error(f"Error in hmgpt_qc_batch: {str(e)}")
    #     logger.info(f"Request data: {json.dumps(request.get_json(), ensure_ascii=False)}")
    #     httpErrorCounter.labels(url='/hmgpt_qc_batch').inc()
    #     return jsonify({"error": -1, "message": str(e)}), 500


@qc_api.route('/hmgpt_qc_patient_data', methods=['POST'])
def hmgpt_qc_patient_data():
    httpCounter.labels(url='/hmgpt_qc_patient_data').inc()
    request_args = request.get_json()
    data = request_args.copy()
    data['record_id'] = request_args.get('recordId', request_args.get('record_id', 1))
    data['customer_id'] = request_args.get('customerId', request_args.get('customer_id', 1))
    patient_data = QC_V2.get_patient_data(data)
    return jsonify({"patientData": patient_data})


@qc_api.route('/hmgpt_qc_stream', methods=['POST'])
def hmgpt_qc_stream():
    httpCounter.labels(url='/hmgpt_qc_stream').inc()
    #try:
    if 1:
        request_data = json.loads(request.get_data().decode('utf-8'))
        if QC_V2.debug_print:
            logger.info("--------------------------------")
            logger.info("request_data")
            logger.info(request_data)
            logger.info("--------------------------------")
        # Extract request parameters
        customer_id = request_data.get('customerId')
        record_id = request_data.get('recordId')
        rule = request_data.get('rule', {})

        rule_code = rule.get('ruleCode')
        metadata = rule.get('metadata', [])
        logic = rule.get('logic', '')
        output_requirement = rule.get('outputRequirement', '')
        # Prepare data for QC processing
        qc_data = {
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_code": rule_code,
            "metadata": metadata,
            "logic": logic,
            "output_requirement": output_requirement
        }
        patient_data = QC_V2.get_patient_data(qc_data)

        # 调用QC_V2类中的generate_stream方法
        headers = {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'X-Accel-Buffering': 'no',
        }

        return Response(QC_V2.generate_stream(qc_data,patient_data),
                       mimetype="text/event-stream",
                       headers=headers)

    # except Exception as e:
    #     logger.error(f"Error in hmgpt_qc_stream: {str(e)}")
    #     logger.info(f"Request data: {json.dumps(request.get_json(), ensure_ascii=False)}")
    #     httpErrorCounter.labels(url='/hmgpt_qc_stream').inc()
    #     return jsonify({"error": -1, "message": str(e)}), 500

