import json
import logging
from web.config import *
from web.dify.mock_case_service import MockCase
from web.dify.patient_info_service import PatientInfo

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)

dify_api = Blueprint('dify_api', __name__, template_folder='templates')

mock_obj = MockCase()
patient_obj = PatientInfo()


@dify_api.route('/mock_case', methods=['POST'])
def mock_case():
    post_json = request.json
    prompt = post_json.get("prompt")
    code = post_json.get("code")
    host = post_json.get("host")
    customer_id = post_json.get("customer_id", post_json.get("customerId"))
    logging.info(f">>> code:{code}, host:{host}")
    if not all([prompt, code, host]):
        return jsonify({
            "code": 400,
            "msg": "缺少必要参数",
        })
    return mock_obj.run(prompt, code, host, customer_id)


@dify_api.route('/get_customer_order_worlds', methods=['POST'])
def get_customer_order_worlds():
    post_json = request.json
    customer_id = post_json.get("customer_id")
    order_worlds= post_json.get("order_worlds")
    order_type = post_json.get("type")
    return jsonify(mock_obj.get_customer_order_worlds(order_worlds, customer_id, order_type))


@dify_api.route('/get_patient_test', methods=['POST'])
def get_patient_test():
    post_json = request.json
    uuid = post_json.get("uuid", None)
    if uuid is None:
        if post_json.get("customer_id", None) is None or post_json.get("record_id", None) is None:
            return jsonify({
                "code": 400,
                "msg": "缺少必要参数",
            })
        uuid = str(post_json["customer_id"]) + "-" + str(post_json["record_id"])
    return patient_obj.run(uuid, "test")
