# coding:utf-8
import asyncio
import copy
import json
import logging
import re
import threading
import time
from web.config import *
from collections import defaultdict
from typing import List, Tu<PERSON>, Generator, Any, Optional
from flask import Flask, Response, stream_with_context, Blueprint, request
import aiohttp

enum_info_type = {
    "content": 1,
    "think": 2}

class AsyncStreamResponse(Response):
    default_mimetype = "text/event-stream"

logging = logging.getLogger(__name__)
# 不再全局创建事件循环，而是在每个线程中按需创建
# main_loop = asyncio.new_event_loop()
# asyncio.set_event_loop(main_loop)

openai_api_base = config_map['llm_model']['api_base']
api_key = config_map['llm_model']['api_key']
dify_base_url = f'http://{config_map["llm_model"]["dify_ip"]}:{config_map["llm_model"]["dify_port"]}/v1/'

dify_llm_api = Blueprint('dify_llm_api', __name__, template_folder='templates')

def empty_process(respChunk: bytes, thinkFlag: bool) -> bytes:
    """原样返回数据块"""
    return respChunk

def remove_content_chunk(chunk: bytes, thinkFlag: bool) -> Optional[bytes]:
    """如果数据块是content类型，则返回None，否则返回原数据块"""
    try:
        data = json.loads(chunk[6:-1].decode('utf-8'))
        if data.get("data", {}).get("info_type", "") == "think": 
            return chunk
        elif thinkFlag:
            return chunk
        else:
            return None
    except:
        return chunk

def remove_think_chunk(chunk: bytes, thinkFlag: bool) -> Optional[bytes]:
    """如果数据块是think类型，则返回None，否则返回原数据块"""
    try:
        data = json.loads(chunk[6:-1].decode('utf-8'))
        if data.get("data", {}).get("info_type", "") == "think":
            return None 
        elif thinkFlag:
            return None
        else:
            return chunk
    except:
        return chunk

def ensure_bytes(data) -> Optional[bytes]:
    """确保数据是bytes类型"""
    if data is None:
        return None
    if isinstance(data, bytes):
        return data
    if isinstance(data, str):
        return data.encode('utf-8')
    if isinstance(data, (dict, list)):
        return json.dumps(data).encode('utf-8')
    # 转换其他类型为字符串后再转为bytes
    return str(data).encode('utf-8')

def get_or_create_event_loop():
    """获取当前线程的事件循环，如果不存在则创建一个新的"""
    try:
        return asyncio.get_event_loop()
    except RuntimeError:
        # 在当前线程中创建一个新的事件循环
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        return loop

def run_async(coro):
    """在当前线程中运行异步协程，返回结果"""
    loop = get_or_create_event_loop()
    return loop.run_until_complete(coro)

def async_generator_to_sync(async_gen) -> Generator[Any, None, None]:
    """将异步生成器转换为同步生成器"""
    # 在当前线程中获取或创建事件循环
    loop = get_or_create_event_loop()
    
    async def consume_async_generator():
        try:
            async for chunk in async_gen:
                yield chunk
        except Exception as e:
            logging.error(f"异步生成器错误: {e}")
            yield f"Generator error: {str(e)}".encode('utf-8')
    
    agen = consume_async_generator()
    ait = agen.__aiter__()
    
    try:
        while True:
            try:
                # 使用当前线程的事件循环运行异步代码
                obj = run_async(ait.__anext__())
                # 确保我们只生成bytes类型的数据
                if obj is not None:
                    yield ensure_bytes(obj)
            except StopAsyncIteration:
                break
            except Exception as e:
                logging.error(f"生成器错误: {e}")
                yield ensure_bytes(f"Error: {str(e)}")
                break
    except RuntimeError as e:
        logging.error(f"运行时错误: {e}")
        # 如果发生运行时错误，尝试重新创建事件循环
        if "event loop" in str(e).lower():
            logging.info("尝试重启生成器...")
            yield ensure_bytes("Restarting generator...")
            # 重启生成器
            yield from async_generator_to_sync(async_gen)
        else:
            # 对于其他运行时错误，返回错误消息
            yield ensure_bytes(f"Runtime error: {str(e)}")

@dify_llm_api.route('/workflow_run', methods=['POST'])
def workflow_run():
    request_data = json.loads(request.get_data().decode('utf-8'))
    auth_key = request.headers.get("Authorization", "")
    info_type = request_data.get("info_type", 11)
    
    streaming_processing_func_list = []
    # 修正位运算逻辑
    if (info_type & enum_info_type["content"]) == 0:
        streaming_processing_func_list = [remove_content_chunk]
    if (info_type & enum_info_type["think"]) == 0:
        streaming_processing_func_list.append(remove_think_chunk)
    if len(streaming_processing_func_list) == 0:
        streaming_processing_func_list.append(empty_process)
    
    # 修正处理函数，确保返回bytes类型
    def combined_processor(chunk, thinkFlag):
        result = chunk
        for func in streaming_processing_func_list:
            if result is None:
                break
            result = func(result, thinkFlag)
        return ensure_bytes(result)
    
    # 创建一个同步生成器函数包装异步生成器
    def generate():
        try:
            # 使用async_gen_wrapper包装异步生成器，使其在每次需要时重新创建事件循环
            async_gen = dify_workflow_run(request_data, auth_key, combined_processor)
            for chunk in async_generator_to_sync(async_gen):
                if chunk is not None:  # 确保不会产生None
                    yield chunk
        except Exception as e:
            logging.error(f"生成过程错误: {e}")
            yield ensure_bytes(f"Generation error: {str(e)}")
    
    # 使用 stream_with_context 确保请求上下文在生成器运行时保持活动
    return Response(
        stream_with_context(generate()),
        mimetype="text/event-stream"
    )

async def dify_workflow_run(request_data, auth_key, streaming_processing_func=empty_process):
    """异步函数，返回一个异步生成器"""
    dify_url = dify_base_url + "workflows/run"
    header = {"Authorization": auth_key, "Content-Type": "application/json"}
    thinkFlag = False
    if int(config_map['llm_model'].get('pre_think',0)) > 0:
        thinkFlag=True
    
    try:
        timeout = aiohttp.ClientTimeout(total=60)  # 设置超时时间为60秒
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.post(dify_url, headers=header, json=request_data) as resp:
                    # 确保响应是流式的
                    if resp.status == 200:
                        async for chunk in resp.content:
                            if not chunk:  # 跳过空数据块
                                continue
                                
                            try:
                                data = json.loads(chunk[6:-1].decode('utf-8'))
                            except:
                                yield chunk
                            if data.get("data", {}).get("text", "") == "<think>":
                                thinkFlag = True
                            elif thinkFlag and data.get("data", {}).get("text", "") == "</think>": 
                                thinkFlag = False
                                continue

                            # 处理数据块
                            processed_chunk = streaming_processing_func(chunk, thinkFlag)
                            if processed_chunk:  # 确保处理后的数据不为None
                                yield processed_chunk
                    else:
                        error_text = await resp.text()
                        error_message = f"Error: {resp.status}, {error_text}".encode('utf-8')
                        yield streaming_processing_func(error_message, thinkFlag)
            except Exception as e:
                logging.error(f"请求异常: {e}")
                error_message = f"Exception during request: {str(e)}".encode('utf-8')
                yield streaming_processing_func(error_message, thinkFlag)
    except Exception as e:
        logging.error(f"会话创建异常: {e}")
        error_message = f"Session creation error: {str(e)}".encode('utf-8')
        yield streaming_processing_func(error_message, thinkFlag)



