# coding:utf-8
from web.config import *
import json
from web.chat.chat import get_chat_response, get_vl_chat_response
from web.chat.ocr import image_ocr
from web.chat.chat_history import get_chat_history
from web.chat.med_import import import_pg, import_pg_status, refresh_config, pdf_ocr_impl


llm_api = Blueprint('llm_api', __name__, template_folder='templates')

import re

reg_pattern_disease = re.compile(r'{}'.format("(预测|生成|给出)[^。;]*(初步诊断|诊断依据|鉴别诊断)"))
reg_pattern_treatment = re.compile(r'{}'.format("(预测|生成|给出)[^。;]*诊疗计划"))
reg_pattern_easy_inquiry = re.compile(r'{}'.format("轻问诊[:：]?"))
reg_pattern_hm = re.compile(r'{}'.format(
    "你[\u4e00-\u9fa5]{0,15}(身份|名字|开发者|开发背景|谁的作品|来自哪里|助手|开发的|打造的|诞生地|创作者|自己|哪些问题|生成的|清华大学|出生|开发设计)|谁创造了你|惠每|你好"))
reg_pattern_drug_rationality = re.compile(r'{}'.format("用药合理性[:：]?"))
reg_pattern_medical_qc = re.compile(r'{}'.format("以下病历是否有缺陷[:：]?"))
reg_pattern_hmgpt_vte = re.compile(r'{}'.format("基于VTE知识库"))
reg_pattern_qc_v2 = re.compile(r'{}'.format("请..缺陷规则"))
reg_pattern_qc_v3 = re.compile(r'{}'.format("【QC_V2】"))
reg_pattern_tc = re.compile(r'{}'.format("请依据以下病历内容.*请回答“是”或“否”"))


@llm_api.route('/ocr', methods=['POST', 'GET'])
def ocr():
    dt = {"error": 0, "status": 200}
    image_url, response, error_msg = image_ocr(request.get_data())
    dt['response'] = response
    dt['image_url'] = image_url
    dt['error_msg'] = error_msg
    if error_msg:
        dt['error'] = -1
    return jsonify(dt)


@llm_api.route('/ocr2', methods=['POST', 'GET'])
def ocr2():
    dt = {"error": 0, "status": 200}
    request_data = json.loads(request.get_data().decode('utf-8'))
    if request_data.get("image_url", None) is not None and request_data["image_url"] != "":
        image_url, response, error_msg = image_ocr(request.get_data())
        dt['list'] = [response] if error_msg == "" else []
        dt["error"] = 0 if error_msg == "" else 1
        dt["msg"] = error_msg
        dt["all"] = 1
    else:
        page_num, response, err, err_cnt = pdf_ocr_impl(request_data)
        dt['list'] = response
        dt["error"] = err_cnt
        dt["msg"] = err
        dt["all"] = page_num
    return jsonify(dt)


@llm_api.route('/pg_import', methods=['POST'])
def pg_import():
    data = request.json
    err = import_pg(data)
    return jsonify({"error": 0 if err is None else 1, "status": 200, "msg": err})


@llm_api.route('/pg_import_status', methods=['POST'])
def pg_import_status():
    data = request.json
    res = import_pg_status(data)
    return jsonify(res)


@llm_api.route('/pg_import_refresh', methods=['GET'])
def pg_import_refresh():
    err = refresh_config()
    return jsonify({"error": 0 if err is None else 1, "status": 200, "msg": err})


@llm_api.route('/chat', methods=['POST', 'GET'])
def chat():
    dt = {"error": 0, "status": 200}
    prompt, response, error_msg = get_chat_response(request.get_data(), config_map)
    dt['response'] = response
    dt['prompt'] = prompt
    dt['error_msg'] = error_msg
    if error_msg:
        dt['error'] = -1
    return jsonify(dt)


@llm_api.route('/vl_chat', methods=['POST', 'GET'])
def vl_chat():
    dt = {"error": 0, "status": 200}
    image_url, prompt, response, error_msg = get_vl_chat_response(request.get_data(), config_map)
    dt['response'] = response
    dt['image_url'] = image_url
    dt['prompt'] = prompt
    dt['error_msg'] = error_msg
    if error_msg:
        dt['error'] = -1
    return jsonify(dt)

@llm_api.route('/stream_chat', methods=['POST'])
def stream_chat():
    request_data = json.loads(request.get_data().decode('utf-8'))
    prompt = request_data.get('prompt', '').strip()
    dialogue_code = request_data.get('dialogue_code', None)
    chat_model = request_data.get('chat_model', '')
    chat_type = request_data.get('chat_type', '')
    customer_id = request_data.get('customer_id', None)
    history_list = []
    if dialogue_code:
        history_list = get_chat_history(customer_id, chat_model, dialogue_code, config_map)

    def chatgpt_generate():
        message_list = []
        for history in history_list:
            message_list.append(history)
        message_list.append({"role": "user", "content": prompt})
        openai_api_key = "********************************"
        openai_api_base = "https://hmopenairesource.openai.azure.com"
        client = AzureOpenAI(
            api_key=openai_api_key,
            api_version="2023-03-15-preview",
            azure_endpoint=openai_api_base,
        )
        for chunk in client.chat.completions.create(model="test_gpt_16k", messages=message_list, top_p=0, timeout=30, stream=True):
            if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
                yield chunk.choices[0].delta.content

    def call_with_stream_qwen():
        message_list = [{"role": "system", "content": "你是惠每云科技开发的临床辅助决策助手，你可以辅助临床医生完成诊疗过程中的相关问题。"}]
        for history in history_list:
            message_list.append(history)
        message_list.append({"role": "user", "content": prompt})
        for response in Generation.call(
                Generation.Models.qwen_max,
                # model='qwen-max-longcontext',
                messages=message_list,
                result_format='message',  # set the result to be "message" format.
                top_k=1,
                stream=True,
                incremental_output=True  # get streaming output incrementally
        ):
            if response.status_code == HTTPStatus.OK:
                output = str(response.output.choices[0]['message']['content'])
                if len(output) > 0:
                    yield output

    def hmgpt_generate():
        message_list = []
        lora_model = 'default'
        new_prompt = prompt
        if re.search(reg_pattern_qc_v3, prompt):
            lora_model = 'hmgpt_qc_v2'
            new_prompt = prompt.replace('【QC_V2】', '')
        elif re.search(reg_pattern_treatment, prompt):
            lora_model = 'treatment_model'
        elif re.search(reg_pattern_disease, prompt):
            lora_model = 'disease_model'
        elif re.search(reg_pattern_easy_inquiry, prompt) or re.search(reg_pattern_hm, prompt):
            lora_model = 'multi_lora_model'
        elif re.search(reg_pattern_drug_rationality, prompt):
            lora_model = 'drug_rationality_model'
        elif re.search(reg_pattern_medical_qc, prompt):
            lora_model = 'medical_qc_model'
        elif re.search(reg_pattern_hmgpt_vte, prompt):
            lora_model = 'hmgpt_vte_model'
        elif re.search(reg_pattern_qc_v2, prompt):
            lora_model = 'hmgpt_qc'
        elif re.search(reg_pattern_tc, prompt):
            lora_model = 'hmgpt_tc'
        if dialogue_code and lora_model == 'default':
            message_list = history_list
        if lora_model in ['multi_lora_model', 'drug_rationality_model']:
            new_prompt = re.sub(r'(轻问诊|用药合理性)[:：]?', '', prompt)
        message_list.append({"role": "user", "content": new_prompt})
        chat_ip = config_map['app']['chat_cpu_ip']
        chat_port = config_map['app']['chat_cpu_port']
        if lora_model in ['hmgpt_qc', 'hmgpt_tc', 'hmgpt_qc_v2', 'default']:
            chat_port = '8016'
        if chat_model.find('gpu') > -1:
            chat_ip = config_map['app']['chat_gpu_ip']
            chat_port = config_map['app']['chat_gpu_port']
        elif chat_model.find('baichuan') > -1:
            chat_ip = config_map['app']['chat_baichuan_ip']
            chat_port = config_map['app']['chat_baichuan_port']
        elif chat_model in ['qwen_test', 'qwen_pre', 'qwen_1_5']:
            chat_ip = config_map['app']['{}_ip'.format(chat_model)]
            chat_port = config_map['app']['{}_port'.format(chat_model)]
        openai_api_key = "EMPTY"
        openai_api_base = "http://{}:{}/v1".format(chat_ip, chat_port)
        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
        )
        if chat_model == 'qwen_1_5':
            for chunk in client.chat.completions.create(model='/data/share_model/Qwen1.5-14B-Chat', messages=message_list,
                                                      top_p=0.01, stream=True):
                if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
                    output = str(chunk.choices[0].delta.content)
                    # if output.find('\n') > -1:
                    #     output = re.sub(r'\n+', '\n\n', output)
                    yield output
        else:
            for chunk in client.chat.completions.create(model=chat_model, messages=message_list,
                                                      top_p=0, stream=True):
                if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
                    output = str(chunk.choices[0].delta.content)
                    # if output.find('\n') > -1:
                    #     output = re.sub(r'\n+', '\n\n', output)
                    yield output

    def self_generate():
        if chat_type == 'ipc':
            output = '您好，我是Dr.Mayson，请问您哪里不舒服？'
            yield output

    headers = {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'X-Accel-Buffering': 'no',
    }
    if len(history_list) == 0 and chat_type in ['ipc']:
        return Response(self_generate(), mimetype="text/event-stream", headers=headers)
    if chat_model == 'chatgpt':
        return Response(chatgpt_generate(), mimetype="text/event-stream", headers=headers)
    elif chat_model == 'qwen_api':
        return Response(call_with_stream_qwen(), mimetype="text/event-stream", headers=headers)
    elif chat_model in ['hmgpt_gpu', 'hmgpt_cpu', 'baichuan2_7b', 'qwen_test', 'qwen_pre', 'qwen_1_5']:
        return Response(hmgpt_generate(), mimetype="text/event-stream", headers=headers)
    # else:
    #     return Response('chat_model错误', mimetype="text/event-stream", headers=headers)

