import logging

from web.config import *
from web.web_utils.db_util import select_by_sql

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)


class PatientInfo(object):
    def __init__(self):
        pass

    def get_test(self, uuid):
        def _select_test(uuid_list):
            idx_tb = None
            results = {}
            if len(uuid_list) == 1:
                idx_tb = int(uuid_list[0][-1:])
            for ti in range(10):
                if idx_tb is not None and idx_tb != ti:
                    continue
                tmp_uuid_list = []
                for tmp_uuid in uuid_list:
                    if tmp_uuid.endswith(str(ti)) is False:
                        continue
                    tmp_uuid_list.append(tmp_uuid)

                sql = f'''
                            select mpt.id as id, mpt.test_name as test_name, mpti.test_item as test_item, mpti.test_result as test_result, mpti.test_value_unit as test_value_unit, mpt.uuid as uuid, DATE_FORMAT(mpt.report_time, '%Y-%m-%d %H:%i') as create_date from hmcdss2.mt_patient_test_item_{ti} mpti
                            left join hmcdss2.mt_patient_test mpt
                            on mpt.id = mpti.test_id
                            where mpt.uuid in ('{"','".join(tmp_uuid_list)}')
                        '''

                db_results = select_by_sql(sql, 'hmcdss2', config_map)
                test_result_map = {}
                for db_result in db_results:
                    test_id = db_result["id"]
                    test_name = db_result["test_name"]
                    test_item = db_result["test_item"]
                    test_result = db_result["test_result"]
                    test_value_unit = db_result["test_value_unit"]
                    uuid = db_result["uuid"]
                    create_date = db_result["create_date"]

                    if uuid not in test_result_map:
                        test_result_map[uuid] = {}

                    if test_id not in test_result_map[uuid]:
                        test_result_map[uuid][test_id] = []

                    test_result_map[uuid][test_id].append({
                        "test_name": test_name,
                        "item_name": test_item,
                        "test_result": test_result,
                        "test_value_unit": test_value_unit,
                        "create_date": create_date
                    })
                    if uuid not in results:
                        results[uuid] = {}
                    if results[uuid] is not None:
                        results[uuid]["4001"] = test_result_map[uuid]
            return results

        tmp_record_id_to_info_map = _select_test([uuid])
        res = tmp_record_id_to_info_map.get(uuid, {}).get("4001", [])
        all_item_list = []
        for test in res:
            all_item_list = all_item_list + res[test]
        all_item_list = sorted(all_item_list, key=lambda x: x["create_date"])
        all_item_map = {}
        for record in all_item_list:
            item_name = record["item_name"]
            if item_name not in all_item_map:
                all_item_map[item_name] = []
            all_item_map[item_name].append({
                "test_result": record["test_result"],
                "unit": record["test_value_unit"],
                "create_date": record["create_date"],
            })
        return all_item_map

    def run(self, uuid, task):
        if task == "test":
            return self.get_test(uuid)
        return {"code": 500, "msg": "任务信息异常"}
