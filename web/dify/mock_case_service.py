import logging

from web.config import *
import threading
from concurrent.futures import ThreadPoolExecutor
from queue import Queue, Full
import time
from web.web_utils.db_util import select_by_sql
from web.dify.helper import _post, load_api_config

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
class MockCase(object):
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=5)
        self.active_codes = set()
        self.active_codes_lock = threading.Lock()
        self.concurrent_tasks = Queue(maxsize=5)
        self.api_info_file = "MockCase"

    def get_customer_order_worlds(self, order_worlds, customer_id, order_type):
        res = {"result": []}
        for query_world in order_worlds.keys():
            common_id = order_worlds[query_world]
            if order_type == "drug":
                sql = f'''
select common_id as commonId, dc.common_name as commonName, cust.id as customerId,cust.org_common_name as customerName, cust.org_drug_id as orgId
from dt_drug_customer cust
inner join dt_drug_common dc on dc.id = cust.common_id
where dc.status=1 and cust.status = 1 and cust.flag=1
and dc.id = {common_id}
and cust.customer_id = {customer_id}
            '''
            elif order_type == "exam":
                sql = f'''
select ec.org_exam_id as orgId, ec.org_exam_name as customerName
from mt_exam_customer_relation ecr
left join mt_exam_customer ec on ec.id = ecr.org_id and ecr.exam_id = {common_id}
where ec.`status` = 1 and ec.flag = 1
and ec.customer_id = {customer_id}
'''
            elif order_type == "nurse":
                sql = f'''
select nc.org_nurse_name as customerName, nc.org_nurse_id as orgId
from mt_nurse_customer_relation ncr
left join mt_nurse_customer nc on nc.id = ncr.org_nurse_id and ncr.nurse_id = {common_id}
where nc.status= 1 and ncr.status=1 and nc.flag=1 and nc.customer_id = {customer_id}
'''
            elif order_type == "operation":
                sql = f'''
select oc.org_operation_name as customerName, oc.org_operation_id as orgId
from  mt_operation_customer_relation ocr
left join mt_operation_customer oc on oc.id = ocr.org_id and ocr.operation_id = {common_id}
where oc.`status` = 1 and oc.flag=1 and oc.customer_id = {customer_id}
'''
                


            db_results = select_by_sql(sql, 'hmcdss2', config_map)
            if len(db_results) == 0:
                return {}
            for db_result in db_results:
                res["result"].append({"query_order_world": query_world, "customer_order_world": db_result["customerName"], "customer_order_id": db_result["orgId"]})
        
        
        
        return res

    def process_request(self, prompt, code, host, task_id, customer_id):
        """异步处理单个请求"""
        try:
            # 将任务加入并发控制队列
            self.concurrent_tasks.put(task_id)
            api_config = load_api_config(self.api_info_file)
            # 调用大模型
            record_obj = _post(
                url=f'http://{config_map["llm_model"]["dify_ip"]}:{config_map["llm_model"]["dify_port"]}/v1/workflows/run',
                data={
                    "inputs": {"prompt": prompt, "code": code, "customer_id": str(customer_id), "db_url": "undefined"},
                    "response_mode": "blocking",
                    "user": "abc-123"
                },
                api_key=api_config["api_key"])
            # 处理结果
            obj_json = {
                "code": code,
                "status": "success",
                "msg": "",
                "recordList": record_obj["data"]["outputs"]["result"]
            }
            # logging.info(obj_json)
            # 保存记录
            host = host.replace("***********", "**********")
            _post(url=f"{host}/mdPatient/modelRecord", data=obj_json, timeout=10)

            return True

        except Exception as e:
            logging.info(f"处理请求时发生错误: {str(e)}")
            obj_json = {
                "code": code,
                "status": "fail",
                "msg": "处理请求时发生错误",
                "recordList": []
            }
            host = host.replace("***********", "**********")
            _post(url=f"{host}/mdPatient/modelRecord", data=obj_json, timeout=10)
            return False
        finally:
            # 清理资源
            try:
                self.concurrent_tasks.get_nowait()  # 从并发控制队列中移除
            except:
                pass

            with self.active_codes_lock:
                self.active_codes.remove(code)  # 从活动code集合中移除

    def run(self, prompt, code, host, customer_id):
        # 检查是否有相同code的任务正在处理
        with self.active_codes_lock:
            if code in self.active_codes:
                return jsonify({
                    "code": 409,
                    "msg": "相同的任务正在处理中",
                })
            self.active_codes.add(code)

        # 检查当前并发任务数量
        try:
            # 尝试立即将任务加入队列，如果队列满则抛出异常
            self.concurrent_tasks.put_nowait(None)
            self.concurrent_tasks.get_nowait()  # 立即移除，实际的限制会在process_request中进行
        except Full:
            with self.active_codes_lock:
                self.active_codes.remove(code)
            return jsonify({
                "code": 429,
                "msg": "系统正忙，请稍后重试",
            })

        # 创建任务ID
        task_id = f"{code}_{int(time.time())}"

        # 提交异步任务
        self.executor.submit(self.process_request, prompt, code, host, task_id, customer_id)

        return jsonify({
            "code": 200,
            "msg": "success",
            "task_id": task_id
        })
