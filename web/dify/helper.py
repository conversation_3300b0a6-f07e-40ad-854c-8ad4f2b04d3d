import logging
import os
import time

import yaml
import requests

def remove_dify_error_header(raw_res):
    # 产品要求移除Run failed:前缀
    if raw_res.startswith('Run failed:'):
        raw_res = raw_res.replace('Run failed:', '').strip()
    if raw_res.startswith('Failed to invoke tool:'):
        raw_res = raw_res.replace('Failed to invoke tool:', '').strip()
    if len(raw_res) > 1 and raw_res.startswith('"') and raw_res.endswith('"'):
        raw_res = raw_res[1:-1].strip()
    return raw_res

def _post(url, data, api_key=None, max_retries=3, timeout=None):
    """
    发送POST请求，带重试机制和API密钥支持
    :param url: 请求URL
    :param data: 请求数据
    :param api_key: API密钥
    :param max_retries: 重试次数
    """
    headers = {}
    if api_key:
        headers['Authorization'] = f'Bearer {api_key}'

    for attempt in range(max_retries):
        try:
            response = requests.post(url, json=data, headers=headers, timeout=timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            logging.info(f"POST请求失败 {url}: {str(e)}")
            time.sleep(2)  # 失败后等待2秒再重试

def _get(url, api_key=None, max_retries=3, timeout=None):
    """
    发送GET请求，带重试机制和API密钥支持
    :param url: 请求URL
    :param api_key: API密钥
    :param max_retries: 重试次数
    :param timeout: 超时时间
    :return: 响应数据
    """
    headers = {}
    if api_key:
        headers['Authorization'] = f'Bearer {api_key}'

    for attempt in range(max_retries):
        try:
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            logging.info(f"GET请求失败 {url}: {str(e)}")
            time.sleep(2)  # 失败后等待2秒再重试


def load_api_config(api_info_file):
    base_dir = os.path.dirname(os.path.abspath(__file__))
    file_path = os.path.join(base_dir, 'api_info', f'{api_info_file}.yaml')
    with open(file_path, 'r', encoding='utf-8') as file:
        config = yaml.safe_load(file)
        return config
