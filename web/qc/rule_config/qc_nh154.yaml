FEW_SHOT: |
    {规则输入内容}
    【指令】
    请基于以上病历内容，逐步回答下面的问题并总结。
    问题1：上述病历内容中是否记录了患者病情的相关描述？（有症状、查体、辅助检查三者中的任一内容即可）
    回答1：（请回答“是”或“否”）

    问题2：上述病历内容中是否记录了诊断内容？（包括目前诊断)
    回答2：（请回答“是”或“否”）

    问题3：上述病历内容中是否记录了鉴别诊断相关描述？（包括“鉴别诊断”、“诊断明确无需鉴别”或其他疾病相鉴别的描述，有任一描述即可）
    回答3：（请回答“是”或“否”）

    问题4：上述病历内容中是否记录了后续诊疗计划的相关描述(包括“治疗上”、或后续检查检验、或拟行手术、或“转住院”、或今后诊疗等，有任一相关描述即可）？
    回答4：（请回答“是”或“否”）

    总结上述四个问题的回答：如果全部为“是”则输出“无遗漏内容”；如果有任意一个回答为“否”，则输出“有遗漏内容”。
    总结：
QUESTION: |
    问题:
    {}
    请基于以上病历内容，逐步回答下面的问题并总结。
    问题1：上述病历内容中是否记录了患者病情的相关描述？（有症状、查体、辅助检查三者中的任一内容即可）
    回答1：（请回答“是”或“否”）

    问题2：上述病历内容中是否记录了诊断内容？（包括目前诊断)
    回答2：（请回答“是”或“否”）

    问题3：上述病历内容中是否记录了鉴别诊断相关描述？（包括“鉴别诊断”、“诊断明确无需鉴别”或其他疾病相鉴别的描述，有任一描述即可）
    回答3：（请回答“是”或“否”）

    问题4：上述病历内容中是否记录了后续诊疗计划的相关描述(包括“治疗上”、或后续检查检验、或拟行手术、或“转住院”、或今后诊疗等，有任一相关描述即可）？
    回答4：（请回答“是”或“否”）

    总结上述四个问题的回答：如果全部为“是”则输出“无遗漏内容”；如果有任意一个回答为“否”，则输出“有遗漏内容”。
    总结：