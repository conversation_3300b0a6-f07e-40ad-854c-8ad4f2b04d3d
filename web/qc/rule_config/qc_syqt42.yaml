FEW_SHOT: |
    ##################################################
    [病历内容-1]
    项目代码名称:腹水
    检查结论:阑尾炎术后
    病案诊断:急性阑尾炎伴局限性腹膜炎,急性化脓性阑尾炎伴腹膜炎,肠粘连,阑尾炎
    仔细阅读上面[病历内容]，执行下面指令
    指令1：抽取检查结论中的症状
    指令2：抽取检查结论中的诊断
    指令3：如果检查结论中有诊断，则判断和病案诊断是否相关（相关/不相关）
    指令4：如果检查结论中有诊断，但和病案诊断不相关，则输出“有缺陷”。其他情况输出“无缺陷”
    
    回答：
    指令1:
    抽取检查结论中的症状:
    无
    
    指令2:
    抽取检查结论中的诊断:
    阑尾炎术后
    
    指令3:
    如果检查结论中有诊断，判断和病案诊断是否相关:
    相关
    
    指令4：
    如果检查结论中有诊断，但和病案诊断不相关，则输出“有缺陷”。其他情况输出“无缺陷”：
    由于检查结论中的诊断与病案诊断相关,因此输出"无缺陷"。
    
    [病历内容-2]
    项目代码名称:体表包块（见检查备注）
    检查结论:腹腔扫查未见明显异常包块
    病案诊断:急性阑尾炎伴局限性腹膜炎,急性化脓性阑尾炎伴腹膜炎,肠粘连,阑尾炎
    仔细阅读上面[病历内容]，执行下面指令
    指令1：抽取检查结论中的症状
    指令2：抽取检查结论中的诊断
    指令3：如果检查结论中有诊断，则判断和病案诊断是否相关（相关/不相关）
    指令4：如果检查结论中有诊断，但和病案诊断不相关，则输出“有缺陷”。其他情况输出“无缺陷”
    
    回答：
    指令1:
    抽取检查结论中的症状:
    腹腔扫查未见明显异常包块
    
    指令2:
    抽取检查结论中的诊断:
    无
    
    指令3:
    检查结论中没有诊断,因此跳过该指令。
    
    指令4：
    检查结论中没有诊断,因此输出"无缺陷"。
    ##################################################
    [病历内容]
    {syqt42输入内容}
    指令1：抽取检查结论中的症状
    指令2：抽取检查结论中的诊断
    指令3：如果检查结论中有诊断，则判断和病案诊断是否相关（相关/不相关）
    指令4：如果检查结论中有诊断，但和病案诊断不相关，则输出“有缺陷”。其他情况输出“无缺陷”
    
    回答：
QUESTION: |
    问题:
    {}
    所有检查报告中提示的疾病都在病案首页诊断中
    回答