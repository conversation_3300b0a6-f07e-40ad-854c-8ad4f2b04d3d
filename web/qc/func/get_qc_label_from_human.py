from web.qc.qc_v2 import qc_v2
import pandas as pd
import os
import json
import datetime
import requests

'''
SET @my_task_id = 10;
select aa.*,bb.prompt from (
SELECT
	customerId,
	recordId,
	ruleNumber,
	ruleName,
	systemResult,
	gptResult,
	artificialResult,
	artificialRemark
FROM
	(
		SELECT
			a.customer_id AS customerId,
			a.record_id AS recordId,
			a.task_id AS taskId,
			a.rule_number AS ruleNumber,
			tr.rule_name AS ruleName,
			a.system_result AS systemResult,
			a.remark AS systemRemark,
			b.system_result AS gptResult,
			b.remark AS gptRemark,
			i.artificial_result AS artificialResult,
			i.remark AS artificialRemark,
			r.record_remind_tag AS recordRemarkTag,
			a.disease_id AS diseaseId,
			a.disease_name AS diseaseName
		FROM
			(
				SELECT
					customer_id,
					record_id,
					task_id,
					rule_number,
					rule_name,
					system_result,
					remark,
					disease_id,
					disease_name
				FROM
					hmcdss2.er_mc_task_result_item
				WHERE
					task_id = @my_task_id
				AND task_record_id = (
					SELECT
						id
					FROM
						hmcdss2.er_mc_task_record
					WHERE
						task_id = @my_task_id
					ORDER BY
						id DESC
					LIMIT 1
				)
			) a
		LEFT JOIN hmcdss2.er_mc_task_artificial_item i ON a.task_id = i.task_id
		AND a.record_id = i.record_id
		AND a.rule_number = i.rule_number
		LEFT JOIN hmcdss2.er_mc_task_record_rule_remark r ON a.task_id = r.task_id
		AND a.record_id = r.record_id
		AND a.rule_number = r.rule_number
		LEFT JOIN (
			SELECT
				record_id,
				task_id,
				rule_number,
				system_result,
				remark
			FROM
				hmcdss2.er_mc_task_result_item_gpt
			WHERE
				task_id = @my_task_id
			AND task_record_id = (
				SELECT
					id
				FROM
					hmcdss2.er_mc_task_record
				WHERE
					task_id = @my_task_id
				ORDER BY
					id DESC
				LIMIT 1
			)
		) b ON a.task_id = b.task_id
		AND a.record_id = b.record_id
		AND a.rule_number = b.rule_number
		LEFT JOIN hmcdss2.er_mc_task_rule tr ON a.task_id = tr.task_id
		AND a.rule_number = tr.rule_number
		WHERE
			a.record_id IN (
				SELECT DISTINCT
					record_id
				FROM
					hmcdss2.er_mc_task_artificial_item
				WHERE
					task_id = @my_task_id
			)
	) AS t
WHERE
	t.artificialResult IS NOT NULL and artificialResult!=-1
) aa
left join 
hm_llm.llm_qc bb on aa.customerId = bb.customer_id and aa.recordId = bb.record_id and aa.ruleNumber =bb.answer_type
'''


def tuomin_post(content):
    url = "http://172.16.2.218:9668/predict"
    try:
        payload = json.dumps({
            "content": content,
            "type": "auto",
            "dataBase": "hmcdss2_push_1220_20221124",
            "recordId": 9421080,
            "progressId": 11442800,
            "customerId": 1220
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return json.loads(response.text)["body"]["mask_text"]
    except:
        print("error！", content)
        return ""


from tqdm import tqdm

if __name__ == '__main__':
    # 477961
    qc_v2 = qc_v2()

    new_lst = []
    temp_lst = []
    folder = "/Users/<USER>/Downloads/data"
    rule_cnt = {}

    # for filename in ['广西附一大模型评测_柯南_0904.xlsx']:
    #     print(filename)
    for filename in os.listdir(folder):
        customerId = -1
        if "~" in filename:
            continue
        if "zheer" in filename:
            customerId = 1687

        if "fdzs" in filename:
            customerId = 1525

        if "sdfy" in filename:
            customerId = 1543

        print(os.path.join(folder, filename))

        lst = pd.read_excel(os.path.join(folder, filename), sheet_name="Sheet2").fillna("").to_dict("records")

        # instruction = lst[0]['instruction']
        ruleNumber = lst[0]['ruleNumber']
        idx = 0
        for _lst in tqdm(lst):
            new_data = {}
            input_obj = {}
            ideal = ''
            customerId = _lst.get('customerId', customerId)
            instruction = _lst['instruction']
            if 'ruleNumber' in _lst:
                _ruleNumber = _lst['ruleNumber']
                if len(_ruleNumber.strip()) != 0:
                    ruleNumber = _ruleNumber
            # input_obj["input"] = tuomin_post(_lst['text'])
            try:
                input_obj = json.loads(_lst['data'])
                # print(input_obj['input2'])
                instruction = instruction.replace("{input}", "{input1}", 1)
                instruction = instruction.replace("{input}", "{input2}", 2)

            except:
                # input_obj = {"input1": _lst['data']}
                instruction = instruction.replace("{input}", "{input1}", 1)
                input_obj["input1"] = _lst['input'] if 'input' in _lst else _lst['data']
            new_data['customerId'] = customerId
            new_data["dict_input"] = input_obj
            new_data["instruction"] = instruction
            new_data['rule'] = ruleNumber
            # new_data['ideal'] = "无缺陷" if _lst['是否有缺陷'] == '不满足' or _lst['是否有缺陷'] == '无缺陷' else "有缺陷"
            if '人工标记' in _lst:
                ideal = _lst['人工标记']
            if '人工标注' in _lst:
                ideal = _lst['人工标注']
            if len(ideal.strip()) == 0:
                temp_lst.append(new_data)
                continue
            if ideal == '不满足':
                continue
            new_data['ideal'] = "无缺陷" if ideal == '无缺陷' else "有缺陷"

            # new_data['qc_label_remark'] = _lst['备注']
            new_data['group_id'] = idx
            idx += 1
            new_lst.append(new_data)

            if ruleNumber not in rule_cnt:
                rule_cnt[ruleNumber] = 0

            rule_cnt[ruleNumber] += 1


        def datetime_to_string(obj):
            if isinstance(obj, datetime.datetime):
                return obj.isoformat()
            return obj

    # with open("mc_rule_sdff_pf6_0711.jsonl", "w", encoding="utf-8") as jsonl_file:
    #     for item in new_lst:
    #         json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
    #         jsonl_file.write("\n")
    sum_cnt = 0
    for rule, cnt in rule_cnt.items():
        sum_cnt += cnt

    print(rule_cnt.keys(), sum_cnt)
    print(rule_cnt)

    with open("mc_rule_zheer_0729.jsonl", "w", encoding="utf-8") as jsonl_file:
        print("new_lst", len(new_lst))
        for item in new_lst:
            json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
            jsonl_file.write("\n")

    with open("mc_rule_zheer_0729_unmark.jsonl", "w", encoding="utf-8") as jsonl_file:
        print("temp_lst", len(temp_lst))
        for item in temp_lst:
            json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
            jsonl_file.write("\n")

    # with open("qc_rule_浙儿_0704_error.jsonl", "w", encoding="utf-8") as jsonl_file:
    #     for item in error_lst:
    #         json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
    #         jsonl_file.write("\n")
