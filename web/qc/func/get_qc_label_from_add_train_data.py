import json


def process_gpt_response(result):
    """简化的响应处理函数"""
    result = result.strip()
    result_data = result.split("总结")[-1] if "总结" in result else result

    # 判断条件简化
    negative_indicators = ["违反" in result and all(x not in result for x in [
        "不违反", "未违反", "没有发现违反", "未发现违反", "没有违反"
    ])]

    uncertain_indicators = any(x in result for x in [
        "无法判断是否违反", "无法判断是否符合",
        "无法确定是否符合", "无法确定是否违反"
    ])

    positive_indicators = any([
        any(x in result for x in ["不违反", "未违反", "没有发现违反", "未发现违反"]),
        "符合" in result and "不符合" not in result
    ])

    if uncertain_indicators:
        pred = "无缺陷"
    elif negative_indicators:
        pred = "有缺陷"
    elif positive_indicators:
        pred = "无缺陷"
    else:
        pred = "有缺陷"

    return result_data, pred


def process_samples(samples_file, train_files):
    """处理样本数据"""
    distinct_lst = set()  # 使用set代替list提高查找效率
    samples = []

    # 处理samples.jsonl
    with open(samples_file) as f:
        for line in f:
            obj = json.loads(line)
            instruction = obj['instruction']
            input_text = obj['dict_input']['input1']
            instruction = instruction.replace("input1", input_text)
            obj["output"] = None
            obj['dataset_type'] = 'test'
            distinct_lst.add(instruction.strip())
            distinct_lst.add(input_text.strip())
            samples.append(obj)

    # 处理训练数据文件
    for train_file in train_files:
        lines = json.load(open(train_file))
        for line in lines:
            instruction = line['instruction'].strip()
            if instruction in distinct_lst:
                continue

            output = line['output']
            _, ideal = process_gpt_response(output)
            obj = {
                'dict_input': {"input1": instruction},
                'instruction': "{input1}",
                'dataset_type': 'train',
                'rule': line.get('rule', '-1'),
                'output': output,
                'group_id': len(samples) + 1,
                'customer_id': line.get('customer_id', -1),
                'ideal': ideal
            }

            distinct_lst.add(instruction)
            samples.append(obj)

    return samples


if __name__ == '__main__':
    train_files = ["mc_train_dataset_v11.json", "mc_gpt4o_0927.json"]
    samples = process_samples("samples.jsonl", train_files)
    with open("new_samples.jsonl", "w") as f:
        for _sample in samples:
            f.write(json.dumps(_sample, ensure_ascii=False) + "\n")
