from web.qc.qc_v2 import qc_v2
import pandas as pd
import os
import json
import datetime
import requests

'''
# 取现场评测数据
SET @my_task_id = 10;
select aa.*,bb.answer_type, bb.prompt from (
SELECT
customerId,
recordId,
ruleNumber,
ruleName,
systemResult,
gptResult,
artificialResult,
artificialRemark
FROM
(
SELECT
a.customer_id AS customerId,
a.record_id AS recordId,
a.task_id AS taskId,
a.rule_number AS ruleNumber,
tr.rule_name AS ruleName,
a.system_result AS systemResult,
a.remark AS systemRemark,
b.system_result AS gptResult,
b.remark AS gptRemark,
i.artificial_result AS artificialResult,
i.remark AS artificialRemark,
r.record_remind_tag AS recordRemarkTag,
a.disease_id AS diseaseId,
a.disease_name AS diseaseName
FROM
(
SELECT
customer_id,
record_id,
task_id,
rule_number,
rule_name,
system_result,
remark,
disease_id,
disease_name
FROM
hmcdss2.er_mc_task_result_item
WHERE
task_id = @my_task_id
AND task_record_id = (
SELECT
id
FROM
hmcdss2.er_mc_task_record
WHERE
task_id = @my_task_id
ORDER BY
id DESC
LIMIT 1
)
) a
LEFT JOIN hmcdss2.er_mc_task_artificial_item i ON a.task_id = i.task_id
AND a.record_id = i.record_id
AND a.rule_number = i.rule_number
LEFT JOIN hmcdss2.er_mc_task_record_rule_remark r ON a.task_id = r.task_id
AND a.record_id = r.record_id
AND a.rule_number = r.rule_number
LEFT JOIN (
SELECT
record_id,
task_id,
rule_number,
system_result,
remark
FROM
hmcdss2.er_mc_task_result_item_gpt
WHERE
task_id = @my_task_id
AND task_record_id = (
SELECT
id
FROM
hmcdss2.er_mc_task_record
WHERE
task_id = @my_task_id
ORDER BY
id DESC
LIMIT 1
)
) b ON a.task_id = b.task_id
AND a.record_id = b.record_id
AND a.rule_number = b.rule_number
LEFT JOIN hmcdss2.er_mc_task_rule tr ON a.task_id = tr.task_id
AND a.rule_number = tr.rule_number
WHERE
a.record_id IN (
SELECT DISTINCT
record_id
FROM
hmcdss2.er_mc_task_artificial_item
WHERE
task_id = @my_task_id
)
) AS t
WHERE
t.artificialResult IS NOT NULL and artificialResult!=-1
) aa
left join
(select 
case answer_type when 'pf2' then 'nh100' 
when 'pf9' then 'nh97' 
else answer_type end as rule_id,
answer_type,
record_id,
prompt 
from hm_llm.llm_qc)
bb on aa.recordId = bb.record_id 
and aa.ruleNumber = bb.rule_id
'''

'''
SELECT *
FROM (
  SELECT 
    t.*,
    @rn := IF(@current_category = CONCAT(answer_type, ' - ', remark), @rn + 1, 1) AS rn,
    @current_category := CONCAT(answer_type, ' - ', remark)
  FROM (
    SELECT *
    FROM hm_llm.llm_qc
    WHERE answer != ''
      AND create_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)
    ORDER BY CONCAT(answer_type, ' - ', remark), create_date DESC
  ) t
  CROSS JOIN (SELECT @rn := 0, @current_category := '') vars
) ranked
WHERE rn <= 500
'''

import re
import json
import requests
import pandas as pd
from datetime import datetime
import copy


class MedicalRecordProcessor:
    def __init__(self, output_path):
        self.output_path = output_path
        self.dist_prompt = []
        self.res_prompt = {}
        self.true_data = []
        self.error_data = []
        self.rule_map = self.init_prompt()

    def init_prompt(self):
        prompts = json.load(open("rule_gpt_prompt_v6.3.json"))
        rule_map = {}
        for _prompt in prompts:
            name = _prompt['name']
            if name == "pf2":
                name = "nh100"
            if name == "pf9":
                name = "nh97"
            lst = []
            for _result in _prompt['step1_result']:
                step1_response = _result['step1_response']
                lst.append(step1_response)
            rule_map[name] = lst
        return rule_map

    def rule_process(self, rule_data):
        rule_number = rule_data["ruleNumber"]
        prompt = rule_data['prompt']
        if rule_number == "nh100":
            prompt = prompt.split("========================================================")[-1]
        return prompt

    def exa_prompt_record(self, medical_record):
        pattern = r'((?:\n|^)\[内容-.*?\])(.*?)(\[\内容-.*?-结束\])'
        prompt = medical_record
        matches = re.findall(pattern, medical_record, re.DOTALL)
        record_lst = []
        sections_to_remove = []

        for match in matches:
            section_name = match[0].strip()
            content = match[1].strip()
            end_section_name = match[2].strip()
            record_lst.append(f"{section_name}\n{content}\n{end_section_name}")
            sections_to_remove.append(re.escape(section_name))
            sections_to_remove.append(re.escape(content))
            sections_to_remove.append(re.escape(end_section_name))

        combined_pattern = '|'.join(sections_to_remove)
        cleaned_prompt = re.sub(combined_pattern, '', prompt, flags=re.DOTALL).strip()
        record_str = "\n".join(record_lst).strip()
        return cleaned_prompt, record_str

    def tuomin_post(self, content):
        url = "http://************:9668/predict"
        try:
            payload = json.dumps({
                "content": content,
                "type": "auto",
                "dataBase": "hmcdss2_push_1220_20221124",
                "recordId": 9421080,
                "progressId": 11442800,
                "customerId": 1220
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=payload)
            return json.loads(response.text)["body"]["mask_text"]
        except:
            print("error！", content)
            return ""

    def read_jsonl(self, path):
        res = []
        with open(path) as f:
            lines = f.readlines()
            for _line in lines:
                res.append(json.loads(_line)['dict_input']['input1'].replace("\\n", "").replace("\n", ""))
        return res

    def filter_datas_v1(self, datas):
        filter_map = {}
        res = []
        for _data in datas:
            key = (_data['customer_id'], _data['answer_type'], _data['remark'])
            if key not in filter_map:
                filter_map[key] = 0
            if filter_map[key] > 10:
                continue
            filter_map[key] += 1
            res.append(_data)
        return res

    def match_prompts_datas(self, datas):
        new_data = []
        for _data in datas:
            ruleNumber = _data['ruleNumber']
            if ruleNumber not in self.rule_map:
                continue
            for prompt in self.rule_map[ruleNumber][:5]:
                data = copy.copy(_data)
                data['prompt'] = prompt + '''
##病历
{input1}
你是质控专家，请从日常工作中审核该规则的角度，给出回答。一步一步分析
'''
                data['input'] = data['prompt'].replace("{input1}", data['data'])
                new_data.append(data)
        return new_data

    def process_data(self):
        sample_datas = self.read_jsonl("samples.jsonl")

        for _path in ['/Users/<USER>/淄博医院0923.json', '/Users/<USER>/广西附一0923.json']:
            datas = json.load(open(_path, "r"))
            datas = list(datas.values())[0]

            for _data in datas:
                if _data['prompt'] is None:
                    continue

                if 'answer_type' in _data:
                    answer_type = _data['answer_type']
                    if answer_type == 'pf2':
                        answer_type = 'nh100'
                    if answer_type == 'pf9':
                        answer_type = 'nh97'
                    _data['answer_type'] = answer_type
                    _data["ruleNumber"] = answer_type

                prompt = self.rule_process(_data)
                if prompt in self.dist_prompt:
                    continue

                instruction, record_str = self.exa_prompt_record(prompt)
                if record_str == "" or instruction == "":
                    continue

                if record_str.replace("\\n", "").replace("\n", "") in sample_datas:
                    continue

                input_obj = {}
                _data["instruction"] = instruction
                _data["data"] = record_str
                _data['人工标注'] = ''

                gpt_result = _data.get('gptResult', -1)
                artificial_result = _data.get('artificialResult', -1)
                if gpt_result == artificial_result:
                    self.true_data.append(_data)
                else:
                    self.error_data.append(_data)

                self.dist_prompt.append(prompt)
                self.res_prompt[
                    (_data['customer_id'], _data['answer_type'], _data['remark'], _data['record_id'])] = _data

        res_datas = self.res_prompt.values()
        new_datas = self.filter_datas_v1(res_datas)
        match_data = self.match_prompts_datas(new_datas)

        self.save_results(match_data)

    def save_results(self, match_data):
        def datetime_to_string(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            return obj

        if "xls" in self.output_path:
            pd.DataFrame(match_data).to_excel(self.output_path.split('/')[-1].replace(".xls", "_一致.xlsx"))
        else:
            pd.DataFrame(match_data).to_excel(self.output_path.split('/')[-1].replace(".json", "_一致.xlsx"))


if __name__ == '__main__':
    processor = MedicalRecordProcessor('训练结果.json')
    processor.process_data()
