from web.qc.qc_v2 import qc_v2
import pandas as pd
import os
import json
import datetime
import requests

'''
# 取现场评测数据
SET @my_task_id = 10;
select aa.*,bb.answer_type, bb.prompt from (
SELECT
customerId,
recordId,
ruleNumber,
ruleName,
systemResult,
gptResult,
artificialResult,
artificialRemark
FROM
(
SELECT
a.customer_id AS customerId,
a.record_id AS recordId,
a.task_id AS taskId,
a.rule_number AS ruleNumber,
tr.rule_name AS ruleName,
a.system_result AS systemResult,
a.remark AS systemRemark,
b.system_result AS gptResult,
b.remark AS gptRemark,
i.artificial_result AS artificialResult,
i.remark AS artificialRemark,
r.record_remind_tag AS recordRemarkTag,
a.disease_id AS diseaseId,
a.disease_name AS diseaseName
FROM
(
SELECT
customer_id,
record_id,
task_id,
rule_number,
rule_name,
system_result,
remark,
disease_id,
disease_name
FROM
hmcdss2.er_mc_task_result_item
WHERE
task_id = @my_task_id
AND task_record_id = (
SELECT
id
FROM
hmcdss2.er_mc_task_record
WHERE
task_id = @my_task_id
ORDER BY
id DESC
LIMIT 1
)
) a
LEFT JOIN hmcdss2.er_mc_task_artificial_item i ON a.task_id = i.task_id
AND a.record_id = i.record_id
AND a.rule_number = i.rule_number
LEFT JOIN hmcdss2.er_mc_task_record_rule_remark r ON a.task_id = r.task_id
AND a.record_id = r.record_id
AND a.rule_number = r.rule_number
LEFT JOIN (
SELECT
record_id,
task_id,
rule_number,
system_result,
remark
FROM
hmcdss2.er_mc_task_result_item_gpt
WHERE
task_id = @my_task_id
AND task_record_id = (
SELECT
id
FROM
hmcdss2.er_mc_task_record
WHERE
task_id = @my_task_id
ORDER BY
id DESC
LIMIT 1
)
) b ON a.task_id = b.task_id
AND a.record_id = b.record_id
AND a.rule_number = b.rule_number
LEFT JOIN hmcdss2.er_mc_task_rule tr ON a.task_id = tr.task_id
AND a.rule_number = tr.rule_number
WHERE
a.record_id IN (
SELECT DISTINCT
record_id
FROM
hmcdss2.er_mc_task_artificial_item
WHERE
task_id = @my_task_id
)
) AS t
WHERE
t.artificialResult IS NOT NULL and artificialResult!=-1
) aa
left join
(select 
case answer_type when 'pf2' then 'nh100' 
when 'pf9' then 'nh97' 
else answer_type end as rule_id,
answer_type,
record_id,
prompt 
from hm_llm.llm_qc)
bb on aa.recordId = bb.record_id 
and aa.ruleNumber = bb.rule_id
'''

'''
SELECT *
FROM (
  SELECT 
    t.*,
    @rn := IF(@current_category = CONCAT(answer_type, ' - ', remark), @rn + 1, 1) AS rn,
    @current_category := CONCAT(answer_type, ' - ', remark)
  FROM (
    SELECT *
    FROM hm_llm.llm_qc
    WHERE answer != ''
      AND create_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 1 MONTH)
    ORDER BY CONCAT(answer_type, ' - ', remark), create_date DESC
  ) t
  CROSS JOIN (SELECT @rn := 0, @current_category := '') vars
) ranked
WHERE rn <= 500
'''

import re
import json


def rule_process(rule_data):
    ruleNumber = rule_data["ruleNumber"]
    prompt = rule_data['prompt']
    if ruleNumber == "nh100":
        prompt = prompt.split("========================================================")[-1]
    return prompt


def exa_prompt_record(medical_record):
    # 正则表达式模式，用于匹配不同的内容段
    pattern = r'((?:\n|^)\[内容-.*?\])(.*?)(\[\内容-.*?-结束\])'
    prompt = medical_record
    # 使用正则表达式进行匹配
    matches = re.findall(pattern, medical_record, re.DOTALL)
    record_lst = []
    sections_to_remove = []
    # 打印结果
    for match in matches:
        section_name = match[0].strip()
        content = match[1].strip()
        end_section_name = match[2].strip()
        record_lst.append(f"{section_name}\n{content}\n{end_section_name}")
        sections_to_remove.append(re.escape(section_name))
        sections_to_remove.append(re.escape(content))
        sections_to_remove.append(re.escape(end_section_name))

    combined_pattern = '|'.join(sections_to_remove)
    cleaned_prompt = re.sub(combined_pattern, '', prompt, flags=re.DOTALL).strip()

    record_str = "\n".join(record_lst).strip()
    return cleaned_prompt, record_str


def tuomin_post(content):
    url = "http://************:9668/predict"
    try:
        payload = json.dumps({
            "content": content,
            "type": "auto",
            "dataBase": "hmcdss2_push_1220_20221124",
            "recordId": 9421080,
            "progressId": 11442800,
            "customerId": 1220
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return json.loads(response.text)["body"]["mask_text"]
    except:
        print("error！", content)
        return ""


def read_jsonl(path):
    res = []
    with open(path) as f:
        lines = f.readlines()
        for _line in lines:
            res.append(json.loads(_line)['dict_input']['input1'].replace("\\n","").replace("\n",""))
    return res


if __name__ == '__main__':
    qc_v2 = qc_v2()

    new_lst = []
    temp_lst = []
    dist_prompt = []

    path = '/Users/<USER>/淄博医院0923.json'
    path = '/Users/<USER>/广西附一0923.json'

    # json data
    datas = json.load(open(path, "r"))
    datas = list(datas.values())[0]
    # res
    sample_datas = read_jsonl("samples.jsonl")
    error_data = []
    true_data = []
    for _data in datas:
        if _data['prompt'] is None:
            continue

        if 'answer_type' in _data:
            answer_type = _data['answer_type']
            if answer_type == 'pf2':
                answer_type = 'nh100'
            if answer_type == 'pf9':
                answer_type = 'nh97'
            _data['answer_type'] = answer_type
            _data["ruleNumber"] = answer_type
        prompt = rule_process(_data)
        if prompt in dist_prompt:
            continue
        instruction, record_str = exa_prompt_record(prompt)
        if record_str == "" or instruction == "":
            continue
        if record_str.replace("\\n","").replace("\n","") in sample_datas:
            # print(record_str)
            continue
        input_obj = {}
        # _data["dict_input"] = {"input1": record_str}
        _data["instruction"] = instruction
        _data["data"] = record_str
        _data['人工标注'] = ''
        gptResult = _data.get('gptResult', -1)
        artificialResult = _data.get('artificialResult', -1)
        if gptResult == artificialResult:
            true_data.append(_data)
        else:
            error_data.append(_data)
        dist_prompt.append(prompt)


    def datetime_to_string(obj):
        if isinstance(obj, datetime.datetime):
            return obj.isoformat()
        return obj


    if "xls" in path:
        pd.DataFrame(error_data).to_excel(path.split('/')[-1].replace(".xls", "_不一致.xlsx"))
        pd.DataFrame(true_data).to_excel(path.split('/')[-1].replace(".xls", "_一致.xlsx"))
    else:
        pd.DataFrame(error_data).to_excel(path.split('/')[-1].replace(".json", "_不一致.xlsx"))
        pd.DataFrame(true_data).to_excel(path.split('/')[-1].replace(".json", "_一致.xlsx"))
