import re
import json
import requests
import pandas as pd
from datetime import datetime
import copy
import os
from openai import AzureOpenAI

from tqdm import tqdm


class GPTProcessor:
    def __init__(self, output_path):
        self.output_path = output_path
        self.datas = pd.read_excel(output_path).to_dict("records")
        self.client = AzureOpenAI(
            azure_endpoint="https://hmgpt-4.openai.azure.com/",
            api_key=os.getenv("OPENAI_API_KEY"),  # It's better to use environment variables for API keys
            api_version="2024-02-15-preview"
        )
        self.rule_map = self.init_prompt()

    def init_prompt(self):
        prompts = json.load(open("rule_gpt_prompt_v6.3.json"))
        rule_map = {}
        for _prompt in prompts:
            name = _prompt['name']
            if name == "pf2":
                name = "nh100"
            if name == "pf9":
                name = "nh97"
            lst = []
            for _result in _prompt['step1_result']:
                step1_response = _result['step1_response']
                lst.append(step1_response)
            rule_map[name] = lst
        return rule_map

        # models = self.client.models.list()
        # for model in models:
        #     print(model)

    # Example function to make a request
    def generate_text(self, prompt):
        messages = [
            {"role": "system", "content": "你是一个有用的病历质控助手。"},
            {"role": "user", "content": prompt},
        ]
        completion = self.client.chat.completions.create(
            model="gpt-4o-mini",  # Replace with your actual model name
            messages=messages,
            temperature=0.01,
            max_tokens=1600,
            top_p=0.95,
            frequency_penalty=0,
            presence_penalty=0,
            stop=None
        )
        #
        # response = self.client.completions.create(
        #     model="gpt-4o-2",  # Replace with your actual model name
        #     # prompt=prompt,
        #     messages=prompt,
        #     max_tokens=100
        # )
        return completion.choices[0].message.content.strip()

    def tuomin_post(self, content):
        url = "http://************:9668/predict"
        try:
            payload = json.dumps({
                "content": content,
                "type": "auto",
                "dataBase": "hmcdss2_push_1220_20221124",
                "recordId": 9421080,
                "progressId": 11442800,
                "customerId": 1220
            })
            headers = {
                'Content-Type': 'application/json'
            }

            response = requests.request("POST", url, headers=headers, data=payload)
            return json.loads(response.text)["body"]["mask_text"]
        except:
            print("error！", content)
            return ""

    def read_jsonl(self, path):
        res = []
        with open(path) as f:
            lines = f.readlines()
            for _line in lines:
                res.append(json.loads(_line)['dict_input']['input1'].replace("\\n", "").replace("\n", ""))
        return res

    def match_prompts_datas(self, datas):
        new_data = []
        for _data in datas:
            ruleNumber = _data['ruleNumber']
            #             if ruleNumber not in self.rule_map:
            #                 continue
            #             for prompt in self.rule_map[ruleNumber][:5]:
            data = copy.copy(_data)

            data['input'] = data['prompt'].replace("{input1}", data['mask_data'])
            new_data.append(data)
        return new_data

    def filter_ruleNumber(self, datas):
        ruleNumber_map = {}
        filter_lst = []
        filter_ruleNumber_datas = []
        for _data in datas:
            # 按照一个规则，一家医院，有/无缺陷，5个record_id过滤
            ruleNumber = _data['ruleNumber']
            record_id = _data['record_id']
            prompt = _data['prompt']
            remark = _data['remark']
            customer_id = _data['customer_id']
            if "Facebook" in _data['prompt']:
                continue

            if (ruleNumber, customer_id, prompt, remark) not in ruleNumber_map:
                ruleNumber_map[(ruleNumber, customer_id, prompt, remark)] = 0
            if ruleNumber_map[(ruleNumber, customer_id, prompt, remark)] < 3:
                filter_lst.append((ruleNumber, customer_id,record_id, prompt, remark))
            ruleNumber_map[(ruleNumber, customer_id, prompt, remark)] += 1

            if (ruleNumber, customer_id,record_id, prompt, remark) not in filter_lst:
                continue
            filter_ruleNumber_datas.append(_data)

        return filter_ruleNumber_datas

    def save_results(self, match_data):
        def datetime_to_string(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            return obj

        if "xls" in self.output_path:
            pd.DataFrame(match_data).to_excel(self.output_path.split('/')[-1].replace(".xlsx", "_一致.xlsx"))
        else:
            pd.DataFrame(match_data).to_excel(self.output_path.split('/')[-1].replace(".json", "_一致.xlsx"))

    def tuomin_func(self, datas):
        idx = 0
        for _data in tqdm(datas):
            _data['mask_data'] = processor.tuomin_post(_data['data'])
            idx += 1
            if idx % 100 == 0:
                pd.DataFrame(datas).to_excel('训练结果_一致_mask.xlsx')

        pd.DataFrame(datas).to_excel('训练结果_一致_mask.xlsx')

    def gpt4_predict(self, datas):
        idx = 0
        for _data in tqdm(datas):
            _data["gpt4_output"] = self.generate_text(_data['input'])
            idx += 1
            if idx % 100 == 0:
                pd.DataFrame(datas).to_excel('训练结果_一致_gpt4.xlsx')

        pd.DataFrame(datas).to_excel('训练结果_一致_gpt4.xlsx')

    def answer_predict(self, datas):
        for _data in datas:
            _data["gpt4_answer"] = self.super_get_answer_promt({"result": _data['gpt4_output']})[1]
        pd.DataFrame(datas).to_excel('训练结果_一致_gpt4_answer.xlsx')

    def super_get_answer_promt(self, data):
        result = data["result"].strip()

        if "总结" in result:
            result_data = result.split("总结")[-1]
        else:
            result_data = result
        pred = ""
        gpt_response = result.replace("确定是否违反原则", "").replace("确定是否符合原则", "")
        '''
        gpt_response = "\n".join([x for x in gpt_response.split("\n") if x.find("Step") < 0])
        if "可以视为符合原则" in gpt_response:
            ideal = "无缺陷"
            pred = "符合"
        el
        '''
        if "无法判断是否违反" in gpt_response or "无法判断是否符合" in gpt_response or "无法确定是否符合" in gpt_response \
                or "无法确定是否违反" in gpt_response:
            pred = "无缺陷"
        elif "违反" in gpt_response and "不违反" not in gpt_response and "未违反" not in gpt_response \
                and "没有发现违反" not in gpt_response \
                and "未发现违反" not in gpt_response \
                and "没有违反" not in gpt_response:

            pred = "有缺陷"

        elif "不违反" in gpt_response or "未违反" in gpt_response \
                or "没有发现违反" in gpt_response \
                or "未发现违反" in gpt_response \
                or "没有发现违反" in gpt_response:
            pred = "无缺陷"
        elif "符合" in gpt_response and "不符合" not in gpt_response:
            pred = "无缺陷"
        else:
            pred = "有缺陷"

        if pred == "有缺陷":
            pred = "失败"
        else:
            pred = "成功"
        return result_data, pred

    def to_train_dataset_json(self, datas, output_path):
        dataset_json = []
        for _data in datas:
            obj = {
                "instruction": _data['input'],
                "input": "",
                "output": _data['gpt4_output']
            }
            dataset_json.append(obj)
        print(f"length {len(dataset_json)}")
        json.dump(dataset_json, open(output_path, "w"), ensure_ascii=False, indent=1)


if __name__ == '__main__':
    processor = GPTProcessor('训练结果_一致_gpt4_answer.xlsx')
    filter_ruleNumber_datas = processor.filter_ruleNumber(processor.datas)
    # new_data = processor.match_prompts_datas(processor.datas)
    # processor.gpt4_predict(new_data)
    processor.save_results(filter_ruleNumber_datas)
    processor.to_train_dataset_json(filter_ruleNumber_datas, "mc_gpt4o_mini_0926.json")
