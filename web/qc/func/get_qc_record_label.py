from web.qc.qc_v2 import qc_v2
import pandas as pd
import re
import json
import datetime

'''
SET @my_task_id = 10;
select aa.*,bb.prompt from (
SELECT
	customerId,
	recordId,
	ruleNumber,
	ruleName,
	systemResult,
	gptResult,
	artificialResult,
	artificialRemark
FROM
	(
		SELECT
			a.customer_id AS customerId,
			a.record_id AS recordId,
			a.task_id AS taskId,
			a.rule_number AS ruleNumber,
			tr.rule_name AS ruleName,
			a.system_result AS systemResult,
			a.remark AS systemRemark,
			b.system_result AS gptResult,
			b.remark AS gptRemark,
			i.artificial_result AS artificialResult,
			i.remark AS artificialRemark,
			r.record_remind_tag AS recordRemarkTag,
			a.disease_id AS diseaseId,
			a.disease_name AS diseaseName
		FROM
			(
				SELECT
					customer_id,
					record_id,
					task_id,
					rule_number,
					rule_name,
					system_result,
					remark,
					disease_id,
					disease_name
				FROM
					hmcdss2.er_mc_task_result_item
				WHERE
					task_id = @my_task_id
				AND task_record_id = (
					SELECT
						id
					FROM
						hmcdss2.er_mc_task_record
					WHERE
						task_id = @my_task_id
					ORDER BY
						id DESC
					LIMIT 1
				)
			) a
		LEFT JOIN hmcdss2.er_mc_task_artificial_item i ON a.task_id = i.task_id
		AND a.record_id = i.record_id
		AND a.rule_number = i.rule_number
		LEFT JOIN hmcdss2.er_mc_task_record_rule_remark r ON a.task_id = r.task_id
		AND a.record_id = r.record_id
		AND a.rule_number = r.rule_number
		LEFT JOIN (
			SELECT
				record_id,
				task_id,
				rule_number,
				system_result,
				remark
			FROM
				hmcdss2.er_mc_task_result_item_gpt
			WHERE
				task_id = @my_task_id
			AND task_record_id = (
				SELECT
					id
				FROM
					hmcdss2.er_mc_task_record
				WHERE
					task_id = @my_task_id
				ORDER BY
					id DESC
				LIMIT 1
			)
		) b ON a.task_id = b.task_id
		AND a.record_id = b.record_id
		AND a.rule_number = b.rule_number
		LEFT JOIN hmcdss2.er_mc_task_rule tr ON a.task_id = tr.task_id
		AND a.rule_number = tr.rule_number
		WHERE
			a.record_id IN (
				SELECT DISTINCT
					record_id
				FROM
					hmcdss2.er_mc_task_artificial_item
				WHERE
					task_id = @my_task_id
			)
	) AS t
WHERE
	t.artificialResult IS NOT NULL and artificialResult!=-1
) aa
left join 
hm_llm.llm_qc bb on aa.customerId = bb.customer_id and aa.recordId = bb.record_id and aa.ruleNumber =bb.answer_type
'''
if __name__ == '__main__':
    # 477961
    qc_v2 = qc_v2()
    # path = "/Users/<USER>/tmp/大模型评测数据/27项.xlsx"
    paths = [
        # "/Users/<USER>/tmp/浙儿kenan1.xlsx",
        # "/Users/<USER>/tmp/浙儿kenan2.xlsx"
        "/Users/<USER>/tmp/prompt苏大附一优化.xlsx"
        # "/Users/<USER>/tmp/大模型评测数据/27项.xlsx",
        # "/Users/<USER>/tmp/大模型评测数据/评分表.xlsx"
    ]
    # 多次调用的规则
    group_rules = ['pf9', 'nh98', 'nh414', 'pfw19', 'pfw1', 'nh296', 'pfw2', 'syqt42', 'pfw27', 'nh155', 'pfw5', 'pfw3','pfw9']
    single_lst = []
    group_lst = []
    error_lst = []
    rule_ids = set()
    for path in paths:
        lst = pd.read_excel(path).fillna("").to_dict("records")
        for _lst in lst:
            ruleNumber = _lst['ruleNumber']
            prompt = _lst['prompt'].replace("\n", "\\n")
            if ruleNumber not in qc_v2.rule_flow or qc_v2.rule_flow[ruleNumber].config is None:
                continue
            if ruleNumber in ['nh190']:
                FEW_SHOT = qc_v2.rule_flow[ruleNumber].config["ZERO_SHOT"].strip()
            else:
                FEW_SHOT = qc_v2.rule_flow[ruleNumber].config["FEW_SHOT"].strip()

            regex_sub = r"{.*?}"
            subst = "(.*)"
            replaceFEW_SHOT = FEW_SHOT.replace("\n", "\\\\n").replace("[", "\[").replace("]", "\]").replace("/",
                                                                                                            "\/").replace(
                "-", "\-").replace("(", "\(").replace(")", "\)").replace("***", "")
            regex_find = re.sub(regex_sub, subst, replaceFEW_SHOT, 0, re.MULTILINE)
            FEW_SHOT = re.sub(regex_sub, subst, FEW_SHOT, 0, re.MULTILINE)
            matches = re.search(regex_find, prompt)

            if matches:
                new_data = {}
                input_obj = {}
                error_flag = 0
                for groupNum in range(0, len(matches.groups())):
                    groupNum = groupNum + 1
                    input_text = matches.group(groupNum)
                    input_obj["input{}".format(groupNum)] = input_text
                    FEW_SHOT = FEW_SHOT.replace(subst, "{" + f'input{groupNum}' + "}", 1)
                    if len(input_text.strip()) <= 10:
                        error_flag = 1
                new_data["instruction"] = FEW_SHOT
                new_data["dict_input"] = input_obj
                new_data['ideal'] = "无缺陷" if _lst['artificialResult'] == 1 else "有缺陷"
                new_data['rule'] = _lst['ruleNumber']
                rule_ids.add(_lst['ruleNumber'])
                new_data['rule_name'] = _lst['ruleName']
                new_data['customer_id'] = _lst['customerId']
                new_data['record_id'] = _lst['recordId']
                new_data['qc_label_remark'] = _lst['artificialRemark']
                if error_flag == 1:
                    error_lst.append(new_data)
                    continue

                # TODO group_id
                if _lst['ruleNumber'] in group_rules:
                    group_lst.append(new_data)
                    continue
                new_data['group_id'] = len(single_lst) + 1
                single_lst.append(new_data)
    # print(new_lst)
    print(rule_ids)


    def datetime_to_string(obj):
        if isinstance(obj, datetime.datetime):
            return obj.isoformat()
        return obj


    pd.DataFrame(group_lst + single_lst).to_excel("苏大附一_data.xlsx")
    # with open("qc_rule_浙儿_0704.jsonl", "w", encoding="utf-8") as jsonl_file:
    #     for item in single_lst:
    #         json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
    #         jsonl_file.write("\n")
    #
    # with open("qc_rule_浙儿_0704_error.jsonl", "w", encoding="utf-8") as jsonl_file:
    #     for item in error_lst:
    #         json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
    #         jsonl_file.write("\n")
