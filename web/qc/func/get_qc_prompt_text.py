from web.qc.qc_v2 import qc_v2
import pandas as pd
import re
import json
import requests
from tqdm import tqdm

'''
SELECT t1.record_id, t1.answer_type ruleNumber,prompt
FROM (
    SELECT record_id, answer_type,prompt,
           @row_num := IF(@current_type = answer_type, @row_num + 1, 1) AS row_num,
           @current_type := answer_type
    FROM hm_llm.llm_qc
    CROSS JOIN (SELECT @row_num := 0, @current_type := '') AS vars
    WHERE answer_type IN ('nh414', 'pfl3', 'nh190', 'nh155', 'nh296')
    ORDER BY answer_type, RAND()
) AS t1
WHERE t1.row_num <= 200;
'''


def tuomin_post(content):
    url = "http://172.16.2.218:9668/predict"
    try:
        payload = json.dumps({
            "content": content,
            "type": "auto",
            "dataBase": "hmcdss2_push_1220_20221124",
            "recordId": 9421080,
            "progressId": 11442800,
            "customerId": 1220
        })
        headers = {
            'Content-Type': 'application/json'
        }

        response = requests.request("POST", url, headers=headers, data=payload)

        return json.loads(response.text)["body"]["mask_text"]
    except:
        print("error！", content)
        return ""


if __name__ == '__main__':
    # 477961
    qc_v2 = qc_v2()
    # path = "/Users/<USER>/tmp/大模型评测数据/27项.xlsx"
    paths = [
        "/Users/<USER>/tmp/prompt_zheer711.xlsx"
        # "/Users/<USER>/tmp/prompt_sdfy0710.xlsx"
        # "/Users/<USER>/tmp/promt_fd710.xlsx"
    ]
    save_path = 'zheer_{}_0718.xlsx'
    # 多次调用的规则
    rule_lst = {}
    rule_ids = set()
    idx = 0
    for path in paths:
        lst = pd.read_excel(path).fillna("").to_dict("records")
        for _lst in tqdm(lst):
            if "ruleNumber" in _lst:
                ruleNumber = _lst['ruleNumber']
            else:
                ruleNumber = _lst['answer_type']
            if ruleNumber not in ["nh100","pf9","nh98","nh99","nh120","nh414","nh121"]:
                # nh120 nh98 nh99 nh100
                continue

            # if ruleNumber not in ["nh190","nh6","nh182"]:
            #     continue
            prompt = _lst['prompt'].replace("\n", "\\n").replace(" ", "").replace("***", "")
            if ruleNumber not in qc_v2.rule_flow or qc_v2.rule_flow[ruleNumber].config is None:
                continue
            if ruleNumber in ['nh190']:
                FEW_SHOT = qc_v2.rule_flow[ruleNumber].config["ZERO_SHOT"].strip()
            else:
                FEW_SHOT = qc_v2.rule_flow[ruleNumber].config["FEW_SHOT"].strip()

            regex_sub = r"{.*?}"
            subst = "(.*)"
            replaceFEW_SHOT = FEW_SHOT.replace("\n", "\\\\n").replace("[", "\[").replace("]", "\]").replace("/",
                                                                                                            "\/").replace(
                "-", "\-").replace("(", "\(").replace(")", "\)").replace("***", "").replace(".", "\.").replace("*",
                                                                                                               "\*").replace(
                "+", "\+")
            regex_find = re.sub(regex_sub, subst, replaceFEW_SHOT, 0, re.MULTILINE)
            try:
                # if ruleNumber=="pf3":
                #     continue
                matches = re.search(regex_find, prompt)
                FEW_SHOT = re.sub(regex_sub, subst, FEW_SHOT, 0, re.MULTILINE)
            except:
                print(f"error{ruleNumber}")
                continue

            if matches:
                new_data = {}
                input_texts = []
                input_json = {}
                error_flag = 0
                for groupNum in range(0, len(matches.groups())):
                    groupNum = groupNum + 1
                    input_text = matches.group(groupNum)
                    input_texts.append(input_text)
                    FEW_SHOT = FEW_SHOT.replace(subst, "{" + f'input' + "}", 1)

                new_data["instruction"] = FEW_SHOT
                input_texts = [tuomin_post(_text) for _text in input_texts]
                if ruleNumber == "nh190":
                    new_data["input"] = '[内容-现病史]\n{}\n[内容-现病史-结束]\n[内容-主诉]\n{}\n[内容-主诉-结束]'.format(input_texts[0],
                                                                                                    input_texts[1])
                elif ruleNumber == "nh6":
                    new_data["input"] = '[内容-入院记录]\n{}\n[内容-入院记录-结束]\n[内容-入院诊断]\n{}\n[内容-入院诊断-结束]'.format(
                        input_texts[0],
                        input_texts[1])
                elif ruleNumber == "nh182":
                    new_data["input"] = '[内容-主诉]\n{}\n[内容-主诉-结束]\n[内容-诊断]\n{}\n[内容-诊断-结束]'.format(
                        input_texts[0],
                        input_texts[1])
                else:
                    new_data["input"] = "\n".join(input_texts)
                for i, data in enumerate(input_texts):
                    input_json[f"input{i + 1}"] = data
                new_data["data"] = json.dumps(input_json, indent=1, ensure_ascii=False)
                new_data['ruleNumber'] = ruleNumber
                if ruleNumber not in rule_lst:
                    rule_lst[ruleNumber] = []
                rule_lst[ruleNumber].append(new_data)
                idx += 1
            if idx % 100 == 0:
                for ruleNumber, lst in rule_lst.items():
                    pd.DataFrame(lst).to_excel(save_path.format(ruleNumber), index=False)
    for ruleNumber, lst in rule_lst.items():
        pd.DataFrame(lst).to_excel(save_path.format(ruleNumber), index=False)

    # with open("qc_rule_浙儿_0704.jsonl", "w", encoding="utf-8") as jsonl_file:
    #     for item in single_lst:
    #         json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
    #         jsonl_file.write("\n")
    #
    # with open("qc_rule_浙儿_0704_error.jsonl", "w", encoding="utf-8") as jsonl_file:
    #     for item in error_lst:
    #         json.dump(item, jsonl_file, ensure_ascii=False, default=datetime_to_string)
    #         jsonl_file.write("\n")
