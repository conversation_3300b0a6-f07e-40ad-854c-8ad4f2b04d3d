# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh155Flow(QcFlow):
    def __init__(self):
        # 病程记录中无患者病情变化描述
        self.rule_id = "nh155"
        super(Nh155Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        data["nh155输入内容"] = f'{records}'
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()
        if not result.startswith("是的") and (
                "未直接提及" in result or "没有提到" in result or "没有提及" in result or "没有直接提及" in result):
            answer = "失败"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["nh155输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "上级医师查记录", "病程记录", "日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
        }


if __name__ == '__main__':
    pf = Nh155Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
            "customer_id": 1001,
            "record_id": 68922,
            "rule_id": "nh155",
            "data": [
                {
                    "record_type_name": "上级医师查记录",
                    "attribute_name": "",
                    "progressId": 92849,
                    "content": "查房记录: 白细胞计数(WBC): 3.1 x 109/L (降低)，红细胞计数(RBC): 3.9 x 1012/L (正常范围低值)，血红蛋白(HGB): 105 g/L (降低)，红细胞压积(HCT): 0.31 L/L (降低)，平均红细胞体积(MCV): 79.5 fL (正常)，平均红细胞血红蛋白含量(MCH): 26.9 pg (正常)，平均红细胞血红蛋白浓度(MCHC): 338 g/L (正常)，红细胞分布宽度(RDW-CV): 15.2% (升高)，血小板计数(PLT): 158 x 109/L (正常范围低值),结合患儿当前情况，已予以液体补给治疗,静脉滴注生理盐水维持水电解质平衡,并补充人蛋白液预防或纠正低蛋白血症。将密切监测患儿的一般状况,继续评估治疗反应,适时复查相关检查指标。\n医师签名:",
                    "targetKeys": []
                }
            ]
        }]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
