# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh413Flow(QcFlow):
    def __init__(self):
        # 住院患者应用抗菌药物后，病程记录中缺少对应药品名称
        self.rule_id = "nh413"
        super(Nh413Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        medical_order = data['医嘱内容']

        data["nh413输入内容"] = (f'[内容-医嘱名称]\n{medical_order}\n[内容-医嘱名称-结束]\n'
                                 f'[内容-病程记录]\n{records}\n[内容-病程记录-结束]\n')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip().replace("有没有", "是否").replace("有无", "是否")
        if "未提及" in result or "没有提及" in result:
            answer = "失败"
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        input = data["nh413输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        if self.is_hospital == '0':  # 公司环境
            result, answer = self.get_answer_promt(data)
        else:
            result, answer = self.super_get_answer_promt(data)

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录",
                                             "日常病程", '首次病程记录']:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '医嘱内容': "\n".join(new_data.get('医嘱内容', [])),
        }


if __name__ == '__main__':
    pf = Nh413Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{"customer_id": 1807, "record_id": 5804044, "rule_id": "nh413", "version": "v2", "data": [
            {"record_type_name": "住院医嘱", "attribute_name": "", "progressId": 338009610,
             "医嘱内容": "注射用哌拉西林钠他唑巴坦钠[J][4.5g(4.0g/0.5g)] 4.5g q8h 静脉滴注",
             "targetKeys": ["医嘱内容"]}, {"record_type_name": "查房记录", "attribute_name": "", "progressId": 50556582,
                                           "content": "Header:页眉: 接收时间：:2024年08月21日16:13 危急值内容：:1、项目名称：高敏肌钙蛋白T*结果：1877.000(ng/L)参考范围：当结果介于14~100ng/L时，请间隔3、;6、9、12h连续监测，若结果持续升高;>20%提示AMI可能性大。 病人情况：:机械通气 分析评估：:考虑术后改变 :联系并报告总住院医师韦春楼后，予密切观察患者病情，治疗后复查。效果：待复查 签名：:杨华锋签名时间：2024年08月21日",
                                           "targetKeys": []}]}]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
