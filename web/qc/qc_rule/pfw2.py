# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw2Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pfw2"
        super(Pfw2Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        medical_order = data['医嘱内容']

        data["pfw2输入内容"] = (f'[内容-医嘱名称]\n'
                           f'{medical_order}\n'
                           f'[内容-医嘱名称-结束]\n'
                           f'[内容-病程记录]\n'
                           f'{records}\n'
                           f'[内容-病程记录-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()
        pattern = r"(?:step[23]).*?(是|否)"
        # 遍历句子并提取错误和正确的词汇
        outputs = result.replace("\n", "").replace("是/否", "")
        matches = re.findall(pattern, outputs)
        for _match in matches:
            if _match == "否":
                answer = "失败"
            break
        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw2输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录","首次查房记录","术后首次病程","上级医师查房记录","病程记录","日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '医嘱内容': "\n".join(new_data.get('医嘱内容', [])),
        }


if __name__ == '__main__':
    pf = Pfw2Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
            "request": [
                {
                    "customer_id": 1801,
                    "record_id": 3455569,
                    "rule_id": "pfw2",
                    "data": [
                        {
                            "record_type_name": "住院医嘱",
                            "progressId": 4180472,
                            "targetKeys": ["医嘱内容"],
                            "医嘱内容": "曲妥珠单抗（赫赛汀） 2019-01-03 09:21:00"
                        },
                        {
                            "record_type_name": "查房记录",
                            "attribute_name": "",
                            "progressId": 2616259,
                            "content": "副主任医师代主治医师查房。    患者入院后食欲可，无恶心、呕吐，无肢端麻木，无发热，无腹泻，未诉其他不适，相关检查回报未见明显禁忌症，张雁副主任医师查房：患者今日予泰素帝160mg化疗+赫塞汀124mg靶向治疗，注意不良反应，对症处理。无特不适予以今日出院。。  "
                        }
                    ]
                }
            ]
        }]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
