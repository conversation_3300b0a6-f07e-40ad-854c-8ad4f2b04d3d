# -*- coding: utf-8 -*-

import sys
import os

# 获取当前脚本所在目录的绝对路径
script_dir = os.path.dirname(os.path.abspath(__file__))

# 将模块所在目录的上级目录添加到sys.path
module_dir = os.path.join(script_dir, '../../../')
sys.path.append(module_dir)

from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class NhTemplateFlow(QcFlow):
    IS_DB_CACHE = False

    def __init__(self):
        # 入院诊断在入院记录中无相应的诊断依据
        self.rule_id = "nh_x"
        super(NhTemplateFlow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_disease(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        reg_cleaned_disease = self.config["REG_CLEANED_DISEASE"]
        reg_exa_disease = self.config["REG_EXA_DISEASE"]
        reg_filter_pattern = self.config["REG_FILTER_PATTERN"]
        disease_str = ""
        # 数据清洗
        cleaned_text = re.sub(reg_cleaned_disease, "", org_disease, 0, re.MULTILINE)
        if "诊断" not in cleaned_text:
            cleaned_text = "诊断" + cleaned_text
        # 诊断提取
        match = re.search(reg_exa_disease, cleaned_text)
        if match:
            _disease = match.group(1).strip()
            # 拆分 1. 生物制剂治疗\t 2. 克罗恩病(a1a,l3,b1,g0)  -> 1. 生物制剂治疗  取第一个disease_str
            disease_array = set(
                [p for p in re.split(r"(?![^(]*?\))(?:\s|\t|\d\.\s*|\d、\s*|入院诊断)", _disease) if
                 p.strip() and len(p.strip()) > 1 and
                 not re.fullmatch(reg_filter_pattern, p.strip())])
            if len(disease_array) == 0:
                disease_str = ""
            else:
                numbered_diseases = []
                for i, d in enumerate(sorted(disease_array)):
                    numbered_diseases.append(f"{i + 1}.{d}")
                disease_str = " ".join(numbered_diseases)
        data["初步诊断"] = disease_str.strip()
        data["入院记录"] = org_record.strip()

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_rule_answer(self, data):
        answer = "成功"
        disease_str = data["初步诊断"]
        org_main = data["入院记录"]
        input = data["input"]
        question = self.config["QUESTION"].format(org_main, disease_str)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")
        result = data["result"]
        res = []
        outputs = result.split("\n")
        for _sen in outputs:
            _sen = _sen.strip()
            if len(_sen) == 0:
                continue
            if "无关联" in _sen and "虽然" not in _sen:
                answer = "失败"
                res.append(_sen)
        if len(res) > 0:
            result = "\n".join(res)
            data["result"] = result

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            if data["record_type_name"] == "入院记录" and data["attribute_name"] in ("初步诊断"):
                return f"{data['attribute_name']}"
            elif data["record_type_name"] == "入院记录":
                return f"{data['record_type_name']}"

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]

        for _data in data["data"]:
            key = make_key(_data)
            if key:
                new_data.setdefault(key, []).append(_data["content"].replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            # 若有多条初步诊断、主诉，只取第一条
            "初步诊断": new_data.get("初步诊断", [])[0],
            "入院记录": "\n".join(new_data.get("入院记录", []))
        }


import pandas as pd

if __name__ == '__main__':
    qc = NhTemplateFlow()
    paths = [
        # "nh134-gpt_入院记录-现病史中，已完成的检查项目未在辅助检查中列出.xlsx","nh88-gpt_入院诊断与出院诊断不一致时，需要书写更正诊断.xlsx",
        #      "nh154-(过滤)首份上级医师查房记录中关键内容遗漏.xlsx","pfw1_使用激素类药物后，需要记录到病程记录中.xlsx",
        #      "nh155-gpt_病程记录中无患者病情变化描述.xlsx","pfw2_使用靶向药物后，需要记录到病程记录中，并记录治疗后的患者病情变化.xlsx",
        #      "nh18-gpt_RIS报告中提示的疾病未加入病案首页诊断中.xlsx","pfw3_输白蛋白需要记录到病程记录中.xlsx",
        #      "nh258-gpt_住院期间新下达的诊断名称未在病程记录中进行体现.xlsx","pfw5_激素治疗剂量更改时，需记录到病程记录中.xlsx",
        #      "nh54-gpt_入院记录月经史内容记录不完整.xlsx"
        "pf1_1查房记录中对重要检查结果进行分析{}.xlsx"
    ]
    for path in paths:
        print(f"path:{path}")
        lst = pd.read_excel(path.format("")).to_dict("records")

        new_data = []
        for _data in tqdm(lst):
            _data["record_id"] = 1001
            _data["customer_id"] = 1001
            _data["text"] = _data["input"]
            _data["input"] = _data["instruction"].replace("{input}", _data["input"])

            res = qc.work_flow([_data])[0]

            _data["result"] = res["result"]
            # _data["answer"] = res["answer"]
            new_data.append(_data)
            if len(new_data) % 100 == 99:
                pd.DataFrame(new_data).to_excel(path.replace(".xlsx", "v1_save.xlsx"))
        pd.DataFrame(new_data).to_excel(path.replace(".xlsx", "v1_save.xlsx"))
