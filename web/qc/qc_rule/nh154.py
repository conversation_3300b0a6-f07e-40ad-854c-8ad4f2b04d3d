# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh154Flow(QcFlow):
    def __init__(self):
        # 首份上级医师查房记录中关键内容遗漏
        self.rule_id = "nh154"
        super(Nh154Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        records = data["病程记录"]

        data["规则输入内容"] = (f'[内容-病程记录]\n'
                          f'{records}\n'
                          f'[内容-病程记录-结束]\n'
                          )

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        # 最后输出有缺陷或者无缺陷
        result = data["result"].strip()
        answer = "成功"
        res = []

        # if "有遗漏内容" in match_str:
        result_list = result.split("\n")
        for _result in result_list:
            if "否" in _result:
                answer = "失败"
                res.append(_result.replace("回答1", "病历内容中未记录患者病情的相关描述"). \
                           replace("回答2", "病历内容中未记录诊断内容"). \
                           replace("回答3", "病历内容中未记录鉴别诊断相关描述"). \
                           replace("回答4", "病历内容中未记录后续诊疗计划的相关描述").replace("否", "").replace(":", "").replace("：",
                                                                                                           "").strip())
                result = "\n".join(res)

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["规则输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")


        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录"]:
                return [(f"病程记录", _data["content"])]
            else:
                return []

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0]
        }


if __name__ == '__main__':
    nh = Nh154Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs =[
            {
                "customer_id": 1001,
                "record_id": 111144,
                "rule_id": "nh154",
                "data": [
                    {
                        "record_type_name": "上级医师查房记录",
                        "attribute_name": "",
                        "progressId": 9761280,
                        "content": "查房记录： 今查房，患儿入观后体温正常，仍阵发性咳嗽咳痰，无头晕头痛，无气促气喘，无呕吐腹泻，无皮疹等不适，纳眠可，二便无殊。查体：T：36.7℃，HR：100次/分，RR：28次/分，SPO2：98%，神清，精神可，咽红，双侧扁桃体I度肿大，心律齐，心音中，无杂音，双肺呼吸音粗，闻及少许湿啰音，腹软，无包块，无压痛，肝脾肋下未及，全身无皮疹，NS-。辅检：暂无更新。今盛远见主任医师查房示：目前诊断：1.急性肺炎；2.流行性感冒。诊断依据：1-2：患儿有发热咳嗽症状，查体：双肺呼吸音粗、可闻及湿罗音，甲流抗原弱阳性，结合胸部CT所见，故诊断、治疗上，入观后予留观室常规护理，生命体征监测，对症治疗等。现患儿因病情需要，予转住院进一步诊治。 医师签名"
                    }
                ]
            }
    ]
        res = nh.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
