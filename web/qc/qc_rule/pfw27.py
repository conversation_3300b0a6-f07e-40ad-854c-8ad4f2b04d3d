# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw27Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pfw27"
        super(Pfw27Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        consult_order = data['会诊记录']

        data["pfw27输入内容"] = (f'[内容-会诊记录]\n'
                           f'{consult_order}\n'
                           f'[内容-会诊记录-结束]\n'
                           f'[内容-病程记录]\n'
                           f'{records}\n'
                           f'[内容-病程记录-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        outputs = result.split("\n")
        if len(outputs) > 0:
            res = outputs[-1]
            if "病程记录未体现诊疗措施" in res:
                answer = "失败"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw27输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录","首次查房记录","术后首次病程","上级医师查房记录","病程记录","日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "会诊记录":
                return [(f"会诊记录", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '会诊记录': "\n".join(new_data.get('会诊记录', [])),
        }


if __name__ == '__main__':
    pf = Pfw27Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
                {
                    "customer_id": 1801,
                    "record_id": 3455569,
                    "rule_id": "pfw27",
                    "data": [
                        {
                            "record_type_name": "会诊记录",
                            "attribute_name": "",
                            "progressId": 2616259,
                            "content": "病史悉。不再述。患者今日至我科门诊查电测听示双导抗C型，双耳重度感音神经性耳聋。诊断：分泌性中耳炎；感音神经性耳聋。处理：1.同意贵科目前口服切诺对症治疗。2.如无明显禁忌，可加用达芬霖鼻喷雾剂局部应用。弥可保1片，po，tid。金纳多1片，po，tid。3.我科随访，谢邀！"
                        },
                        {
                            "record_type_name": "查房记录",
                            "attribute_name": "",
                            "progressId": 2616259,
                            "content": "今**  主治医师查房，患者一般情况可，会阴部有片状红斑丘疹。体温平，无特殊不适主诉。查体：心肺无殊。下肢无水肿。遵皮肤科建议予氯地松、美克交替外涂会阴部瘙痒处bid。继续目前降糖、降压、碱化尿液等治疗方案，定期随访血糖水平，目前继续床旁康复治疗，继观。"
                        }
                    ]
                }
            ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
