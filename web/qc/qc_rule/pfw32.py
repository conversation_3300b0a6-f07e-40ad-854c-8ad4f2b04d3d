# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Pfw32Flow(QcFlow):
    def __init__(self):
        # 病程记录中抗生素类药物使用记录不能过于笼统，需要记录具体名称
        self.rule_id = "pfw32"
        super(Pfw32Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        records = data["病程记录"]

        data["pwf32输入内容"] = (f'[内容-病程记录]\n'
                             f'{records}\n'
                             f'[内容-病程记录-结束]'
                             )

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self,data):
        answer = "成功"
        result = data['result']
        outputs = result.replace("是否", "")
        matches = re.findall(r'(?:step\s*1).*?(是|否)', outputs.lower())
        for _match in matches:
            if _match == "否":
                return result, answer

        outputs = result.split("\n")

        if "鉴别诊断" not in result and len(outputs) > 0:
            res = outputs[-1]
            if "未提及使用抗生素药物治疗" in res:
                answer = "成功"
            elif "没有记录具体" in res or "没有明确记录具体" in res:
                answer = "失败"
            result = res

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pwf32输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            # 病程记录
            if data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录",
                                            "日常病程"]:
                return [(f"病程记录", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0]
        }


if __name__ == '__main__':
    pf = Pfw32Flow()

    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
        {
            "customer_id": 1001,
            "record_id": 69044,
            "rule_id": "pfw32",
            "data": [
                {
                    "record_type_name": "日常病程",
                    "attribute_name": "",
                    "progressId": 94676,
                    "content": "2023-12-22 12:35:23 日常病程记录\n录: 患者诉发热，咳少量白痰，未诉其他明显不适，病情可。化疗同时予止吐、护胃及碱化尿液等治疗。化疗过程顺利，无不适反应。患者偶有咳嗽，伴咳白色粘痰，今日已行胸部CT检查，阅片提示不排除肺部感染可能，处理:考虑患者发热为无菌性感染可能大，使用抗菌治疗，继续予抑酸、营养支持等疗。\n医师签名:",
                    "targetKeys": []
                }
            ]
        }
    ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
