# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh296Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "nh296"
        super(Nh296Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        data["nh296输入内容"] = (f'[内容-术前讨论]\n'
                             f'{records}\n'
                             f'[内容-术前讨论-结束]')
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()
        outputs = result.split("\n")
        result = result.strip()
        if len(outputs) > 0:
            res = outputs[-1].replace("\n", "\\n").replace("答案是", "答案").replace("回答是", "回答")
            if "是" == res:
                answer = "失败"
            pattern = r"(?:答案|回答|step3：).*?(是|否)"
            results = re.findall(pattern, res)
            for _result in results:
                if "是" in _result:
                    answer = "失败"
            if result.endswith("是"):
                result = result[:-1]

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["nh296输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def proccess_data(self, content):
        content = re.sub("主治医生发言内容:.*?会诊医生发言内容:", "", content)
        return content

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["术前讨论"]:
                content = self.proccess_data(_data["content"])
                return [(f"病程记录", content)]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
        }


import pandas as pd

if __name__ == '__main__':
    import re

    pf = Nh296Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1001,
                "record_id": 69000,
                "rule_id": "nh296",
                "data": [
                    {
                        "record_type_name": "术前讨论",
                        "attribute_name": "主持人小结",
                        "progressId": 94440,
                        "content": "参加人员: 参加讨论人员及称:彭俊杰 李艺伟 陈奕宽 \\n主持人小结:主持人:蔡三军主持人小结:拟明日联合麻醉下行腹会阴直肠联合切除术治疗手术意外或并发症、合并症处理预案 :1.完善相关检查,排除手术及麻醉禁忌。做好充分术前准备,包括备皮,备血,肠道准备等。2.术前需向患者及家属详细沟通,告知患者病情,治疗方式,术风险,预期疗效,手术风险及可能的并发症。术中可能肛门改道。腹腔镜手术中转开放可能。3.手术操作谨慎,出血多,必要时输血,脏器损伤,给予及时修补。4.加强围手术期护理,密切监测术后引流量色,手术切口情况及腹部体征,生命体征变化\t 主持人:蔡三军\t 主治医生发言内容:- 会诊医生发言内容:- - ",
                        "targetKeys": []
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
