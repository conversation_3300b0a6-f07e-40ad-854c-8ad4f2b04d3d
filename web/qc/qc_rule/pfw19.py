# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw19Flow(QcFlow):
    def __init__(self):
        # 有输血治疗的患者，上级医生查房记录中应有对应的输血诊疗计划或记录
        self.rule_id = "pfw19"
        super(Pfw19Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        blood_order = data['输血记录']

        data["pfw19输入内容"] = (f'[内容-输血记录]\n'
                           f'{blood_order}\n'
                           f'[内容-输血记录-结束]\n'
                           f'[内容-病程记录]\n'
                           f'{records}\n'
                           f'[内容-病程记录-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()
        outputs = result.split("\n")
        if len(outputs) > 0:
            res = outputs[-1]
            if "有输血但病程中无" in res:
                answer = "失败"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw19输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录","首次查房记录","术后首次病程","上级医师查房记录","病程记录","日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "输血记录":
                return [(f"输血记录", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '输血记录': "\n".join(new_data.get('输血记录', [])),
        }


if __name__ == '__main__':
    pf = Pfw19Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
                "customer_id": 1801,
                "record_id": 3455569,
                "rule_id": "pfw19",
                "data": [
                    {
                        "record_type_name": "输血记录",
                        "attribute_name": "",
                        "progressId": 2616259,
                        "content": "姓名：**性别：男年龄：64岁病案号：1345087血型：B+输血前评估(指征)：患者近日检查指示：凝血功能异常, 有出血倾向输血目的：为了纠正凝血功能输血前检查HGB：79.000PT：20.300PLT：45.000输血成分：病毒灭活冰冻血浆100ml 100.0000ml领血时间：2021-05-02T09:52:46.733当前病区：（东院）肝外监护室输血开始时间：输血结束时间：输血反应：无输血反应输血后检查(48小时后)HGB：93.000PT：20.900PLT：55.000输血后评估(疗效)：继续观察。"
                    },
                    {
                        "record_type_name": "查房记录",
                        "attribute_name": "",
                        "progressId": 2616259,
                        "content": "今**主任医师查房 ，患者神志清 。目前面罩吸氧中，5L/min。今晨体温：36.2度。即时心电监护示HR：77bpm、SpO2：100%、R：16bpm、BP：104/47mmHg。查体：双肺呼吸音清 ，未闻及 干湿啰音，腹软，无压痛反跳痛，肠鸣音4 次/分，四肢活动可，双下肢不肿。昨24h入水量2988 ml，脱水2540ml，右胸引流 240ml，左侧腹穿 1030ml，胆管引流240ml，大便40ml 。王婷主任了解病史并查看患者，　 解决主要矛盾的途径、措施和方法：１．监测生命体征、血糖、尿量，完善各项实验室检查２．维持水、酸碱、电解质平衡，计算２４小时出入液量，维持血糖平稳３．加强雾化吸入，积极预防肺部感染，适当使用支气管解痉药物４．完善镇痛，解除患者焦虑５．积极预防下肢深静脉血栓６．积极营养支持。"
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
