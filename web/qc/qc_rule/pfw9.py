# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw9Flow(QcFlow):
    def __init__(self):
        # 病程记录中如果有发热症状，应记录具体体温
        self.rule_id = "pfw9"
        super(Pfw9Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        data["pfw9输入内容"] = (f'[内容-病程记录]\n'
                                f'{records}\n'
                                f'[内容-病程记录-结束]')
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"
        input = data["input"]
        result = data["result"].strip()
        if result.startswith("是的") and "无发热" not in result and "没有发热" not in result:
            pattern = r"\d+\s*\.?\s*\d*\s*(?:℃|°|度|摄氏度)"
            temperatures = re.findall(pattern, input)
            if len(temperatures) == 0:
                answer = "失败"
                result +="但未记录具体体温"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw9输入内容"]
        data["input"] = input
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录","首次查房记录","术后首次病程","上级医师查房记录","病程记录","日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
        }


if __name__ == '__main__':
    pf = Pfw9Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
    {
      "customer_id": 1001,
      "record_id": 53886,
      "rule_id": "pfw9",
      "data": [
        {
          "record_type_name": "上级医师查房记录",
          "attribute_name": "",
          "progressId": 92207,
          "content": "XX省第XX人民医院上级医师查 记录姓名:张心敬 科室:- 床号:- 住院号:S21W50M47393     \n2023-02-22 11:21 副主任医师查房记录今日刘文超副主任医师查房，患者无气喘加重，无咳嗽、咳痰加重，无发热，查体：神清，精神差，呼吸平稳，皮肤粘膜无黄染，两肺呼吸音粗，肺部可闻及少许干湿性啰音，心率74次/分，律齐，无杂， 腹平软，全腹部压痛阴性，反跳痛阴性,未见肠型及蠕动波,肝脾肋下未及肿大，Murphy征阴性，移动性浊音阴性，肠鸣音正常，约3-4次/分，双下肢无水肿，四肢肌力肌张力正常，NS(-)。2023-02-20 血常规（五分类） :红细胞 3.45*1012/L,血红蛋白 103g/L,红细胞压积 32.9%,红细胞平均血红蛋白度 312.0g/L,红细胞分布宽度 56.5fl,红细胞分布宽度-CV值16.7%,白细胞 7.48*109/L,中性粒细胞比率 57.9%,2023-02-20 肝肾功能:谷草转氨酶 56.3U/L,总蛋白 57.2g/L,白蛋白 23.3g/L,白球比 0.7,总胆汁酸 14.3μmol/L,甘胆酸 4.99μg/ml,前白蛋白 92.2mg/L,肌酐 50.5μmol/L,尿酸 143.27μmol/L,抑素C 1.87mg/L,视黄醇结合蛋白 21.87μg/ml,2023-02-20 电解质8项检测:钾 3.23mmol/L,钠 135.00mmol/L,氯 94.00mmol/L,钙 1. \n\n88mmol/L,铁 9.07μmol/L,磷 0.59mmol/L,二氧化碳 38.9mmol/L,2023-02-20 C反应蛋白定量(CRP):C-反应蛋白 46.34mg/L,刘文超副主任医师查房，患者血钾偏低，今给予补钾治疗。/ \n\n\n 医师签名:x  ",
          "targetKeys": []
        }
      ]
    }
  ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
