# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Nh10Flow(QcFlow):
    def __init__(self):
        # 首次病程鉴别诊断无有意义内容
        self.rule_id = "nh10"
        super(Nh10Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        records = data["病历特点"]
        disease_name = data['初步诊断']
        diagnosis_name = data["鉴别诊断"]

        data["nh10输入内容"] = (f'[内容-病例特点/诊断]\n'
                            f'{records}\n'
                            f'初步诊断：{disease_name}\n'
                            f'[内容-病例特点/诊断-结束]\n'
                            f'[内容-鉴别诊断]\n'
                            f'鉴别诊断: {diagnosis_name}\n'
                            f'[内容-鉴别诊断-结束]'
                            )

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    # 统一使用该方法做后处理
    def get_answer_promt(self, data):
        answer = "失败"
        result = data['result']
        outputs = result.replace("\n", "\\n").replace("回答是", "回答为").replace("答案是", "答案")
        pattern = r"(step3.*)"
        results = re.findall(pattern, outputs)
        if len(results) > 0:
            result = results[0]
            pattern = r"(step3.*?(?:回答|答案).{0,4}是)|(step3[：:]?是)"
            res = re.findall(pattern, result)
            if len(res) > 0 or "每个诊断都有明确" in outputs or "回答是。" in outputs:
                answer = "成功"
            result = result.replace("\n", "").replace("\\n", "")
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        input = data["nh10输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            # 首次病程记录
            if data["record_type_name"] in ["首次病程记录"]:
                return [(_data['attribute_name'], _data["content"])]
            #

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病历特点': new_data.get('病历特点', [])[0],
            '初步诊断': new_data.get('初步诊断', [])[0],
            '鉴别诊断': new_data.get('鉴别诊断', [])[0]
        }


if __name__ == '__main__':
    pf = Nh10Flow()

    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1801,
                "record_id": 3455569,
                "rule_id": "nh10",
                "data": [
                    {
                        "record_type_name": "首次病程记录",
                        "attribute_name": "病历特点",
                        "progressId": 2616259,
                        "content": "患者于2019年3月无明显诱因出现大便次数增加,约5-6次/日,为黄褐色成形便,无粘液、无便血,无腹痛、腹胀,无恶心、呕吐,无头晕、乏力。入院完善相关检查,患者“直肠癌”诊断明确,未见明显手术禁忌,于2019-08-01于全麻下行经腹腔镜直肠切除术,术程顺利,术后予以预防感染、营养支持等治疗。现患者为行术后第7次化疗入院。自上次出院以来,病人精神状态一般,体力情况一般,食欲食量一般,睡眠情况一般,体重无明显变化,大便正常,小便正常。 2、 入院查体: 体温:36.4°C, 脉搏: 99次/分, 呼吸:20次/分,血压: 139/78mmHg。 腹部腹平坦,无腹壁静脉曲张,腹部柔软,无压痛、反跳痛,腹部无包块。肝脏肋下未触及,脾脏肋下未触及,Murphy氏征阴性,肾区无叩击痛,无移动性浊音。肠鸣音未见异常,4次/分。 3、辅助检查:2019-07-30我院结肠镜:(送达盲肠,回盲瓣呈唇状,粘膜光滑,未见糜烂、溃疡、肿物。距肛门8m-14m可见溃疡型肿物,环1/2周,覆污秽苔,触之易出血,于病变口侧、肛侧正常粘膜处行纳米碳标记,过程顺利,余所见大肠粘膜光滑, 血管纹理清晰, 未见明显异常。)直肠Ca,纳米碳标记术。 "
                    },
                    {
                        "record_type_name": "首次病程记录",
                        "attribute_name": "初步诊断",
                        "progressId": 2616259,
                        "content": "初步诊断:1.手术后恶性肿瘤化学治疗;2.直肠恶性肿瘤(pT4aN2M0)。"
                    },
                    {
                        "record_type_name": "首次病程记录",
                        "attribute_name": "鉴别诊断",
                        "progressId": 2616259,
                        "content": "鉴别诊断:诊断明确,无需鉴别"
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
