# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Syqt42Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "syqt42"
        super(Syqt42Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        exam_name = data["项目代码名称"]
        exam_result = data['检查结论']
        disease_name = data['诊断名称']

        data["syqt42输入内容"] = (f'项目代码名称:{exam_name}\n'
                              f'检查结论:{exam_result}\n'
                              f'病案诊断:{disease_name}')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self,data):
        answer = "成功"
        result = data["result"].strip()
        pattern = r"(?:指令4).*?(无缺陷|有缺陷)"
        # 遍历句子并提取错误和正确的词汇
        outputs = result.replace("\n", "")
        matches = re.findall(pattern, outputs)
        for _match in matches:
            if _match == "有缺陷":
                answer = "失败"
            break
        matches = re.findall(r"指令3[:|：](.*?)指令4", outputs)
        for _match in matches:
            result = _match
            break
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        input = data["syqt42输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["检查结果报告", "患者诊断信息"]:
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '项目代码名称': new_data.get('项目代码名称', [])[0],
            '检查结论': "\n".join(new_data.get('检查结论', [])),
            '诊断名称': "\n".join(new_data.get('诊断名称', [])),
        }


if __name__ == '__main__':
    pf = Syqt42Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
        {
            "customer_id": 1001,
            "record_id": 69038,
            "rule_id": "syqt42",
            "data": [
                {
                    "record_type_name": "检查结果报告",
                    "progressId": 6280,
                    "项目代码名称": "彩色多普勒超声常规检查B",
                    "检查结论": "脂肪肝（轻度） \\r\\n胆囊壁稍厚毛糙\\r\\n右下腹阑尾区未见明显肿大阑尾",
                    "targetKeys": [
                        "项目代码名称",
                        "检查结论"
                    ]
                },
                {
                    "record_type_name": "患者诊断信息",
                    "progressId": 136975,
                    "诊断名称": "高血压",
                    "targetKeys": [
                        "诊断名称"
                    ]
                }
            ]
        }
    ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
