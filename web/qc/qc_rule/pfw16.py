# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw16Flow(QcFlow):
    def __init__(self):
        # 首次病程中初步诊断需符合拟诊分析中的诊断描述
        self.rule_id = "pfw16"
        super(Pfw16Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        disease = data["初步诊断"]
        disease_detail = data['鉴别诊断']

        data["pfw16输入内容"] = (f'[内容-拟诊分析]\n'
                           f'{disease_detail}\n'
                           f'[内容-拟诊分析-结束]\n'
                           f'[内容-初步诊断]\n'
                           f'{disease}\n'
                           f'[内容-初步诊断-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        outputs = result.split("\n")
        if len(outputs) > 0:
            res = outputs[-1]
            if "没有无法对应的诊断" not in res:
                answer = "失败"
                result = res
        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw16输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] == "首次病程记录":
                if _data["attribute_name"] == "鉴别诊断":
                    return [(f"鉴别诊断", _data["content"])]
                elif _data["attribute_name"] == "初步诊断":
                    return [(f"初步诊断", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '鉴别诊断': "\n".join(new_data.get('鉴别诊断', [])),
            '初步诊断': "\n".join(new_data.get('初步诊断', [])),
        }


if __name__ == '__main__':
    pf = Pfw16Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
                    "customer_id": 1801,
                    "record_id": 3455569,
                    "rule_id": "pfw16",
                    "data": [
                        {
                            "record_type_name": "首次病程记录",
                            "attribute_name": "鉴别诊断",
                            "progressId": 2616259,
                            "content": "鉴别诊断:患者目前诊断颅内感染,拟进一步明确病原学证据,需鉴别以下疾病: 1、病毒性脑膜脑炎:患者多有感冒、腹泻等前驱感染史,可有头痛、意识模糊、精神症状及癫痫发作等症状,有神经系统局灶定位体征及脑膜刺激征,可出现脑脊液细胞数增多,头颅核磁可有颞叶等部位的异常信号。本病人需进一步行腰穿检查以确诊。 2、结核性脑膜炎:以发热、头痛为主要表现,时伴结核中毒症状,如盗汗、午后低热等,查体可见脑膜刺激征,脑脊液检查压力增高,白细胞多在数百,早期以分叶核为主,后期以单核为主,蛋白增高,氯化物降低。本病人外院胸部CT不排除继发性肺结核,需进一步完善腰穿及影像学检查。 3、化脓性脑膜脑炎:以发热、头痛为主要表现,查体可见脑膜刺激征,脑脊液检查压力增高,白细胞多在数千,以分叶核为主,蛋白增高,糖和氯化物降低。本病人需进一步完善腰穿鉴别。 4、隐球菌性脑膜脑炎:以发热、头痛、呕吐为主要表现,查体可见脑膜刺激征,脑脊液检查压力可明显增高,墨汁染色检菌有助于鉴别。本病人需进一步完善检查鉴别。"
                        },
                        {
                            "record_type_name": "首次病程记录",
                            "attribute_name": "初步诊断",
                            "progressId": 2616259,
                            "content": "初步诊断:1.颅内感染;2.肺部感染。"
                        }
                    ]
                }]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
