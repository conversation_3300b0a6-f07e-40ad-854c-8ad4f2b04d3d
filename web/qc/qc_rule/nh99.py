# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh99Flow(QcFlow):
    def __init__(self):
        # nh99	细菌培养结果需要在病程记录中有对应记录
        self.rule_id = "nh99"
        super(Nh99Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        examination = data["微生物检查结果"]
        record = data["病程记录"]

        data["nh99输入内容"] = (f'[内容-微生物检查结果]\n'
                                f'{examination}\n'
                                f'[内容-微生物检查结果-结束]\n'
                                f'[内容-病程记录]\n'
                                f'{record}\n'
                                f'[内容-病程记录-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        result = data["result"].strip()
        answer = "成功"
        if "未记录" in result:
            answer = "失败"
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        # disease_str = data["初步诊断"]
        # org_main = data["入院记录"]
        input = data["nh99输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")
        if self.is_hospital == '0':  # 公司环境
            result, answer = self.get_answer_promt(data)
        else:
            result, answer = self.super_get_answer_promt(data)

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            #
            if data["record_type_name"] in ["微生物检验结果-住院", "病理检查结果表-住院"]:
                return [(f"微生物检查结果", _data["细菌检查结果"])]
            elif _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录",
                                               "病程记录", "日常病程", '首次病程记录']:
                return [(f"病程记录", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '微生物检查结果': new_data.get('微生物检查结果', [])[0],
            '病程记录': new_data.get('病程记录', [])[0],
        }


if __name__ == '__main__':
    nh = Nh99Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1801,
                "record_id": 3455569,
                "rule_id": "nh99",
                "data": [
                    {
                        "record_type_name": "病理检查结果表-住院",
                        "attribute_name": "",
                        "progressId": 2616259,
                        "targetKeys": ["细菌检查结果"],
                        "细菌检查结果": "鲍曼不动杆菌: ++  铜绿假单胞菌: +++  嗜麦芽窄食单胞菌: +++  "
                    },
                    {
                        "record_type_name": "病程记录",
                        "attribute_name": "",
                        "progressId": 2616258,
                        "content": "2020-05-29 15:48:01 ***主治医师今日查房,记录如下:主治查房内容:患者今日术后第2天，一般情况可，晨体温36.9摄氏度，诉伤口疼痛，能耐受，较昨日缓解，余无明显不适主诉。查体：手术伤口清洁，无红肿，感染，双肺呼吸音尚清。昨日胸管引流量中等，淡红色，无混浊，胸瓶液面波动可，无漏气。纵隔引流球引流少量，色淡红。胃管通畅，引流多量，黄褐色。嘱患者注意咳痰，加强呼吸锻炼，患者术后复查血常规中性粒细胞偏高，复查胸片肺纹理增粗伴渗出，继续予以抗炎、止痛、化痰止咳、营养支持等对症治疗，密切观察患者生命体征及引流情况。"
                    }
                ]
            }
        ]
        res = nh.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
