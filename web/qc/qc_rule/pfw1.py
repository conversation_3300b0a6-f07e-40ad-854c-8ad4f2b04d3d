# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw1Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pfw1"
        super(Pfw1Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        medical_order = data['医嘱内容']

        data["pfw1输入内容"] = (f'[内容-医嘱名称]\n'
                           f'{medical_order}\n'
                           f'[内容-医嘱名称-结束]\n'
                           f'[内容-病程记录]\n'
                           f'{records}\n'
                           f'[内容-病程记录-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        pattern = r"(?:总结结果|step[34]|结果).*?(是|否)"
        # 遍历句子并提取错误和正确的词汇
        outputs = result.replace("\n", "").replace("是/否", "").replace("结果是", "结果").replace("是否", "")
        matches = re.findall(pattern, outputs)
        for _match in matches:
            if "否" in _match:
                answer = "失败"
            break
        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw1输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录","首次查房记录","术后首次病程","上级医师查房记录","病程记录","日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '医嘱内容': "\n".join(new_data.get('医嘱内容', [])),
        }


if __name__ == '__main__':
    pf = Pfw1Flow()
    pf.get_answer_promt({"result":""})
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    # lst = [{}]
    # new_data = []
    # for _data in tqdm(lst):
    #     reqs = [{
    #                 "customer_id": 1801,
    #                 "record_id": 3455569,
    #                 "rule_id": "pfw1",
    #                 "data": [
    #                     {
    #                         "record_type_name": "住院医嘱",
    #                         "progressId": 4180472,
    #                         "targetKeys": ["医嘱内容"],
    #                         "医嘱内容": "地塞米松针  B 2019-01-04 13:31:27"
    #                     },
    #                     {
    #                         "record_type_name": "查房记录",
    #                         "attribute_name": "",
    #                         "progressId": 2616259,
    #                         "content": "2019-01-04 10:29      赵弘副主任医师查房。   患者诉偶有咳嗽，咳少量白痰，未诉其他明显不适，病情可。查体：130/80mmHg神清，精神可,口唇无发绀，浅表淋巴结无肿大。双肺呼吸音粗，未闻及干湿性啰音。无胸膜摩擦音。心律齐，未闻及杂音，无心包摩擦音。腹软，无压痛、反跳痛及肌紧张，肝脾肋下未及。双下肢无水肿。。   赵弘副主任医师查房：1、诊断：多发性骨髓瘤化疗 多发性骨髓瘤（IgGκ型 DS III期）：患者老年女性，慢性起病，查IgG 20.8g/l，KAP 2920mg/ml，LAM 207mg/ml，血清蛋白电泳示：γ球蛋白23.8%，骨穿结果示浆细胞占7.5%，流式示骨髓单克隆浆细胞占1.438%，颅骨X片示 颅盖骨多发穿凿样骨质破坏，明确诊断为“多发性骨髓瘤（IgGκ型 DS III期）”，病史明确故诊断。2. 鉴别诊断：1）多发性骨髓瘤完全缓解：符合此诊断须满足血尿免疫固定电泳阴性，没有任何软组织浆细胞瘤的表现，骨髓内浆细胞≤5%。患者已完成BCD化疗7疗程后，BD方案维持化疗中，复查骨穿，流式示：骨髓单克隆浆细胞占0.278%；免疫固定电泳示：重链IgG阳性，轻链kappa阳性。故暂不考虑该诊断。2）多发性骨髓瘤疾病进展：符合此诊断须满足血清M或尿蛋白增加≥25%，骨髓浆细胞百分比绝对值≥10%。复查骨穿流式示：骨髓单克隆浆细胞占0.278%；故暂不考虑该诊断。3.治疗：入院后予完善相关检查，排除化疗禁忌，于今日行BD方案静脉化疗，具体化疗方案如下：      。          d1（01.04）    。硼替佐米2.5mg ↑    。地塞米松40mg  ↑    。化疗同时予止吐、护胃及碱化尿液等治疗。化疗过程顺利，无不适反应。患者偶有咳嗽，伴咳白色粘痰，今日已行胸部CT检查，阅片提示不排除肺部感染可能，今日给予加用一君预防感染等对症治疗。病情继观。。          "
    #                     }
    #                 ]
    #             }
    #         ]
    #     res = pf.work_flow(reqs)[0]
    #     _data["input"] = res["input"]
    #     _data["result"] = res["result"]
    #     _data["answer"] = res["answer"]
    #     new_data.append(_data)
    # print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
