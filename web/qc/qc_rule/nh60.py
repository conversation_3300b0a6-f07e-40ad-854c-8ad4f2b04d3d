# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re

class Nh60Flow(QcFlow):
    def __init__(self):
        # 入院记录体格检查结果与手术史矛盾
        self.rule_id = 'nh60'
        super(Nh60Flow, self).__init__(self.rule_id)

    def pipeline(self):
        tasks = [
            {'func': self.aggregate_data, 'desc': '获取飞天数据', 'flag': 1},
            {'func': self.is_need_qc, 'desc': '判断是否需要做质控', 'flag': 1},
            {'func': self.get_llm_result, 'desc': '大模型预测结果', 'flag': 1},
            {'func': self.get_rule_answer, 'desc': '获取质控结果', 'flag': 1}
        ]
        return tasks

    def is_need_qc(self, data):
        exam = data["体格检查"]
        if exam is None or len(exam.strip()) == 0:
            instruction = ""
            question = ""
            answer = "成功"
            result = "体格检查不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        opt = data["手术史"]
        if opt is None or len(opt.strip()) == 0:
            instruction = ""
            question = ""
            answer = "成功"
            result = "手术史不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def aggregate_data(self, ft_data):
        customer_id = ft_data['customer_id']
        record_id = ft_data['record_id']
        rule_id = ft_data['rule_id']
        for item in ft_data['data']:
            item['content'] = item['content'].replace(';', '；').replace(',', '，')
        phy_exam = []
        for item in ft_data['data']:
            if item['record_type_name'] == '入院记录' and item['attribute_name'] == '体格检查':
                phy_exam.append(item['content'])

        phy_exam = '\n'.join(phy_exam)
        phy_exam = phy_exam.replace('\u200b', '').replace('\t', ' ').replace('\r', ' ').lstrip(' ').rstrip(' ')

        opt_his = []
        # 判断请求中是否有手术史，有的话直接用，没有的话用既往史
        has_opt_his = False
        for item in ft_data['data']:
            if item['record_type_name'] == '入院记录' and item['attribute_name'] == '手术史':
                has_opt_his = True
                break

        for item in ft_data['data']:
            if has_opt_his and item['record_type_name'] == '入院记录' and item['attribute_name'] == '手术史':
                opt_his.append(item['content'])
            if (not has_opt_his) and item['record_type_name'] == '入院记录' and item['attribute_name'] == '既往史':
                opt_his.append(item['content'])
        opt_his = '。'.join(opt_his)
        opt_his = opt_his.replace('\u200b', '').replace('\t', ' ').replace('\r', ' ').lstrip(' ').rstrip(' ')
        if '见现病史' in opt_his:
            answer = '成功'
            result = '手术史在现病史中，无法判断，不做质控。'
            return self.response(ft_data, '', '', answer, result)

        return {
            'customer_id': customer_id,
            'record_id': record_id,
            'rule_id': rule_id,
            '体格检查': phy_exam,
            '手术史': opt_his
        }

    def get_llm_result(self, data):
        phy_exam = data['体格检查']
        opt_his = data['手术史']

        input1 = f'{phy_exam}\n\n判断体格检查中是否有对于手术疤痕或者手术切口的描述，请先回答有或者没有，再分析。'
        data1 = {'customer_id': data['customer_id'], 'record_id': data['record_id'], 'rule_id': data['rule_id'],
                 'input': input1}
        llm_result1 = self.llm_predict([data1], llm_model="Qwen_14b")
        if llm_result1:
            res1 = llm_result1[0]['result']
        else:
            res1 = ''

        input2 = self.config['FEW_SHOT'].format(opt_his)
        for w in self.config['REG_NOT_NEED_QC'].split(','):
            if w in input2:
                return self.response(data, '', '', '成功', '不做质控')

        if opt_his:
            data2 = {'customer_id': data['customer_id'], 'record_id': data['record_id'], 'rule_id': data['rule_id'],
                     'input': input2}
            llm_result2 = self.llm_predict([data2], llm_model="Qwen_14b")
            if llm_result2:
                res2 = llm_result2[0]['result']
            else:
                res2 = ''
        else:
            res2 = '不会，手术史为空，所以不会。'

        data['input'] = input1 + '@@@@@' + input2
        data['result'] = res1 + '@@@@@' + res2
        return data
    def get_answer_promt(self, data):
        answer = '成功'
        res1, res2 = data['result'].split('@@@@@')
        new_result = ''
        res2 = re.sub(r'答[：:]\s*', '', res2)
        res1 = re.sub(r'答[：:]\s*', '', res1)
        if res1.startswith('没有') and res2.startswith('会'):
            answer = '失败'
            new_result = "体格检查中对于手术疤痕或者手术切口没有相关描述。" \
                         "但是手术史会造成手术疤痕或者手术切口，{}".format(res2.replace('会，', ''))
        if res1.startswith('有') and res2.startswith('不会'):
            answer = '失败'
            new_result = "体格检查中对于手术疤痕或者手术切口有相关描述。" \
                         "但是手术史不会造成手术疤痕或者手术切口，{}".format(res2.replace('不会，', ''))

        return new_result, answer

    def get_rule_answer(self, data):
        result, answer = self.get_answer_promt(data)
        return self.response(data, data['input'], data['input'], answer, result)


if __name__ == '__main__':
    qc = Nh60Flow()
    reqs = [{
            "customer_id": 1001,
            "record_id": 69877382,
            "rule_id": "nh60",
            "data": [
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "体格检查",
                    "progressId": 9762610,
                    "content": "体格检查 \tt: 36.5 °c p: 76 次/分 r: 16 次/分 bp: 116/76 mmhg\t神志清晰,精神尚可,呼吸平稳,营养中等,表情自如,发育正常,自主体位,应答流畅,查体合作 。 皮肤、黏膜: 色泽正常 弹性正常 水肿无 右大腿可见局部手术瘢痕。全身浅表淋巴结无肿大,头颅无畸形,巩膜无黄染、眼球无突出、瞳孔等大等圆、对光反射灵敏,力正常、外耳道无分泌物、耳廓、乳突无压痛鼻中隔无偏曲、鼻翼无扇动、鼻窦区无压痛口唇红润光泽、口腔无特殊气味、伸舌居中、扁桃体无肿大、腮腺正常 。颈软,甲状腺未及肿大,胸廓无畸形,双肺叩诊清音,听诊呼吸音清。心前区无隆起,心界不大,心率76次/分,律齐。腹部平软,肝脾肋下未及,肝肾无叩击痛,肠鸣音3次/分。。肛门及生殖器未检,四肢脊柱无畸形,活动自如,神经系统检查(-)。\t ",
                    "targetKeys": []
                },
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "既往史",
                    "progressId": 9762610,
                    "content": "既往史:手术外伤史:否认有手术外伤史 \t ",
                    "targetKeys": []
                }
            ]
        }]
    print(qc.work_flow(reqs))
