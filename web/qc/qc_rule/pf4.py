# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pf4Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pf4"
        super(Pf4Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        disease = data['出院诊断']
        data['pf4出院诊断内容'] = disease
        data["pf4病程记录内容"] = records
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        outputs = result.split("\n")
        if len(outputs) > 0:
            res = outputs[-1]
            if "有诊断无法对应" in res:
                answer = "失败"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = (f'[内容-出院诊断]'
                 f'{data["pf4出院诊断内容"]}\n'
                 f'[内容-出院诊断-结束]\n'
                 f'[内容-病程记录]'
                 f'{data["pf4病程记录内容"]}\n'
                 f'[内容-病程记录-结束]')
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录",
                                             "日常病程", '首次病程记录']:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "出院记录" and _data["attribute_name"] == "出院诊断":
                return [(f"出院诊断", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '出院诊断': new_data.get('出院诊断', [])[0],
        }


if __name__ == '__main__':
    pf = Pf4Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{"customer_id": 1001, "record_id": 69993, "rule_id": "pf4", "data": [
            {"record_type_name": "出院记录", "attribute_name": "出院诊断", "progressId": 97647,
             "content": "出院诊断:1.左肺癌(腺癌,pt1n0m0,ia期→rt1n0m0 ia2期,egfr 19del)\t2.恶性肿瘤放射治疗\t3.姑息性化疗\t4.(右)肺磨玻璃灶\t5.骨髓抑制(白细胞减少)\t6.高血压病3级(极高危)\t7.陈旧性脑梗死\t8.2型糖尿病 ",
             "targetKeys": []}, {"record_type_name": "首次病程记录", "attribute_name": "", "progressId": 97645,
                                 "content": "2024-03-28 11:00 首次病程记录\r\n病例特点:\r\n1.患者中年女性,既往脑梗死、高血压、糖尿病病史;\r\n2.患者已发现肺结节为首发表现,于2022-04-13全麻下行胸腔镜下胸腔黏连松解术+左肺上叶切除加纵膈淋巴结清扫术,术后病理提示腺癌;\r\n3.体格检查:胸部可见手术瘢痕。胸廓对称无畸形,双肺呼吸音清,未闻干湿性啰音。心律齐,各瓣膜听诊区未闻及病理性杂音;\r\n4.辅助检查:2022-1-04胸部CT双肺多发小结节影,最大者位于左肺上叶尖后段,长径约9mm,未见明显肿大淋巴结。(台州市立医院)2022-3-22胸部CT双肺结节灶,最大者位于左肺上叶尖后段,截面约1.1*1.0cm,纵隔、肺门未见肿大淋巴结。(山东省立医院)2022-04-15术后常规病理:(左肺上叶尖后段)浸润性腺癌,腺泡为主型,未侵犯脏层胸膜。未见明确神经及脉管侵犯。(左肺上叶)残留肺组织未见癌。支气管断端未见癌。区域淋巴结状态:支气管周(0/1)、“5+6组”(0/1)、“7组”(0/2)、“10组”(0/1)、“11组”(0/2)、“12组”(0/5)。\r\n初步诊断:\r\n1.左肺癌(腺癌,pT1N0M0,IA) 2.(右)肺磨玻璃灶 3.高血压病3级(极高危) 4.陈旧性脑梗死 5.2型糖尿病\r\n诊断依据:\r\n1.患者中年女性,既往脑梗死、高血压、糖尿病病史;\r\n2.患者已发现肺结节为首发表现,于2022-04-13全麻下行胸腔镜下胸腔黏连松解术+左肺上叶切除加纵膈淋巴结清扫术,术后病理提示腺癌。\r\n鉴别诊断:\r\n病理诊断明确,无需鉴别。\r\n诊疗计划:\r\n1.放疗科护理常规,II级护理,低盐低脂饮食。\r\n2.完善血常规、凝血、肝肾功、心电图及胸腹CT等辅助检查,评估患者病情;\r\n3.待检查结果完善后汇报上级医师制定下一步诊疗计划。",
                                 "targetKeys": []}]}]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
        print(new_data)
        #     if len(new_data) % 100 == 99:
        #         pd.DataFrame(new_data).to_excel(path.format("_save"))
        # pd.DataFrame(new_data).to_excel(path.format("_save"))
