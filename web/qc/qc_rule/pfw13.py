# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw13Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pfw13"
        super(Pfw13Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        disease = data["初步诊断"]
        medical_order = data['医嘱内容']

        data["pfw13输入内容"] = (f'[内容-药品医嘱]\n'
                           f'{medical_order}\n'
                           f'[内容-药品医嘱-结束]\n'
                           f'[内容-诊断]\n'
                           f'{disease}\n'
                           f'[内容-诊断-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()
        if "有遗漏" in result:
            answer = "失败"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw13输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] == "入院记录" and _data["attribute_name"] == "初步诊断":
                return [(f"初步诊断", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '医嘱内容': "\n".join(new_data.get('医嘱内容', [])),
            '初步诊断': "\n".join(new_data.get('初步诊断', [])),
        }


if __name__ == '__main__':
    pf = Pfw13Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
            "request": [
                {
                    "customer_id": 1801,
                    "record_id": 3455569,
                    "rule_id": "pfw13",
                    "data": [
                        {
                            "record_type_name": "住院医嘱",
                            "progressId": 4180472,
                            "targetKeys": ["医嘱内容"],
                            "医嘱内容": "阿立哌唑  B 2019-04-03 15:53:05 2.5mg\n五氟利多  B 2019-04-08 09:33:08 2.0mg"
                        },
                        {
                            "record_type_name": "入院记录",
                            "attribute_name": "初步诊断",
                            "progressId": 2616259,
                            "content": "初步诊断: 高血压 糖尿病"
                        }
                    ]
                }
            ]
        }]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
