# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Pf8Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pf8"
        super(Pf8Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "func": self.is_need_qc,
                "desc": "判断是否需要调用大模型",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def aggregate_data(self, data):
        def make_key(_d):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _d["attribute_name"] in ["初步诊断"]:
                return [(f"初步诊断", _d["content"])]
            elif _d["attribute_name"] in ["诊断依据"]:
                return [(f"诊断依据", _d["content"])]

        mapping = {}
        for row in data["data"]:
            keys = make_key(row)
            for _key, _content in keys:
                if _key:
                    mapping.setdefault(_key, []).append(_content.replace('\u200b', ''))
        print(mapping)
        return {
            "customer_id": data["customer_id"],
            "record_id": data["record_id"],
            "rule_id": data["rule_id"],
            "诊断依据": mapping.get("诊断依据", []),
            "初步诊断": mapping.get("初步诊断", []),
        }

    def exactor_prompt(self, data):
        diagnose_analysis = '\n'.join(data["诊断依据"])
        diseases = '\n'.join(data['初步诊断'])

        data["pf8输入内容"] = (f'[内容-诊断依据]\n'
                           f'{diagnose_analysis}\n'
                           f'[内容-诊断依据-结束]\n'
                           f'[内容-初步诊断]\n'
                           f'{diseases}\n'
                           f'[内容-初步诊断-结束]')
        return data

    def is_need_qc(self, data):
        diagnose_analysis = data["诊断依据"]
        diseases = data['初步诊断']
        instruction = data["pf8输入内容"]
        if not diagnose_analysis:
            question = self.config["QUESTION"].format(instruction)
            return self.response(data, instruction, question, "成功", "缺少诊断依据，不做质控")
        if not diseases:
            question = self.config["QUESTION"].format(instruction)
            return self.response(data, instruction, question, "成功", "缺少初步诊断，不做质控")
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue
            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    # 统一使用该方法做后处理
    def get_answer_promt(self, data):
        result = data["result"].strip()
        answer = "成功"
        # 遍历句子并提取错误和正确的词汇
        matches = re.findall(r"找不到.*?相应.*?描述", result)
        if matches:
            answer = "失败"
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        instruction = data["pf8输入内容"]
        question = self.config["QUESTION"].format(instruction)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, instruction, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, instruction, question, answer, result)


if __name__ == '__main__':
    pf = Pf8Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
            "customer_id": 1801,
            "record_id": 3455569,
            "rule_id": "pf8",
            "data": [
                {
                    "record_type_name": "首次病程记录",
                    "attribute_name": "初步诊断",
                    "progressId": 2616259,
                    "content": "初步诊断(首程):1. 脓毒血症2. 急性支气管炎3. 心肌损害"
                },
                {
                    "record_type_name": "首次病程记录",
                    "attribute_name": "诊断依据",
                    "progressId": 2616259,
                    "content": "诊断依据:1.患儿系3岁10月女童,有高热、咳嗽等症状,血常规白细胞、crp明显升高,pct升高,故诊断。2.患儿病程中有发热、咳嗽表现,听诊呼吸音粗,故诊断。3.患儿急诊测肌酸激酶-mb活性 30u/l,故诊断"
                }
            ]
        }]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
