# -*- coding: utf-8 -*-
import re
from collections import defaultdict
from web.qc.qc_util import QcFlow


class Nh269Flow(QcFlow):
    def __init__(self):
        # 手术记录中手术名称和手术经过不相符
        self.rule_id = 'nh269'
        super(Nh269Flow, self).__init__(self.rule_id)

    def pipeline(self):
        tasks = [
            {'func': self.aggregate_data, 'desc': '获取飞天数据', 'flag': 1},
            {'func': self.get_llm_result, 'desc': '大模型预测结果', 'flag': 1},
            {'func': self.get_rule_answer, 'desc': '获取质控结果', 'flag': 1}
        ]
        return tasks

    def aggregate_data(self, ft_data):
        customer_id = ft_data['customer_id']
        record_id = ft_data['record_id']
        rule_id = ft_data['rule_id']
        for item in ft_data['data']:
            item['content'] = item['content'].replace(';', '；').replace(',', '，')
        # 多个手术名称+手术经过，按照progressId拼接成list
        grouped_data = defaultdict(list)
        for item in ft_data['data']:
            if item['record_type_name'] == '手术记录':
                grouped_data[item['progressId']].append(item)
        opt_list = []
        for _, group in grouped_data.items():
            if len(group) != 2:  # 手术名称和手术经过不成对出现，跳过
                continue
            opt_name, opt_content = '', ''
            for item in group:
                if item['attribute_name'] == '手术名称':
                    if '录入时间' in item['content']:
                        find_result = re.findall('手术名称:(.*)录入时间', item['content'])
                    else:
                        find_result = re.findall('手术名称:(.*)', item['content'])
                    if find_result:
                        opt_name = find_result[0]
                    else:
                        opt_name = ''
                    opt_name = opt_name.replace('\u200b', '').replace('\t', ' ').replace('\r', ' ').lstrip(' ').rstrip(' ')
                elif item['attribute_name'] == '手术经过':
                    find_result = re.findall('手术经过:(.*)', item['content'])
                    if find_result:
                        opt_content = find_result[0]
                    else:
                        opt_content = ''
                    opt_content = opt_content.replace('\u200b', '').replace('\t', ' ').replace('\r', ' ').lstrip(' ').rstrip(' ')
                    if '手术操作一览表' in opt_content:
                        opt_content = re.findall('(.*)手术操作一览表', opt_content)[0]
            if opt_name or opt_content:
                opt_list.append({'手术名称': opt_name, '手术经过': opt_content})
        if not opt_list:
            return self.response(ft_data, '', '', '成功', '没有手术记录，不做质控')

        for opt in opt_list:
            if opt['手术名称'] and not opt['手术经过']:
                return self.response(ft_data, '', '', '成功', '有手术名称但是没有手术经过')
            if not opt['手术名称'] and opt['手术经过']:
                return self.response(ft_data, '', '', '成功', '有手术经过但是没有手术名称')

        return {
            'customer_id': customer_id,
            'record_id': record_id,
            'rule_id': rule_id,
            '手术记录': opt_list
        }

    def get_llm_result(self, data):
        inputs = []
        results = []
        for item in data['手术记录']:
            opt_name = item['手术名称']
            opt_content = item['手术经过']
            if opt_name.split('+')[0].endswith('检查'):  # “xxx检查”手术不做质控
                continue

            if '手术操作一览表' in opt_content:
                find_result = re.findall('(.*)手术操作一览表', opt_content)
                if find_result:
                    opt_content = find_result[0]

            input_ = f'手术名称:{opt_name}\n手术经过:{opt_content}\n\n请对上述病历做质控，判断手术记录中手术名称和手术经过是否相符'
            inputs.append(input_)
            data_tmp = {'customer_id': data['customer_id'], 'record_id': data['record_id'], 'rule_id': data['rule_id'], 'input': input_}
            llm_result = self.llm_predict([data_tmp], llm_model="Qwen_14b")
            if llm_result:
                res = llm_result[0]['result']
            else:
                res = ''
            results.append(res)
        data['input'] = '@@@@@'.join(inputs)
        data['result'] = '@@@@@'.join(results)
        return data
    def get_answer_promt(self, data):
        answer = '成功'
        results = data['result'].split('@@@@@')

        for res in results:
            # 肯定过滤
            # 没有发现明显不符
            if re.search(r'没有.{0,4}不符', res):
                continue
            # 否定失败
            if re.search(r'不符|不相符|不完全相符|不完全符合', res):
                answer = '失败'
                break

        return results, answer
    def get_rule_answer(self, data):
        result, answer = self.get_answer_promt(data)
        return self.response(data, data['input'], data['input'], answer, data['result'])


if __name__ == '__main__':
    qc = Nh269Flow()
    reqs = [{"customer_id": 1001, "record_id": 3150176, "rule_id": "nh269", "data": [{"record_type_name": "手术记录", "attribute_name": "手术名称", "progressId": 9961116, "content": "手术名称:化疗泵取出术((97.8903)) 录入时间:- "}, {"record_type_name": "手术记录", "attribute_name": "手术经过", "progressId": 9961116, "content": "手术经过:患者仰卧位,心电监护,常规右侧胸廓及肩部消毒铺巾,以2%利多卡因5ml局麻穿刺部位,彩超引导穿刺右腋静脉近锁骨下静脉成功后,引入鞘及透视下定位port导管至上腔静脉内,然后局麻右侧锁骨区域,逐层切开皮肤及皮下组织至胸廓筋膜层,钝性分离皮下组织与筋膜层内做一皮囊,置入port主体,然后用隧道针将port导管引至皮囊并连接port,注入肝素水证实无渗漏后将port定位,给予3-0可吸收线进行皮下缝合,酒精纱布外敷纱布包裹伤口,术中患者无不适主诉,术毕患者安返病房。术后注意事项:观察伤口有无渗血,疼痛等情况;术后一周伤口勿沾水,无需拆线。 "}]}]
    print(qc.work_flow(reqs))
