# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Nh182Flow(QcFlow):
    def __init__(self):
        # 主诉和入院记录诊断无明显关联
        self.rule_id = "nh182"
        super(Nh182Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.is_need_qc,
                "desc": "是否做规则质控",
                "flag": 1
            },
            {
                "func": self.exactor_main_disease,
                "desc": "提取诊断",
                "flag": 1
            },
            {
                "func": self.filter_disease,
                "desc": "诊断过滤",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_main = data["主诉"]
        reg_not_need_qc = self.config["REG_NOT_NEED_QC"]
        # 判断是否做质控，若正则匹配则不做质控 返回结果
        match = re.search(reg_not_need_qc, org_disease)
        if match:
            instruction = f"初步诊断：{org_disease}\n" + f"主诉：{org_main}"
            question = self.config["QUESTION"].format(org_main, org_disease)
            answer = "成功"
            result = "第一诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)
        reg_not_main_need_qc = self.config["REG_NOT_MAIN_NEED_QC"]
        # 判断是否做质控，若正则匹配则不做质控 返回结果
        match = re.search(reg_not_main_need_qc, org_main)
        if match:
            instruction = f"初步诊断：{org_disease}\n" + f"主诉：{org_main}"
            question = self.config["QUESTION"].format(org_main, org_disease)
            answer = "成功"
            result = "主诉不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_main_disease(self, data):
        org_disease = data["初步诊断"]
        org_main = data["主诉"]
        reg_cleaned_disease = self.config["REG_CLEANED_DISEASE"]
        reg_exa_disease = self.config["REG_EXA_DISEASE"]
        disease_str = ""
        # 数据清洗
        _disease = re.sub(reg_cleaned_disease, "", org_disease, 0, re.MULTILINE)
        # 诊断提取
        match = re.search(reg_exa_disease, _disease)
        if match:
            disease_str = match.group(1).strip()
            # 拆分 1. 生物制剂治疗\t 2. 克罗恩病(a1a,l3,b1,g0)  -> 1. 生物制剂治疗  取第一个disease_str
            disease_array = [p for p in re.split(r"(?:\s|\t|\d\.\s*|\d、\s*|入院诊断)", disease_str) if
                             p.strip()]
            if len(disease_array) == 0 or len(disease_array[0]) <= 1:
                disease_str = ""
            else:
                disease_str = disease_array[0]
        data["初步诊断"] = disease_str.strip()
        data["主诉"] = org_main.strip()

        return data

    def filter_disease(self, data):
        disease_str = data["初步诊断"]
        org_main = data["主诉"]
        if disease_str == "":
            instruction = f"初步诊断：{disease_str}\n" + f"主诉：{org_main}"
            question = self.config["QUESTION"].format(org_main, disease_str)
            answer = "成功"
            result = "第一诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)
        if "待查" in disease_str:
            instruction = f"初步诊断：{disease_str}\n" + f"主诉：{org_main}"
            question = f"初步诊断：{disease_str}\n" + f"主诉：{org_main}"
            answer = "成功"
            result = "第一诊断待查，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    # 统一使用该方法做后处理
    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        if result[:3] == "无关联":
            # 无关联，说明有缺陷,answer为失败
            answer = "失败"
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        disease_str = data["初步诊断"]
        org_main = data["主诉"]
        input = data["input"]
        question = self.config["QUESTION"].format(org_main, disease_str)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            if data["record_type_name"] == "入院记录" and data["attribute_name"] in ("初步诊断", "主诉"):
                return f"{data['attribute_name']}"

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            key = make_key(_data)
            if key:
                new_data.setdefault(key, []).append(_data["content"].replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            # 若有多条初步诊断、主诉，只取第一条
            "初步诊断": new_data.get("初步诊断", [])[0],
            "主诉": new_data.get("主诉", [])[0]
        }


import pandas as pd

if __name__ == '__main__':
    qc = Nh182Flow()
    path = "nh_182_zhere{}.xlsx"
    lst = pd.read_excel(path.format("")).to_dict("records")
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
            "customer_id": "1220",
            "record_id": "1885255",
            "rule_id": "nh182",
            "data": [
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "主诉",
                    "content": _data["入院主诉"]
                },
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "初步诊断",
                    "content": "诊断:{}".format(_data["入院诊断"])
                }
            ]
        }]
        res = qc.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        new_data.append(_data)
        if len(new_data) % 100 == 99:
            pd.DataFrame(new_data).to_excel(path.format("_save"))
    pd.DataFrame(new_data).to_excel(path.format("_save"))
