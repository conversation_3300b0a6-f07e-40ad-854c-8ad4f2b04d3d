# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pfw5Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "pfw5"
        super(Pfw5Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        medical_order = data['医嘱内容']

        data["pfw5输入内容"] = (
            f'[内容-病程记录]\n'
            f'{records}\n'
            f'[内容-病程记录-结束]')
        data["医嘱药品"] = medical_order
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()

        output = result.split("\n")[-1]
        if "否" in output:
            answer = "失败"
        pattern = r"(?:step[12]).*?(是|否)"
        # 遍历句子并提取错误和正确的词汇
        outputs = result.replace("\n", "").replace("是/否", "")
        matches = re.findall(pattern, outputs)
        for _match in matches:
            if _match == "否":
                answer = "失败"
            break
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pfw5输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录", "日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        _record = _data[k].replace("(客户药品名)", "")
                        res.append((k, _record))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '医嘱内容': "\n".join(new_data.get('医嘱内容', [])),
        }


if __name__ == '__main__':
    pf = Pfw5Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1001,
                "record_id": 69004,
                "rule_id": "pfw5",
                "data": [
                    {
                        "record_type_name": "住院医嘱",
                        "progressId": 117303,
                        "医嘱内容": "地塞米松(客户药品名) 5mg ",
                        "targetKeys": [
                            "医嘱内容"
                        ]
                    },
                    {
                        "record_type_name": "日常病程",
                        "attribute_name": "",
                        "progressId": 94481,
                        "content": "2023-12-22 16:16:19 日常病程记录\n查房记录: 血气电解质:酸碱度 pH7.215↓,二氧化碳分压 pCO2 56.1↑mmHg,氧分压 pO244.1↓mmHg,血红蛋白 Hb(血气仪)132↓g/L,氧饱和度 sO273.5↓%,钾 K+3.8 mmol/L,钠 Na+133↓mmol/L,氯 Cl-108↑mmol/L,钙(离子)Ca2+1.03↓mmol/L,葡萄糖 Glu(电极法)6.0 mmol/L,乳酸 Lac2.4↑mmol/L,红细胞压积Hct40.5 %,阴离子间隙 AG3.2↓mmol/L,碳酸氢根HCO3-21.9 mmol/L,标准碳酸氢根 SBC19.0↓mmol/L,实际碱剩余 ABE-6.2↓mmol/L,标准碱剩余 SBE-4.8↓mmol/L,继续密切观察。\n医师签名:",
                        "targetKeys": []
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
        print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
