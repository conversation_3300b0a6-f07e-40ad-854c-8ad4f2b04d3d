# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh54Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要检查结果进行记录
        self.rule_id = "nh54"
        super(Nh54Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.is_need_qc,
                "desc": "判断是否需要做质控",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        menstrual_history = data["月经史"]
        if menstrual_history is None or len(menstrual_history.strip()) == 0:
            instruction = ""
            question = ""
            answer = "成功"
            result = "月经史不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        menstrual_history = data["月经史"].replace("月经史", "")
        data["nh54输入内容"] = menstrual_history
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        if result.startswith('不完整'):
            answer = "失败"
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        input = data["nh54输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        menstrual_history = ""
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            if _data["record_type_name"] == "入院记录" and _data["attribute_name"] == "月经史":
                menstrual_history = _data["content"]
        if "月经史:-" == menstrual_history.strip():
            answer = "成功"
            input = ""
            question = ""
            return self.response(data, input, question, answer, result="没有月经史内容")

        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '月经史': menstrual_history,
        }


if __name__ == '__main__':
    pf = Nh54Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{
            "request": [
                {
                    "customer_id": 1801,
                    "record_id": 3455569,
                    "rule_id": "nh54",
                    "data": [
                        {
                            "record_type_name": "入院记录",
                            "attribute_name": "月经史",
                            "progressId": 2616259,
                            "content": "月经史：19岁,5~6天/28天,2023-04-01 无痛经"
                        }
                    ]
                }
            ]
        }]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
