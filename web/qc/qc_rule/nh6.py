# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Nh6Flow(QcFlow):
    def __init__(self):
        # 入院诊断在入院记录中无相应的诊断依据
        self.rule_id = "nh6"
        super(Nh6Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.is_need_qc,
                "desc": "是否做规则质控",
                "flag": 1
            },
            {
                "func": self.exactor_disease,
                "desc": "提取诊断",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_disease(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        reg_cleaned_disease = self.config["REG_CLEANED_DISEASE"]
        reg_exa_disease = self.config["REG_EXA_DISEASE"]
        reg_filter_pattern = self.config["REG_FILTER_PATTERN"]
        disease_str = ""
        # 数据清洗
        cleaned_text = re.sub(reg_cleaned_disease, "", org_disease, 0, re.MULTILINE)
        if "诊断" not in cleaned_text:
            cleaned_text = "诊断" + cleaned_text
        cleaned_text = cleaned_text.replace("2\t型糖", "2型糖").replace("1\t型糖", "1型糖")
        # 诊断提取
        match = re.search(reg_exa_disease, cleaned_text)
        if match:
            _disease = match.group(1).strip()
            # 拆分 1. 生物制剂治疗\t 2. 克罗恩病(a1a,l3,b1,g0)  -> 1. 生物制剂治疗  取第一个disease_str
            disease_array = set(
                [p for p in re.split(r"(?![^(]*?\))(?:\s|\t|\d\.\s*|\d、\s*|入院诊断)", _disease) if
                 p.strip() and len(p.strip()) > 1 and
                 not re.fullmatch(reg_filter_pattern, p.strip())])
            if len(disease_array) == 0:
                disease_str = ""
            else:
                numbered_diseases = []
                for i, d in enumerate(sorted(disease_array)):
                    numbered_diseases.append(f"{i + 1}.{d}")
                disease_str = " ".join(numbered_diseases)
        data["初步诊断"] = disease_str.strip()
        data["入院记录"] = org_record.strip()

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    # 统一使用该方法做后处理
    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"]
        res = []
        outputs = result.split("\n")
        for _sen in outputs:
            _sen = _sen.strip()
            if len(_sen) == 0:
                continue
            if "虽然" in _sen and not re.search(r'(没有?|未)直接提及', _sen):
                continue
            if "无关联" in _sen:
                answer = "失败"
                res.append(_sen)
        if len(res) > 0:
            result = "\n".join(res)
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        disease_str = data["初步诊断"]
        org_main = data["入院记录"]
        input = data["input"]
        question = self.config["QUESTION"].format(org_main, disease_str)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            if data["record_type_name"] == "入院记录" and data["attribute_name"] in ("初步诊断"):
                return f"{data['attribute_name']}"
            elif data["record_type_name"] == "入院记录":
                return f"{data['record_type_name']}"

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            key = make_key(_data)
            if key:
                new_data.setdefault(key, []).append(_data["content"].replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            # 若有多条初步诊断、主诉，只取第一条
            "初步诊断": new_data.get("初步诊断", [])[0],
            "入院记录": "\n".join(new_data.get("入院记录", []))
        }


import pandas as pd

if __name__ == '__main__':
    qc = Nh6Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1001,
                "record_id": 69025,
                "rule_id": "nh6",
                "data": [
                    {
                        "record_type_name": "入院记录",
                        "attribute_name": "初步诊断",
                        "progressId": 94540,
                        "content": "初步诊断:\t1.2\t型糖尿病并糖尿病高血糖状态糖尿病肾病2.重度贫血:肿瘤性贫血3.宫颈恶性肿瘤(晚期)4.泌尿道梗阻(宫颈癌浸润)5.膀胱出血\t6.左肾造瘘术后心\t ",
                        "targetKeys": []
                    },
                    {
                        "record_type_name": "入院记录",
                        "attribute_name": "主诉",
                        "progressId": 94540,
                        "content": "主 诉:口干多饮多尿20+年,身软乏力4+小时\t ",
                        "targetKeys": []
                    },
                    {
                        "record_type_name": "入院记录",
                        "attribute_name": "体格检查",
                        "progressId": 94540,
                        "content": "体格检查 体温36.2°c脉搏126次/分呼吸20次/分血压119/63mmhg\t一般情况:发育正常,营养中等,体型匀称,神志清楚,体位自主,危重病容。\t皮肤黏膜:皮肤颜色苍白,温度正常,弹性正常,无水肿、瘀点、紫癜、皮下结节、肿块、蜘蛛痣、肝掌、溃疡、瘢痕,毛发分布正常。\t淋巴结:全身浅表淋巴结无肿大。\t头颅五官:头颅无畸形,毛发分布均匀,眼睑苍白,结膜无充血,巩膜无黄染,双侧瞳孔等大等圆,直径3mm,对光反射正常,口唇苍白,伸舌居中,咽部无充血,外耳道无分泌物,听力正常。\t颈部:颈项无强直,肝颈静脉回流征阴性,甲状腺无肿大。\t胸部:胸廓对称,无局部隆起、塌陷、压痛,呼吸频率正常,乳房正常对称,胸壁无静脉曲张。\t肺部:呼吸运动正常,无肋间隙增宽或变窄,语颤正常,无胸膜摩擦感、皮下捻发感,叩诊音清音,双肺呼吸音清晰,无干、湿性啰音,无胸膜摩擦音,语音共振正常。\t心脏:心前区无隆起,心尖搏动[第五肋间锁骨中线内1cm],心浊音界无扩大,律齐,心脉率一致,心率126次/分,各瓣膜听诊区未闻及杂音,无心包摩擦音。无脉搏短绌。\t左锁骨中线距胸骨中线为9cm\t腹部:腹平坦,无腹壁静脉曲张,腹壁无压痛、无反跳痛,腹部柔软,腹部无包块。肝脏肋缘下未触及, 脾脏肋缘下未触及, murphy氏征阴性, 肾区无叩击痛, 无移动性浊音。肠鸣音4次/分。左肾造瘘引流管通畅固定在位,引流出淡黄色尿液,敷料潮湿。膀胱引流管通畅固定在位,引流出血性引流液,\t肛门及外生殖器:肛门及外生殖器未查。\t脊柱四肢:脊柱无畸形,无压痛,四肢无畸形,关节活动正常,无下肢静脉曲张、杵状指(趾),双下肢无水肿,左侧足背动脉搏动正常,右侧足背动脉搏动正常。\t神经系统:腹壁反射正常,双侧肱二、三头肌腱反射正常,双侧膝、跟腱反射正常,双侧babinski's sign阴性, kern ig's sign阴性。\t ",
                        "targetKeys": []
                    },
                    {
                        "record_type_name": "入院记录",
                        "attribute_name": "现病史",
                        "progressId": 94540,
                        "content": "现病史:入院前20+年,患者无明显诱因出现口干、多饮、多尿,每日饮水量约2000-3000ml,伴善食易饥,无心悸、胸闷、胸痛,无心累、气促,无出汗,无皮肤黏膜紫纹,无视物模糊,不伴肢端麻木,无双下肢水肿,院外诊断“糖尿病”,一直口服药物“二甲双胍、格列齐特”治疗,平素血糖控制欠佳。20+天前因上述症状加重伴寒战,我院就诊诊断“1、2型糖尿病周围神经病变糖尿病肾病v期;2、败血症;3、左侧泌尿系统双\"j\"管置入术后;4、高血压病1级(极高危);5、右肾结石;6、肾性贫血?7、高同型半胱氨酸血症;8、高甘油三酯血症;9、宫颈癌个人史”,后转西南医科大学附属医院就诊,诊断“急性肾功衰,宫颈恶性肿瘤(晚期),重度贫血4.泌尿道感染,泌尿道梗阻(宫颈癌浸润),肿瘤性贫血,膀胱血凝块出血,2型糖尿病糖尿病肾病”,行左肾穿刺造瘘,输血,止血等治疗后出院。4+小时前,患者无明显诱因出现身软乏力不适,头晕不适,无晕厥抽搐,无恶心呕吐,无恶心,无呕吐,无咳嗽咳痰,家属波动120急救中心电话后,急救车送我院急诊科测血糖:hi,以“糖尿病”收入我科。自此次发病以来,患者精神欠佳,带入膀胱引流管,可见血性引流液,大便正常。\t ",
                        "targetKeys": []
                    },
                    {
                        "record_type_name": "入院记录",
                        "attribute_name": "既往史",
                        "progressId": 94540,
                        "content": "既往史:1、过去相关病史:自诉5+年前诊断“宫颈腺鳞癌”,行姑息手术,术后未正规复查及治疗,偶有排出暗黑色血凝块;间断因“头昏”服用“头痛粉”,具体不详;否认甲亢病史,否认脑血管疾病病史;\t2、 ",
                        "targetKeys": []
                    },
                    {
                        "record_type_name": "入院记录",
                        "attribute_name": "辅助检查",
                        "progressId": 94540,
                        "content": "辅助检查 2023-12-30血气分析乳酸检测(内二) :ph 7.4910, po 2116.0mmhg, pco 225.9mmhg, ck+4.80mmol/l, cna+135.00mmol/l, ccl-102.00mmol/l, c glu 27.00mmol/l, clac 2.5mmol/l, hct 11.5%, s 02100.0%, thb 3.8g/dl,\t入院指血糖hi,床旁心电图:窦性心动过速。\t ",
                        "targetKeys": []
                    }
                ]
            }
        ]
        res = qc.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        print(_data)
    #     new_data.append(_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
