# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Pf6Flow(QcFlow):
    def __init__(self):
        # 病程记录治疗药品更换需要记录理由
        self.rule_id = "pf6"
        super(Pf6Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]

        data["pf6输入内容"] = (
            f'{records}')
        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        if "有缺陷" in result:
            answer = "失败"

        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        input = data["pf6输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            # TODO 都接受为病程记录
            if 1 or _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录", "日常病程", "知情同意书"]:
                return [(f"病程记录", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
        }


if __name__ == '__main__':
    pf = Pf6Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1801,
                "record_id": 3455569,
                "rule_id": "pf6",
                "data": [
                    {
                        "record_type_name": "首次病程记录",
                        "attribute_name": "",
                        "progressId": 2616258,
                        "content": "查房内容今日查房，患者生命体征平稳，精神可，双侧瞳孔等大等圆，直径约2.5mm，对光反射可。餐后血糖偏高。手术切口外敷料末端见切口裂开，约1m，局部渗出，切口无明显红肿。江建明主任医师查房后指出：目前患者一般情况良好，餐后血糖偏高，予以口服阿卡波糖降低血糖；注意控制血压，保证脑灌注量，今复查血常规、肝肾功能、心功能等指标；切口未愈合，仍有渗出，予以局部清创缝合术，停用速碧林，改用口服氢氯吡格雷片，减少渗出。逐步抬高床头，防止体位性低血压导致脑供血不足。继续予心电监测、低流量吸氧、镇痛、护胃、促进排便、对症支持等治疗，密观患者病情变化，指导患者加强双下肢活动，预防静脉血栓形成。以上已遵嘱执行。"
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
        print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
