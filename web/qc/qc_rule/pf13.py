# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Pf13Flow(QcFlow):
    def __init__(self):
        # 查房记录中对重要病理检查结果进行记录
        self.rule_id = "pf13"
        super(Pf13Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        records = data["手术记录"]

        examination_name = data['病理检查名称']
        examination_sample = data['标本及部位']
        examination_desc = data['病理检查描述']
        examination_result = data['病理检查结果']

        data["pf13输入内容"] = (f'[内容-手术记录]\n'
                            f'{records}\n'
                            f'[内容-手术记录-结束]\n'
                            # f'[内容-诊断]\n'
                            # f'{disease}\n'
                            # f'[内容-诊断-结束]\n'
                            f'[内容-病理检查报告]\n'
                            f'病理检查名称：{examination_name}\n'
                            f'标本及部位：：{examination_sample}\n'
                            f'病理检查描述：{examination_desc}\n'
                            f'病理检查结果：{examination_result}\n'
                            f'[内容-病理检查报告-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res
    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip()
        # 遍历句子并提取错误和正确的词汇
        output = result.split("\n")[-1]
        if "不符合" in output:
            answer = "失败"
        return result, answer
    def get_rule_answer(self, data):
        answer = "成功"
        # disease_str = data["初步诊断"]
        # org_main = data["入院记录"]
        input = data["pf13输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            # 手术记录
            if data["record_type_name"] in ["手术记录"]:
                return [(f"手术记录", _data["content"])]
            elif data["record_type_name"] == "病理检查结果":
                res = []
                if "项目名称" in data:
                    res.append((f"项目名称", _data["项目名称"]))
                if "标本及部位" in data:
                    res.append((f"标本及部位", _data["标本及部位"]))
                if "检查描述" in data:
                    res.append((f"检查描述", _data["检查描述"]))
                if "检查结果" in data:
                    res.append((f"检查结果", _data["检查结果"]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '手术记录': new_data.get('手术记录', [])[0],
            '病理检查名称': "\n".join(new_data.get('项目名称', [])),
            '标本及部位': "\n".join(new_data.get('标本及部位', [])),
            '病理检查描述': "\n".join(new_data.get('检查描述', ["无"])),
            '病理检查结果': "\n".join(new_data.get('检查结果', ["无"]))
        }


if __name__ == '__main__':
    pf = Pf13Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1801,
                "record_id": 3455569,
                "rule_id": "pf13",
                "data": [
                    {
                        "record_type_name": "病理检查结果",
                        "progressId": 4180472,
                        "targetKeys": [
                            "项目名称",
                            "标本及部位",
                            "检查描述",
                            "检查结果",
                            "报告时间"
                        ],
                        "项目名称": "快速病理检查",
                        "标本及部位": "1、 远端胆管切缘，",
                        "检查描述": "",
                        "检查结果": "（1、 远端胆管切缘）粘膜慢性炎，未见肿瘤累及。",
                        "报告时间": "2021-04-12 13:19:00"
                    },
                    {
                        "record_type_name": "手术记录",
                        "attribute_name": "",
                        "progressId": 2616259,
                        "content": "手术经过: 一. 全身麻醉病人 rn1. 目前病人意识：                rn2. 术中知晓：       rn3. 气管插管并发症：                  rn二. 椎管内麻醉病人 rn1. 椎管内麻醉穿刺点：          rn2. 硬膜外导管：          rn3. 椎管内麻醉病人双下肢运动、感觉：          rn三. 神经阻滞 rn1. 神经阻滞穿刺点：          rn2. 被阻滞神经运动感觉：          rn四. 监测 rn1. 动脉穿刺部位：              rn2. 深静脉穿刺部位：             rn五. 镇痛泵 rn1. 镇痛泵：                    rn2. 镇痛泵工作：         rn3. 镇痛泵效果：         rn4. 镇痛泵副作用："
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
        print(new_data)
        #     if len(new_data) % 100 == 99:
        #         pd.DataFrame(new_data).to_excel(path.format("_save"))
        # pd.DataFrame(new_data).to_excel(path.format("_save"))
