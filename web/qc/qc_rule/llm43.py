# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Llm43Flow(QcFlow):
    def __init__(self):
        # 产科病历应优先选择出院诊断中的并发症或伴随疾病诊断作为病历的主诊断
        self.rule_id = "llm43"
        super(Llm43Flow, self).__init__(self.rule_id)
        self.res = {}

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        input_lst = []
        for k, vs in data.items():
            if k in ['customer_id', 'record_id', 'rule_id', 'input_prompt']: continue
            input_lst.append(f"[内容-{k}]\n{vs}\n[内容-{k}-结束]")

        data["输入内容"] = "\n".join(input_lst)

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        result = data["result"].strip()

        if "总结" in result:
            result_data = result.split("总结")[-1]
        else:
            result_data = result
        pred = ""
        gpt_response = result.replace("确定是否违反原则", "").replace("确定是否符合原则", "")
        '''
        gpt_response = "\n".join([x for x in gpt_response.split("\n") if x.find("Step") < 0])
        if "可以视为符合原则" in gpt_response:
            ideal = "无缺陷"
            pred = "符合"
        el
        '''
        if "无法判断是否违反" in gpt_response or "无法判断是否符合" in gpt_response or "无法确定是否符合" in gpt_response \
                or "无法确定是否违反" in gpt_response:
            pred = "无缺陷"
        elif "违反" in gpt_response and "不违反" not in gpt_response and "未违反" not in gpt_response \
                and "没有发现违反" not in gpt_response \
                and "未发现违反" not in gpt_response \
                and "没有违反" not in gpt_response:

            pred = "有缺陷"

        elif "不违反" in gpt_response or "未违反" in gpt_response \
                or "没有发现违反" in gpt_response \
                or "未发现违反" in gpt_response \
                or "没有发现违反" in gpt_response:
            pred = "无缺陷"
        elif "符合" in gpt_response and "不符合" not in gpt_response:
            pred = "无缺陷"
        else:
            pred = "有缺陷"

        return result_data, pred

    def get_rule_answer(self, data):
        answer = "成功"
        # disease_str = data["初步诊断"]
        # org_main = data["入院记录"]
        input = data["输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        result, answer = self.get_answer_promt(data)
        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            key = data['key']
            value = data['value']
            if key not in self.res:
                self.res[key] = []
            value = value.replace('\u200b', '')
            self.res[key].append(value)

        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            make_key(_data)
        agg_res = {
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id
        }
        for k, _kv in self.res.items():
            agg_res[k] = "\n".join(_kv)

        return agg_res


if __name__ == '__main__':
    pf = Llm43Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{"customer_id": 1480, "record_id": 556337, "rule_id": "llm41", "version": "v3",
                 "data": [{"key": "入院记录-主诉", "value": "主 诉:前列腺癌根治术后2月。"},
                          {"key": "入院记录-现病史",
                           "value": "现病史:患者2021-1-6于我院行pca根治术+盆腔淋巴结清扫术,术前骨扫描结果示:左侧耻骨显像剂浓聚灶,考虑肿瘤骨转移可能,建议治疗后随访。患者术后无排尿刺激症状及发热乏力等感染表现,恢复良好。现患者为进一步确诊是否存在远端骨转移可能入院行骨穿刺检查。患者自起病以来,精神可,胃纳可,大便如常,小便如常,睡眠尚可,饮食未见异常,体重无明显变化。。"},
                          {"key": "出院记录-诊疗经过",
                           "value": "诊疗经过。患者入院后完善相关检查,并于2021年3月1日在局部麻醉下行左侧耻骨穿刺术,术中ct引导下左侧耻骨穿刺活检1针。标本送病理。现患者恢复可,故予出院。。合并症。/。"},
                          {"key": "患者诊断信息-主诊断", "value": "前列腺恶性肿瘤"}]}]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
