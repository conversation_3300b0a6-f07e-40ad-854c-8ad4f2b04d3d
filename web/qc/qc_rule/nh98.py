# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh98Flow(QcFlow):
    def __init__(self):
        # nh98-手术存在病理检查的患者需要在手术记录中记录取材情况
        self.rule_id = "nh98"
        super(Nh98Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        examination = data["病理检查"]
        opt = data["手术记录"]

        data["nh98输入内容"] = (f'[内容-检查报告]\n'
                                f'{examination}\n'
                                f'[内容-检查报告-结束]\n'
                                f'[内容-手术记录]\n'
                                f'{opt}\n'
                                f'[内容-手术记录-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        result = data["result"].strip()
        answer = "成功"
        if "未明确记录" in result:
            answer = "失败"

        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        # disease_str = data["初步诊断"]
        # org_main = data["入院记录"]
        input = data["nh98输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        if self.is_hospital == '0':  # 公司环境
            result, answer = self.get_answer_promt(data)
        else:
            result, answer = self.super_get_answer_promt(data)

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            #
            if data["record_type_name"] in ["病理检查结果", "病理检查结果表-住院"]:
                return [(f"病理检查", _data["检查结果"])]
            elif data["record_type_name"] == "手术记录":
                return [(f"手术记录", _data["content"])]

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病理检查': new_data.get('病理检查', [])[0],
            '手术记录': new_data.get('手术记录', [])[0],
        }


if __name__ == '__main__':
    nh = Nh98Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1801,
                "record_id": 3455569,
                "rule_id": "nh98",
                "data": [
                    {
                        "record_type_name": "病理检查结果",
                        "attribute_name": "",
                        "progressId": 2616259,
                        "targetKeys": ["检查结果"],
                        "检查结果": "末端回肠：灰白色组织4粒，直径均为0.2cm。（末端回肠）送检组织4粒，其中3粒为肉芽组织及炎性渗出坏死物，1粒固有层内肉芽组织形成，可见单个隐窝脓肿，周围可见炎症较轻区域，肠黏膜呈非扁平的变化不定的肠绒毛异常，参考病史，可符合炎症性肠病，克罗恩(Crohn)病可能。请结合临床。"
                    },
                    {
                        "record_type_name": "手术记录",
                        "attribute_name": "手术经过",
                        "progressId": 2616258,
                        "content": "手术日期：2021-03-18       手术名称：(其他内镜)小肠镜检查 术前诊断：克罗恩病       术中诊断：克罗恩病   麻醉方法：全麻       麻醉医师： **   手术医师：**   手术经过：小肠镜经肛检查至回盲瓣，肠腔内大量粪便影响观察；回盲瓣变形，进入回肠30cm见环周溃疡，肠腔狭窄，镜身无法通过，活检4块送病理；余小肠散在浅溃疡，结肠扭曲明显，未见明显异常。"
                    }
                ]
            }
        ]
        res = nh.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
