# -*- coding: utf-8 -*-
from web.qc.qc_util import QcFlow
import re
from tqdm import tqdm


class Pf2Flow(QcFlow):
    def __init__(self):
        # CT/MRI检查结果需记录在病程记录中	nh100
        self.rule_id = "pf2"
        super(Pf2Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def is_need_qc(self, data):
        org_disease = data["初步诊断"]
        org_record = data["入院记录"]
        if org_disease is None or len(org_disease.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "初步诊断不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        if org_record is None or len(org_record.strip()) == 0:
            instruction = f"初步诊断：{org_disease}\n入院记录：{org_record}"
            question = self.config["QUESTION"].format(org_record, org_disease)
            answer = "成功"
            result = "入院记录不存在，不做质控"
            return self.response(data, instruction, question, answer, result)

        return data

    def exactor_prompt(self, data):
        records = data["病程记录"]
        examination_name = data['检查名称']
        examination_result = data["检查结论"]

        data["pf2输入内容"] = (f'[内容-病程记录]\n'
                               f'{records}\n'
                               f'[内容-病程记录-结束]\n'
                               # f'[内容-诊断]\n'
                               # f'{disease}\n'
                               # f'[内容-诊断-结束]\n'
                               f'[内容-检查报告]\n'
                               f'检查名称：{examination_name}\n'
                               # f'检查描述：{examination_desc}\n'
                               f'检查结论：{examination_result}\n'
                               f'[内容-检查报告-结束]')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"
        result = data["result"].strip()
        pattern = r"(?:答案是|回答是|回答|答案：|总结为).*?(是|否)"
        answer = "成功"
        # 遍历句子并提取错误和正确的词汇
        outputs = result.replace("\n", "\\n")
        matches = re.findall(pattern, outputs)
        for _match in matches:
            if _match == "否":
                answer = "失败"
            break

        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        # disease_str = data["初步诊断"]
        # org_main = data["入院记录"]
        input = data["pf2输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        if self.is_hospital == '0':  # 公司环境
            result, answer = self.get_answer_promt(data)
        else:
            result, answer = self.super_get_answer_promt(data)

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录",
                                            "日常病程"]:
                return [(f"病程记录", _data["content"])]
            elif data["record_type_name"] == "检查结果报告":
                res = []
                if "项目代码名称" in data:
                    res.append((f"检查名称", _data["项目代码名称"]))
                if "检查结论" in data:
                    res.append((f"检查结论", _data["检查结论"]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '检查名称': "\n".join(new_data.get('检查名称', [])),
            '检查结论': "\n".join(new_data.get('检查结论', []))
        }


if __name__ == '__main__':
    pf = Pf2Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [{"customer_id": 1801, "record_id": 5429378, "rule_id": "pf2", "version": "v2", "data": [
            {"record_type_name": "检查结果报告", "attribute_name": "", "progressId": 8988722,
             "项目代码名称": "头部平扫|胸部平扫",
             "检查结论": "较2024-07-03片对比：1.桥脑、 双侧丘脑、基底节区腔隙性脑梗塞，大致同前；2.双侧侧脑室旁低密度影，大致同前，考虑脱髓鞘改变；3.老年性脑改变，同前；4.大脑镰钙化灶，同前；5.原左侧上颌窦、双侧筛窦、蝶窦炎，较前基本好转；  建议结合临床必要时高场MR检查。胸部：1.双肺背侧渗出性改变，左肺下叶较前进展，右肺大致同前；2.左侧胸腔积液，较前稍减轻；3.主动脉及冠状动脉硬化，大致同前；4.右侧第4-6前肋骨皮质皱褶；5.左侧胸壁软组织密度影，大致同前，建议结合临床及复查；6.肝左叶钙化灶，大致同前； 建议结合临床，必要时进一步检查。",
             "targetKeys": ["项目代码名称", "检查结论"]},
            {"record_type_name": "查房记录", "attribute_name": "", "progressId": 92867007,
             "content": "2024.07.27 18:34           输血病程记录       患者病情危重，贫血，消化道出血，为改善贫血，予输注RH阳性0型血，悬浮红细胞2u，输血前给予苯海拉明注射液20mg im预防输血反应，于14:50开始输注O型Rh（+）悬浮红细胞2u，并动态监测输血反应，于17:23输注完毕，输注过程顺利，未诉不适。生命体征：输血前：T36.9℃, P80次/分，R19次/分，BP120/65mmHg，输血后：T36.8℃, P82次/分，R20次/分，BP124/70mmHg，拟择期复查血常规。主治医师:   ",
             "targetKeys": []}]}]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
