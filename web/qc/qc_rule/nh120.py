# -*- coding: utf-8 -*-
from web.qc.qc_util import Qc<PERSON>low
import re
from tqdm import tqdm


class Nh120Flow(QcFlow):
    def __init__(self):
        # 住院患者应用抗菌药物后，病程记录中缺少对应药品名称、用法、剂量
        self.rule_id = "nh120"
        super(Nh120Flow, self).__init__(self.rule_id)

    def pipeline(self):
        # func 接收单个data
        # predict 接收datas list
        tasks = [
            {
                "func": self.aggregate_data,
                "desc": "获取飞天数据",
                "flag": 1
            },
            {
                "func": self.exactor_prompt,
                "desc": "提取prompt",
                "flag": 1
            },
            {
                "prompt": self.config["FEW_SHOT"],
                "desc": "输入大模型指令",
                "flag": 1
            },
            {
                "predict": self.get_llm_result,  # datas list预测
                "desc": "大模型预测结果",
                "flag": 1
            },
            {
                "func": self.get_rule_answer,
                "desc": "获取质控结果",
                "flag": 1
            }
        ]

        return tasks

    def exactor_prompt(self, data):
        records = data["病程记录"]
        medical_order = data['医嘱内容']

        data["nh120输入内容"] = (f'[内容-医嘱名称]\n{medical_order}\n[内容-医嘱名称-结束]\n'
                                 f'[内容-病程记录]\n{records}\n[内容-病程记录-结束]\n')

        return data

    def get_llm_result(self, datas):
        llm_res = []
        for _data in datas:
            if _data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                llm_res.append(_data)
                continue

            llm_res += self.llm_predict([_data], llm_model="Qwen_14b")
        return llm_res

    def get_answer_promt(self, data):
        answer = "成功"

        result = data["result"].strip().replace("有没有", "是否").replace("有无", "是否")
        if "未提及" in result or "没有提及" in result:
            answer = "失败"
        return result, answer

    def get_rule_answer(self, data):
        answer = "成功"
        input = data["nh120输入内容"]
        question = self.config["QUESTION"].format(input)
        if "result" not in data or len(data["result"].strip()) <= 3:
            return self.response(data, input, question, answer, result="")

        if self.is_hospital == '0':  # 公司环境
            result, answer = self.get_answer_promt(data)
        else:
            result, answer = self.super_get_answer_promt(data)

        return self.response(data, input, question, answer, result)

    def aggregate_data(self, data):
        def make_key(_data):
            # 查房记录|病程记录|上级医师查房记录|术后首次病程|首次查房记录
            if _data["record_type_name"] in ["查房记录", "首次查房记录", "术后首次病程", "上级医师查房记录", "病程记录",
                                             "日常病程", '首次病程记录']:
                return [(f"病程记录", _data["content"])]
            elif _data["record_type_name"] == "住院医嘱":
                res = []
                for k in _data["targetKeys"]:
                    if k in _data:
                        res.append((k, _data[k]))
                return res

        new_data = {}
        customer_id = data["customer_id"]
        record_id = data["record_id"]
        rule_id = data["rule_id"]
        for _data in data["data"]:
            keys = make_key(_data)
            for _key, _content in keys:
                if _key:
                    new_data.setdefault(_key, []).append(_content.replace('\u200b', ''))
        return {
            # customer_id、record_id、rule_id必须赋值
            "customer_id": customer_id,
            "record_id": record_id,
            "rule_id": rule_id,
            '病程记录': new_data.get('病程记录', [])[0],
            '医嘱内容': "\n".join(new_data.get('医嘱内容', [])),
        }


if __name__ == '__main__':
    pf = Nh120Flow()
    # path = "/Users/<USER>/Program/py_script_yunxiao/2023Q4/src/qc_hard/nh_hard数据_nh6_prompt_post.xlsx"
    # lst = pd.read_excel(path.format("")).to_dict("records")
    lst = [{}]
    new_data = []
    for _data in tqdm(lst):
        reqs = [
            {
                "customer_id": 1001,
                "record_id": 68993,
                "rule_id": "nh120",
                "data": [
                    {
                        "record_type_name": "住院医嘱",
                        "progressId": 117294,
                        "医嘱内容": "左氧氟沙星片(客户药品名)   ",
                        "targetKeys": [
                            "医嘱内容"
                        ]
                    },
                    {
                        "record_type_name": "首次病程记录",
                        "attribute_name": "",
                        "progressId": 94395,
                        "content": "??首次病程录????????姓名:演示数据????????科室:骨科????????床号:-????????住院号:S28BFSH605230?????????病历特点: 主诉:因发热1天余,胸痛半天现病史:患儿1天余前(2023.12.20 18时左右)在接触感冒的同学后出现发热,初期体温不高,昨中午起高热,最高体温39.4摄氏度,予美林、泰诺林各一次可降至正常,但反复,有咽部疼痛,少许鼻饲,阵发性咳嗽,四肢酸痛。昨上午至当地医院查甲流阳性,予奥司他韦口服1次,半天前出现胸痛,初期不剧,能忍受,胸前区为主,持续约10余秒,且仍反复高热,故至当地医院住院治疗,予“阿奇霉素针、维生素C针”及雾化、吸氧等对症治疗,症状无明显改善,今晨5:30左右胸痛明显,胸闷不能平卧,咳痰不畅,面罩吸氧(3L/min)处理下血氧饱和度80%左右,考虑“流行性感冒(甲型)、I型呼吸衰竭、暴发性心肌炎?重症肺炎?”,联系120转我院继续治疗,在我院急诊,查体意识烦躁不安,面色差,5L/min面罩吸氧下氧合84%,呼吸急促,立即予吸氧、皮囊加压呼吸、气管插管处理,拟“急性重症肺炎、I型呼吸衰竭、流行性感冒”收住我科。入院查体:T 39.7°C,P 192次/分,R 28次/分,BP 100/62mmHg,血氧饱和度80%(气管插管、皮囊加压给氧下),危重评分86分,营养评估1分,GCS评分7+T分,意识不清,烦躁不安,面色苍白,口唇稍绀,颈软,有自主呼吸,双肺呼吸音粗,未闻及明显湿?音,心律齐,心动过速,心音中,心前区未闻及明显杂音,腹软,腹部可见手术疤痕,约10cm,愈合可,肝触诊不满意,脾肋下未及肿大,双侧瞳孔等大等圆,直径1mm,光反射未引出,双侧巴氏征阴性,肩部可见不规则咖啡斑样皮疹,肢端稍凉,CRT3s。\n辅助检查:林州区中医院化验:2023.12.22血气分析:酸碱度 pH 7.32, 二氧化碳分压 pCO2 47.3mmHg, 氧分压 pO2 50.4mmHg, 血红蛋白 Hb128g/L, 氧饱和度 sO2 83.2%, 钾 K+ 3.5mmol/L, 钠 Na+ 133mmol/L, 氯 Cl- 98mmol/L, 钙(离子)Ca2+ 1.18mmol/L, 葡萄糖 Glu(电极法) 10.6mmol/L, 乳酸 Lac 2.2mmol/L, 碳酸氢根HCO3- 24.3mmol/L, 实际碱剩余 ABE -2.1mmol/L。 血沉7mm/h;呼吸道抗原三项12.21:甲型流感病毒抗原 阳性。血常规: 白细胞计数6.08×10^9/L,淋巴细胞百分比 20.9%, 中性粒细胞百分比 63.5%, 血红蛋白 140g/L, 血小板计数 190×10^9/L, 超敏C反应蛋白0mg/L。 2023.12.21凝血功能:凝血酶原时间 13.6s,D-二聚体910 ug/l。本院化验: 2023.12.22 血气+电解质+乳酸+葡萄糖: 酸碱度 pH 7.283↓, 二氧化碳分压 pCO2 49.9mmHg↑, 氧分压 pO2 52.3mmHg, 血红蛋白 Hb(血气仪) 139g/L, 氧饱和度 sO2 84.9%, 钾 K+ 3.9mmol/L, 钠 Na+ 131mmol/L↓, 氯 Cl- 97mmol/L↓, 钙(离子)Ca2+ 1.24mmol/L, 葡萄糖 Glu(电极法) 11.3mmol/L↑, 乳酸 Lac 3.4mmol/L↑, 碳酸氢根HCO3- 22.9mmol/L, 实际碱剩余 ABE -3.7mmol/L↓。 2023.12.22 血常规(五分类).,超敏CRP测定: 白细胞计数 0.97 【危急值处理记录】。2023.12.22 GPT+肌酐+尿素(急诊).,肌酸激酶-MB同工酶活性(急诊): 丙氨酸氨基转移酶(干片法) 19U/L, 肌酐(干片法) 26μmol/L, 尿素(干片法) 3.97mmol/L, 肌酸激酶-MB活性 22U/L。 2023.12.22 心肌损伤标志物: 心肌肌钙蛋白 I <0.05ng/mL, 肌酸激酶同工酶MB质量测定 <1.0ng/ml, 肌红蛋白 16.9ng/mL, 氨基末端脑利钠肽前体 95.0pg/ml。 2023.12.22 胸部平扫 CT全腹部平扫 检查结论 胸部平扫示:肺炎伴两肺下叶实变、不张考虑,两肺充气不均,请结合临床。2023.12.22 普通心电 检查结论 1、窦性心动过速2、右心室高电压3、不完全性右束支传导阻滞图形。\n????????拟诊讨论?????  ???????初步诊断: 1. 急性重症肺炎2. 呼吸衰竭3. 急性呼吸窘迫综合征4. 腹股沟疝\n??????????  ???????诊断依据: 1-3:患儿急性起病,呼吸急促,两肺可闻及湿罗音,胸部CT提示肺炎伴两肺下叶实变、不张考虑,两肺充气不均,急诊面罩吸氧10L/min下血气分析二氧化碳分压 pCO2 49.9mmHg, 氧分压 pO2 52.3mmHg, 血红蛋白 Hb(血气仪) 139g/L, 入科后气管插管下皮囊加压给氧氧饱和度80%,我院胸部CT提示肺不张、肺实变,故诊断。4:患儿有发热,体温反复,白细胞计数 0.97 *10^9/l, 四肢肢端偏凉,毛细血管充盈时间3秒,心率入科192次/分,尿量少,乳酸3.4mmol/l,故诊断。\n??????????  ???????鉴别诊断: 1、金黄色葡萄球菌肺炎:患儿甲流阳性,肺部CT可见大片肺实变及不张,需警惕继发阳性菌感染所致,以金黄色葡萄球菌感染多见,动态观察,待痰培养等检查结果助诊;2、爆发性心肌炎:患儿流感阳性,起病急,有休克表现,血压不稳,需考虑,但急诊心肌酶正常范围,床边B超心脏泵注功能尚可,暂 不考虑,动态监测心肌酶、心脏彩超、心电图等变化。??????????  ???????诊疗计划:1.入院完善相关检查,如三大常规、血气、生化、血培养、前降钙素、B超、心电图、呼吸道病原体核酸13项、胸片等检查;2.予告病危,呼吸机辅助通气,患儿存在重症肺炎,目前甲型流感病毒阳性,CRP升高,考虑覆盖革兰氏阴性、阳性菌及不典型菌的抗生素,请示副主任医师并经三线抗生素审批小组审核后,予美罗培南0.6g/次 q8h、万古霉素440mg q8h及阿奇霉素针0.29g qd泵注抗感染,奥司他韦60mg BID口服,甲泼尼龙泵注抗炎,丙球输注等对症支持治疗;3.用药可能出现肝肾功能损害、皮疹、过敏性休克等,根据患儿病情变化及检查结果进一步调整治疗计划。\n?????????????????????????医师签名:-?????????????????",
                        "targetKeys": []
                    }
                ]
            }
        ]
        res = pf.work_flow(reqs)[0]
        _data["input"] = res["input"]
        _data["result"] = res["result"]
        _data["answer"] = res["answer"]
        new_data.append(_data)
    print(new_data)
    #     if len(new_data) % 100 == 99:
    #         pd.DataFrame(new_data).to_excel(path.format("_save"))
    # pd.DataFrame(new_data).to_excel(path.format("_save"))
