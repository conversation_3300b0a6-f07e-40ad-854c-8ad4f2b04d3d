# -*- coding: utf-8 -*-
import time
import datetime
import yaml
from web.web_utils.api_util import post_url
from web.web_utils.text_util import normalize_text
from web.web_utils.db_util import *
from web.config import *
import hashlib
import os
import re
import jsonlines
import configparser
from loguru import logger
import dashscope
import random
from web.prometheus_monitor import g_monitor, QUALITY_CONTROL_BATCH

dashscope.api_key = 'sk-********************************'
from http import HTTPStatus


def get_model_offline_result(rids, table_name, config_map):
    sql = '''
        select prompt_hash, prompt,record_id,answer,answer_type,lora_model,uuid from {} where record_id in ({});
    '''.format(table_name, ','.join(rids))
    xs = select_by_sql(sql, 'hm_llm', config_map)
    record2prompt = {}
    for v in xs:
        record_id = v['record_id']
        prompt_hash = v['prompt_hash']
        if record_id not in record2prompt:
            record2prompt[record_id] = {}
        if prompt_hash not in record2prompt[record_id]:
            record2prompt[record_id][prompt_hash] = {}
        record2prompt[record_id][prompt_hash]["病历质控"] = [v['answer'], v['uuid'], v['lora_model']]
    return record2prompt


def get_model_offline_result_new(input_hash, table_name, config_map):
    # no record_id
    sql = '''
        select prompt_hash, record_id,answer,answer_type,lora_model,uuid from {}  where prompt_hash='{}' ;
    '''.format(table_name, input_hash)
    # print(sql)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    record2prompt = {}
    for v in xs:
        prompt_hash = v['prompt_hash']
        if prompt_hash not in record2prompt:
            record2prompt[prompt_hash] = {}
        record2prompt[prompt_hash] = [v['answer'], v['uuid'], v['lora_model']]
    return record2prompt


clean_QA_reg = re.compile(r'[^ \u4e00-\u9fa5\s0-9A-Za-z]')


def get_model_QA_result(config_map):
    # 历史QA数据，用于问题检索
    sql = '''
        select  prompt_hash, prompt,answer,lora_model from llm_qa_result where status=1;
    '''
    xs = select_by_sql(sql, 'hm_llm', config_map)
    QAprompt = {}
    for v in xs:
        prompt_hash = v['prompt_hash']
        answer = v['answer']
        if prompt_hash not in QAprompt:
            QAprompt[prompt_hash] = answer
    return QAprompt


def insert_model_result(xs, table_name, config_map):
    if len(xs) == 0:
        return -1
    sql = '''
        INSERT INTO {} (prompt_hash,prompt, answer, uuid, lora_model, customer_id, record_id, answer_type, remark) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s);
    '''.format(table_name)

    ret = execute_sql_many(sql, xs, 'hm_llm', config_map)
    return ret

    # print(f"error:{sql}")
    # return -1


def input_hash_func(input_, llm_model="Qwen_14b"):
    input_ = clean_QA_reg.sub('', input_)
    input_hash = str(
        hashlib.sha1(normalize_text(input_ + f"\nllm_model:{llm_model}").encode("utf-8")).hexdigest())

    return input_hash


def insert_llm_qc(xs, llm_model, config_map):
    lst = []
    for _data in xs:
        if _data.get("input_prompt", "").strip() == "":
            continue
        result = _data["result"]
        input_prompt = _data["input_prompt"]
        input_hash = input_hash_func(input_prompt, llm_model)
        customer_id = _data["customer_id"]
        record_id = _data["record_id"]
        rule_id = _data['rule_id']
        answer = _data['answer']
        uuid = f"{customer_id}_{record_id}"

        if 1 or result:  # 空的结果也进行缓存
            lst.append([input_hash, input_prompt, result, uuid, llm_model, customer_id, record_id, rule_id, answer])
    insert_model_result(lst, 'llm_qc', config_map)


def insert_qc_v2_response(xs, config_map):
    if len(xs) == 0:
        return -1
    sql = '''select distinct prompt_hash from llm_qc_v2_response '''
    prompt_hashs = select_by_sql(sql, 'hm_llm', config_map)
    prompt_hashs = [hash["prompt_hash"] for hash in prompt_hashs]
    inserts = []
    for r in xs:
        input_hash = input_hash_func(r["input"])
        if input_hash in prompt_hashs:  # 如果有过此input，更新结果，没有的话则插入
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            update_sql = f'''
                update llm_qc_v2_response 
                set answer='{r['answer']}', result='{r['result']}', modify_date='{now}'
                where prompt_hash = '{input_hash}'
                and customer_id = {r['customer_id']}
                and record_id = {r['record_id']}
            '''
            execute_sql(update_sql, 'hm_llm', config_map)
            continue

        _x = (
            r['customer_id'], r['record_id'], r['rule_id'], r['input'], r['question'], r['answer'], r['result'],
            input_hash)
        inserts.append(_x)
    sql = '''
        insert into llm_qc_v2_response (customer_id, record_id, rule_id,  input, question, answer, result,prompt_hash)
        VALUES (%s, %s, %s, %s,%s, %s, %s, %s);
    '''
    ret = execute_sql_many(sql, inserts, 'hm_llm', config_map)
    return ret


def delete_result(rids, table_name, config_map):
    sql = '''
        delete from {} where record_id in ({});
    '''.format(table_name, ','.join(rids))
    ret = execute_sql(sql, 'hm_llm', config_map)
    return ret


def load_rule_config():
    sql = '''
            SELECT * FROM llm_qc_config WHERE  status=1;
        '''
    xs = select_by_sql(sql, 'hm_llm', config_map)
    rule_config = {}
    for v in xs:
        # 将键转换为大写
        rule_config[v['rule_id']] = {k.upper(): v for k, v in v.items()}
    return rule_config


def insert_discharge_result(xs, config_map):
    sql = '''
        INSERT INTO llm_discharge (answer, uuid, treatment_uuid, customer_id, record_id, remark) VALUES  (%s, %s, %s, %s, %s, %s);
    '''
    ret = execute_sql_many(sql, xs, 'hm_llm', config_map)
    return ret


def get_feedback_result(rid, lora_model, config_map):
    sql = '''
        select lora_model, customer_id,record_id,doctor_name,feedback,create_date 
        from llm_feedback 
        where record_id = {} and lora_model regexp '{}' order by id;
    '''.format(rid, lora_model)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    lora_model2feedback = {}
    for v in xs:
        lora_model2feedback[v['lora_model']] = v['feedback']
    return lora_model2feedback


def insert_feedback_result(xs, config_map):
    sql = '''
        INSERT INTO llm_feedback (lora_model, answer_uuid, doctor_name, customer_id, record_id, feedback) 
        VALUES (%s, %s, %s, %s, %s, %s);
    '''
    ret = execute_sql_many(sql, xs, 'hm_llm', config_map)
    return ret


def get_cn_str(text):
    chinese_chars = [char for char in text if '\u4e00' <= char <= '\u9fff']
    return ''.join(chinese_chars)


class QcFlow(object):
    logger.info("init config.ini")
    config_map = config_map
    # 数据库缓存
    IS_DB_CACHE = True
    # 模型类型
    LLM_MODEL = "Qwen_14b"
    # 历史QA数据，用于问题检索 使用predefine_case

    # 预设的输入(只取中文字符)和对应的输出
    predefined_cases = {}
    with jsonlines.open(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'rule_config',
                                     'predefined_cases.jsonl')) as reader:
        for x in reader:
            predefined_cases[get_cn_str(x['input'])] = x['output']

    rule_config = load_rule_config()
    is_hospital = config_map['app']['is_hospital']

    def __init__(self, rule_id):
        if not os.path.exists(f'{os.path.dirname(os.path.abspath(__file__))}/rule_config/qc_{rule_id}.yaml'):
            logger.info(f"not qc_{rule_id}.yaml")
            self.config = None
        else:
            with open(f'{os.path.dirname(os.path.abspath(__file__))}/rule_config/qc_{rule_id}.yaml',
                      encoding="utf-8") as f:
                logger.info(f"init qc_{rule_id}.yaml")
                self.config = yaml.safe_load(f)
                if self.is_hospital == '1' and rule_id in self.rule_config:  # 现场环境 is_hospital:1 替换
                    self.config = self.rule_config[rule_id]

    def response(self, data, input, question, answer, result):
        # isResponse:True 表示不需要处理，直接返回结果
        """
        The JSON represents the following information:
        - "rule_id": The value "规则id"
        - "input": The value "大模型输入"
        - "question": The value "问题" (问题:\n主 诉:活动后胸闷气短2年余,加重半月 \n入院记录中的主诉是否与「心脏瓣膜病」的主诊断有关联依据？若有关联，则回答“有关联”与关联的依据；若无关联，则回答“无关联”与无关联的依据；\n回答:\n).
        - "answer": The value "成功/失败/不满足"
        - "result": The value "大模型回复"
        Please note that JSON syntax is not valid Python code.
        The above JSON is only provided as an example,
        and the comments represent the corresponding information conveyed by the JSON structure.
        """
        if result is not None and self.config_map["llm_qc_model"]["text_limit_str"] in result:
            answer = "超长"

        result = re.sub(r'([Ss]tep\s*\d\s*[.：:])', '', result)
        response_ = {
            "isResponse": True,
            "customer_id": data["customer_id"],
            "record_id": data["record_id"],
            "rule_id": data["rule_id"],
            "input": input,
            "question": question,
            "answer": answer,
            "result": result
        }
        return response_

    def super_get_answer_promt(self, data):
        result = data["result"].strip()

        if "总结" in result:
            result_data = result.split("总结")[-1]
        else:
            result_data = result
        gpt_response = result.replace("确定是否违反原则", "").replace("确定是否符合原则", "")
        '''
        gpt_response = "\n".join([x for x in gpt_response.split("\n") if x.find("Step") < 0])
        if "可以视为符合原则" in gpt_response:
            ideal = "无缺陷"
            pred = "符合"
        el
        '''
        if "规则结果:无缺陷" in gpt_response or "规则结果：无缺陷" in gpt_response:
            pred = '无缺陷'
        elif "规则结果:有缺陷" in gpt_response or "规则结果：有缺陷" in gpt_response:
            pred = '有缺陷'
        elif "无法判断是否违反" in gpt_response or "无法判断是否符合" in gpt_response or "无法确定是否符合" in gpt_response \
                or "无法确定是否违反" in gpt_response:
            pred = "无缺陷"
        elif "违反" in gpt_response and "不违反" not in gpt_response and "未违反" not in gpt_response \
                and "没有发现违反" not in gpt_response \
                and "未发现违反" not in gpt_response \
                and "没有违反" not in gpt_response:

            pred = "有缺陷"

        elif "不违反" in gpt_response or "未违反" in gpt_response \
                or "没有发现违反" in gpt_response \
                or "未发现违反" in gpt_response \
                or "没有发现违反" in gpt_response:
            pred = "无缺陷"
        elif "符合" in gpt_response and "不符合" not in gpt_response:
            pred = "无缺陷"
        else:
            pred = "有缺陷"

        if pred == "有缺陷":
            pred = "失败"
        else:
            pred = "成功"
        return result_data, pred

    @staticmethod
    def execute_work(task, datas):
        def format(s, args):
            for k, v in args.items():
                k = '{' + k + '}'
                if k not in s:
                    continue
                s = s.replace(k, str(v).strip())
            return s.strip()

        def prompt_wrapper(data):
            # prompt拼接
            if data.get("isResponse", False):
                # isResponse:True 表示不需要处理，直接返回结果
                return data
            data["input"] = format(task.get("prompt"), data)
            data["output"] = ""
            return data

        # 任务执行
        def task_wrapper(data, func):
            # isResponse:True 表示不需要处理，直接返回结果
            if data.get("isResponse", False):
                return data

            return func(data)

        _func = task.get('func', None)
        if _func is not None:
            _datas = []
            for _data in datas:
                # 获取该条数据是否要保存mysql
                input_prompt = _data.get("input_prompt", "")
                _data = task_wrapper(_data, _func)

                _data["input_prompt"] = input_prompt
                _datas.append(_data)
            return _datas

        _prompt = task.get('prompt', None)
        if _prompt is not None:
            input_datas = []
            for _data in datas:
                input_datas.append(prompt_wrapper(_data))
            return input_datas

        _pred_func = task.get('predict', None)
        return _pred_func(datas)

    def pipeline(self) -> list:
        # 子类需要实现的处理逻辑
        raise NotImplementedError

    def work_flow(self, datas):
        tasks = self.pipeline()
        for _task in tasks:
            if _task.get("flag", 1) == 0:
                continue
            datas = self.execute_work(_task, datas)
        if self.IS_DB_CACHE:
            insert_llm_qc(datas, self.LLM_MODEL, self.config_map)
        return datas

    def llm_predict(self, datas, llm_model="Qwen_14b"):
        is_hospital = self.config_map['llm_qc_model']['is_run']  # 1 调用 llm

        def llm_req(datas, llm_model, limit=-1):
            for _data in datas:
                _data["llm_model"] = llm_model
                text = _data["input"]
                _data["input_prompt"] = text
                start_time = time.time()
                try:
                    # qwen api调用
                    if is_hospital == '0':
                        result = self.predict_QwenApi(text, limit)
                    else:
                        # 本地接口调用
                        # result = self.predict(text, limit)
                        # 本地vllm接口调用
                        result = self.predict_vllm(text, limit)
                except:
                    g_monitor.set_prometheus_fail_count(QUALITY_CONTROL_BATCH)
                    result = ""
                _data["result"] = result

                # 大模型qps统计
                g_monitor.set_prometheus_qps_count(QUALITY_CONTROL_BATCH)
                # 统计耗时和输入输出
                g_monitor.set_model_time_historgram(QUALITY_CONTROL_BATCH, time.time() - start_time)
                g_monitor.set_model_length_summary(QUALITY_CONTROL_BATCH, len(text), len(result))

            return datas

        infer_queue = []
        res_datas = []
        for _data in datas:
            # record_id = int(_data["record_id"])
            record_id = 1001  # TODO 不用record_id 做缓存
            input = _data["input"]
            if get_cn_str(input) in self.predefined_cases.keys():
                _data["result"] = self.predefined_cases[get_cn_str(input)]
                res_datas.append(_data)
                time.sleep(1)
                continue
            record2prompt = {}
            input_hash = input_hash_func(input, self.LLM_MODEL)
            _data["input_hash"] = input_hash
            if self.IS_DB_CACHE:
                try:
                    record2prompt = get_model_offline_result_new(input_hash, 'llm_qc', self.config_map)
                except:
                    record2prompt = {}
            # 数据库获取答案
            if input_hash in record2prompt:
                result, uuid, _ = record2prompt[input_hash]
                _data["result"] = result
                res_datas.append(_data)

            # 数据库未有答案，大模型推理
            else:
                infer_queue.append(_data)
        # 模型推理
        infer_datas = llm_req(infer_queue, llm_model)

        res_datas += infer_datas
        return res_datas

    def chat(self, text, model, limit=-1):
        # 老接口调用
        ip = self.config_map["llm_qc_model"]["qc_chat_ip"]
        port = self.config_map["llm_qc_model"]["qc_chat_port"]
        chat_url = 'http://{}:{}/generate'.format(ip, port)
        if limit == -1:
            limit = int(self.config_map["llm_qc_model"]["text_limit"])
        # 超过长度不调用大模型预测
        if limit != -1 and len(text) > limit:
            result = self.config_map["llm_qc_model"]["text_limit_str"]
            return result
        return_json = post_url(chat_url, {'input': text, 'llm_model': model})
        result = return_json.get('response', '')
        return result

    def predict_vllm(self, message, limit=-1, history=None):
        # 新接口调用
        # 老接口调用 chat(self, text, model, limit=-1)
        ip = self.config_map["llm_qc_model"]["qc_chat_ip"]
        port = self.config_map["llm_qc_model"]["qc_chat_port"]
        qc_chat_model = self.config_map["llm_qc_model"]["qc_chat_model"]
        chat_url = 'http://{}:{}/v1/chat/completions'.format(ip, port)

        # 超过长度不调用大模型预测
        if limit != -1 and len(message) > limit:
            result = self.config_map["llm_qc_model"]["text_limit_str"]
            return result

        data = {
            'model': qc_chat_model,
            'messages': [{
                'role': 'user',
                'content': message
            }],
            'temperature': 0.01,
            'top_p': 0.01,
            'n': 1,
            'max_tokens': 1024,
            'stream': False
        }
        result = post_url(chat_url, data)

        content = result['choices'][0]['message']['content']

        return content

    def predict(self, message, limit=-1, history=None):
        # 新接口调用
        # 老接口调用 chat(self, text, model, limit=-1)
        ip = self.config_map["llm_qc_model"]["qc_chat_ip"]
        port = self.config_map["llm_qc_model"]["qc_chat_port"]
        qc_chat_model = self.config_map["llm_qc_model"]["qc_chat_model"]
        chat_url = 'http://{}:{}/v1/chat/completions'.format(ip, port)

        # 超过长度不调用大模型预测
        if limit != -1 and len(message) > limit:
            result = self.config_map["llm_qc_model"]["text_limit_str"]
            return result

        data = {
            'model': qc_chat_model,
            'messages': [{
                'role': 'user',
                'content': message
            }],
            'tools': [],
            'do_sample': False,
            'temperature': 0,
            'top_p': 0,
            'n': 1,
            'stream': False
        }
        result = post_url(chat_url, data)

        content = result['choices'][0]['message']['content']

        return content

    def predict_QwenApi(self, message, limit=-1, history=None):

        # 超过长度不调用大模型预测
        if limit != -1 and len(message) > limit:
            result = self.config_map["llm_qc_model"]["text_limit_str"]
            return result

        kwargs = {}
        res = {}
        kwargs["do_sample"] = False
        kwargs["temperature"] = 0.
        response = dashscope.Generation.call(
            # dashscope.Generation.Models.qwen_turbo,
            model='qwen-turbo-0624',
            prompt=message,
            history=history,
            **kwargs
        )
        # The response status_code is HTTPStatus.OK indicate success,
        # otherwise indicate request is failed, you can get error code
        # and message from code and message.
        if response.status_code == HTTPStatus.OK:
            res = response.output
        else:
            print(response.code)  # The error code.
            print(response.message)  # The error message.

        return res["text"] if "text" in res else ""
