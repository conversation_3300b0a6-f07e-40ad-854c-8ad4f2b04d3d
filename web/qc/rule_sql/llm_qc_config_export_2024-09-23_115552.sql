CREATE TABLE `llm_qc_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `rule_id` varchar(50) NOT NULL,
  `rule_name` varchar(100) NOT NULL,
  `few_shot` mediumtext,
  `question` mediumtext,
  `status` tinyint(4) DEFAULT '1',
  `remark` text,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 18:02:27', '## 判断方法： \\n要判断病历内容是否符合“微生物培养结果需要明确记录在病程记录中”的原则，可参考以下步骤：\\n  \\nStep 1：请摘录“微生物检查结果”中微生物培养的具体日期、检测项目和结果。\\n  \\nStep 2：转到“病程记录”部分，查找是否有对应微生物培养结果的记录。 \\n- 如果微生物培养结果为异常，应在病程记录中明确记录异常结果，如“找到XX细菌”、“XX真菌生长”等。请摘录病程记录中与微生物培养异常结果相关的描述。 \\n- 如果微生物培养结果为正常，则允许使用“未见异常”、“无细菌生长”、“阴性”等描述。请摘录病程记录中与微生物培养项目正常结果相关的描述。\\n  \\nStep 3：根据 Step 1 和 Step 2 中的发现，进行以下判断： \\n- 如果“微生物检查结果”中的结果（包含异常结果和正常结果），在“病程记录”中没有对应的记录或记录不完整，则违反上述原则。 \\n- 如果“微生物检查结果”中的结果（包含异常结果和正常结果），在“病程记录”中都有对应的明确记录，则符合上述原则。\\n  \\n总之，审核的重点是确认微生物培养结果是否在病程记录中得到明确记录，以确保病历信息的完整性和连续性。\\n  \\n## 病历内容： \\n{nh99输入内容}', 1, '问题: \\n{} \\n细菌培养结果是否在病程记录中有对应记录 \\n回答', NULL, 'nh99', '细菌培养结果需要在病程记录中有对应记录', 1, '2024-09-20 19:08:54');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 18:56:15', '## 判断方法： \\n要判断病历内容是否符合“做了病理检查的患者，需要在手术记录中有标本送检病理的相关记录”的原则，可按以下步骤进行：\\n  \\nStep1：在“手术记录”部分，查找并摘录有关“送检”的描述，如“取活检标本送检病理”、“标本送检”、“病理诊断：已送未回”。\\n  \\nStep2：根据Step1的摘录结果，进行以下判断： \\n- 如果在“手术记录”中找到了送检病理标本的记录，则符合上述原则；如果“手术记录”中只有病理检查的结果，但未明确记录“送检”这一步骤或动作信息，则认为是违反上述原则。 \\n- 如果在“手术记录”中没有找到送检病理标本的记录，则也违反上述原则。\\n  \\n总之，审核的关键是确认手术记录中是否明确记录了对标本送检这一信息，和是否有病理结果无关。\\n  \\n## 病历内容： \\n{nh98输入内容}\\n  \\n你是质控专家，请从日常工作中审核该规则的角度，给出回答。一步一步分析,给出总结', 2, '问题: \\n{} \\n手术存在病理检查的患者是否在手术记录中记录取材情况 \\n回答', NULL, 'nh98', '手术存在病理检查的患者需要在手术记录中记录取材情况', 1, '2024-09-20 18:56:15');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 19:07:11', '## 判断方法： \\n要判断病历内容是否符合“住院患者应用抗菌药物后，病程记录中应包含对应药品名称”的原则，可参考以下步骤：\\n  \\nStep 1：在“抗生素医嘱”中，识别住院期间使用的抗菌药物（包括抗生素、抗真菌药物等）。摘录相关抗菌药物的名称和使用日期。\\n  \\nStep 2：转到“病程记录”部分，查找上级医生在会诊/查房记录中是否提及了 Step 1 中识别出的抗菌药物名称。这可能包括但不限于以下情况： \\n- 直接明确地记录抗菌药物使用情况，如“给予甲硝唑治疗，患者耐受性良好”或“使用阿莫西林后，腹泻情况明显改善”等。 \\n- 对于长期使用的抗菌药物，上级医生可能在病程记录中进行过评估或讨论，如“患者使用呼吸道复方制剂已一周，症状得到控制，计划继续观察几日后再考虑停药”等。 \\n如有，请摘录相关讨论或记录。\\n  \\nStep 3：根据 Step 1 和 Step 2 中的信息，进行以下判断： \\n- 如果在“抗生素医嘱”中发现了抗菌药物使用，而“病程记录”中上级医生查房记录中没有提及任何对应的抗菌药物名称或讨论，则违反上述原则。 \\n- 如果在“抗生素医嘱”中没有发现抗菌药物使用，则不违反上述原则，因为原则提及的是住院患者应用抗菌药物后的记录要求。\\n  \\n审核时，可适当放宽细节要求，考虑以下几点： \\n- 如果上级医生在病程记录中没有直接使用抗菌药物的药品名称，但提到了通用名或类别，如“使用广谱抗生素”或“口服头孢类药物”，也可视为符合原则。 \\n- 对于多次会诊/查房记录，不必要求每次都重复记录抗菌药物名称，但至少应在首次使用和后续有需要讨论或调整时提及。\\n  \\n总之，审核的重点是确认上级医生是否在病程记录中关注并记录了住院期间使用的抗菌药物，以确保药物治疗的合理性和连续性。\\n  \\n##病历 \\n{nh120输入内容} \\n你是质控专家，请从日常工作中审核该规则的角度，给出回答。一步一步分析,给出总结', 3, '问题: \\n{} \\n住院患者应用抗菌药物后，对应药品名称、用法、剂量在病程记录中都有明确记录 \\n回答', NULL, 'nh120', '住院患者应用抗菌药物后，病程记录中缺少对应药品名称、用法、剂量', 1, '2024-09-20 19:14:06');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 19:08:41', '## 判断方法： \\n要判断病历内容是否符合“病程记录中需要明确记录患者接受了放射治疗”的原则，可参考以下步骤。\\n  \\n已知该病历的患者已经接受了放射治疗，请直接执行以下步骤进行判断：\\n  \\nStep 1：在“病程记录”部分，查找是否有明确记录患者接受放射治疗的描述。这需要是直接的表述，如“患者接受了放射治疗”、“放疗已完成”、“于明日行放疗”等。请注意，“计划进行放射治疗”或“拟行放疗”这类无明确日期的放疗计划描述，不认为有放疗记录，请忽略此类文本。如果存在明确的接受治疗记录，请摘录相关文本。\\n  \\nStep 2：根据 Step 1 的结果，进行以下判断 \\n- 如果在“病程记录”中有明确记录患者已接受放射治疗，则符合上述原则。注意，（1） 如果有“计划明日进行放射治疗”或“拟今日放疗”这类有明确日期的放疗计划描述，则也认为是符合原则；（2）根据语义，如果能得知已进行过放疗的，也认为是符合原则，如“继续放疗”、“放疗顺利”。\\n  \\n- 如果在“病程记录”中没有明确记录患者已接受放射治疗，则违反上述原则。注意，如果只有“计划进行放射治疗”或“拟行放疗”这类无明确日期的放疗计划描述，则也认为是违反原则。\\n  \\n审核时，请注意以下几点： \\n- 此判断方法的重点在于确认病程记录中是否有直接体现患者接受放射治疗的描述。 \\n- 在判断时，请勿对原文进行多余的推理或假设。判断是否违反原则应基于病历中已存在的文本信息。\\n  \\n##病历 \\n{nh121输入内容} \\n你是质控专家，请从日常工作中审核该规则的角度，给出回答。一步一步分析,给出总结', 4, '问题: \\n{} \\n住院患者接受了放射治疗，那么在病程记录是否有放疗相关的内容 \\n回答', NULL, 'nh121', '放射治疗情况在病程记录中需有相应记录', 1, '2024-09-20 19:14:06');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 19:08:41', '## 判断方法： \\n要判断病历内容是否符合“病程记录中需要明确记录医嘱名称中的药物名称”的原则，可参考以下步骤：\\n  \\nStep 1：在“医嘱名称”中，摘录所有的药物名称。\\n  \\nStep 2：转到“病程记录”部分，查找是否有记录医嘱中药物名称的文本。根据原则，记录的药物名称（或简称）应与医嘱中一致，或记录抗生素的具体类别，或记录该药物相应的商品名。例如，如果医嘱中使用了“注射用头孢曲松钠”，病程记录中应有药物名，或抗生素类别“头孢”或商品名“罗氏芬”的记录，而不能仅记录为“抗生素”。\\n  \\nStep 3：根据 Step 1 和 Step 2 中的发现，进行以下判断： \\n- 如果在“病程记录”中明确记录了相应的药物名称，或记录了药物的商品名，或记录了抗生素/抗菌药的类别，则符合上述原则。 \\n- 如果在“病程记录”中药物名称、药物的商品名、抗生素/抗菌药的类别都没有记录，则违反上述原则。 \\n如果仅仅记录为“抗生素”、“抗菌药”等通用词语，也认为是违反上述原则。\\n  \\n总之，审核的目的是确保医嘱名称中的所有药物，需要在病程记录中都有明确的记录。\\n  \\n## 病历内容： \\n{nh413输入内容}', 5, '问题: \\n{} \\n住院患者应用抗菌药物后，对应药品名称 在病程记录中都有明确记录 \\n回答', NULL, 'nh413', '住院患者应用抗菌药物后，病程记录中缺少对应药品名称', 1, '2024-09-20 19:14:06');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 19:08:41', '## 判断方法： \\n要判断病历内容是否符合“病程记录中需要明确记录患者接受了化疗”的原则，可按以下步骤进行：\\n  \\nStep 1：“医嘱名称”中的内容都是和化疗相关的，请摘录其中的内容。\\n  \\nStep 2：在“病程记录”中，搜索是否有明确记录患者接受化疗的文本。这可能包括以下情况： \\n- 直接描述患者已接受了化疗，如“患者接受了化疗”、“化疗顺利完成”等。 \\n- 记录了具体的化疗方案（如“患者接受了TP方案化疗”），或化疗药物名（如“给予多西他赛静脉注射治疗”）、或化疗药的商品名（如“拟明日行克艾力 2mg治疗”）等。 \\n如有，请摘录相关记录。\\n  \\nStep 3：根据 Step 1 和 Step 2 中的发现，进行判断： \\n- 如果在“病程记录”中明确直接地记录“医嘱名称”中的药品名称，则符合上述原则。 \\n- 如果在“病程记录”中有明确记录患者已接受化疗，比如“已行化疗”、“化疗完成”等描述，也符合上述原则。 \\n- 其他情况均认为是违反上述原则。注意，如果只有“计划行化疗治疗”或“拟行化疗”这类无明确日期的化疗计划描述，则也认为是违反原则。\\n  \\n请注意： \\n- 如果“医嘱名称”中化疗医嘱的描述较为具体，包含明确的化疗方案或药物名，而“病程记录”中虽然没有直接描述“接受化疗”，但提到了该方案或药物名，可视为符合原则。 \\n- 如果“病程记录”中记录了“化疗顺利完成”、“拟明日化疗”等描述，但没有具体方案或药物名，也可视为符合原则。 \\n审核时，请注意以下几点： \\n- 此判断方法的重点在于保证病程记录有直接体现患者已接受化疗的描述，或是记录了计划化疗，并且有具体的方案或化疗药物，而不是没有任何化疗相关的描述、或只是记录了计划化疗但没有具体的化疗方案或化疗计划。 \\n- 在判断时，请勿对原文进行多余的推理或假设。\\n  \\n## 病历内容： \\n{nh414输入内容}\\n  \\n你是质控专家，请从日常工作中审核该规则的角度，给出回答。一步一步分析,给出总结。', 6, '问题: \\n{} \\n住院患者应用化疗药物后，病程记录中是否缺少与化学治疗有关的内容 \\n回答', NULL, 'nh414', '住院患者应用化疗药物后，病程记录中缺少与化学治疗有关的内容', 1, '2024-09-20 19:14:06');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 19:08:41', '## 判断方法：\\n  \\nStep 1: 识别本次住院期间的CT或MRI检查报告，并摘录检查结论中的全部内容，包括异常和正常结果。\\n  \\nStep 2: 在病程记录中，查找是否有对应CT或MRI检查结果的记录。注意区分其他类型的检查结果，如超声检查。\\n  \\nStep 3: 比较Step 1和Step 2中的信息： \\n- 如果检查报告中的全部结论，包括异常和正常结果，都在病程记录中得到记录，且记录形式为CT或MRI的结果，则符合原则。 \\n- 如果检查报告中的结论没有在病程记录中完整记录，或有遗漏，或记录形式不正确（例如将正常结果记录为其他检查的结果），则违反原则。\\n  \\n在判断过程中，应注意以下几点： \\n- 允许将正常结果记录为“未见异常”等描述，但必须明确指出是CT或MRI检查的结果。 \\n- 应避免多余的推理，仅根据检查报告和病程记录中的信息进行判断。 \\n- 只关注本次住院期间的CT或MRI检查结果，注意区分并排除其他检查结果或外院检查结果。\\n  \\n如果存在以下情况，可视为符合原则： \\n- 检查报告中的结论在病程记录中有部分缺失，但缺失的内容不影响诊断或治疗决策。 \\n- 检查报告中的结论在病程记录中有略微的措辞差异，但语义上一致，且不影响理解。\\n  \\n总之，审核的要点是确保本次住院期间的CT或MRI检查结果在病程记录中得到完整、准确的记录，以保证病历的连续性和可读性。\\n  \\n## 病历内容：  \\n{pf2输入内容}', 7, '问题: \\n{} \\n患者病情评估至关重要的检查结果都需要记录到病程记录中 \\n回答', NULL, 'pf2', '查房记录中对重要检查结果进行记录', 1, '2024-09-23 11:54:57');
insert into `llm_qc_config` (`create_time`, `few_shot`, `id`, `question`, `remark`, `rule_id`, `rule_name`, `status`, `update_time`) values ('2024-09-20 19:08:41', '## 判断方法： \\n要判断病历内容是否符合“病理检查的全部结果，需要在病程记录中有明确记录”的原则，可按以下步骤进行：\\n  \\nStep 1：在“病理检查报告”中，摘录所有检查项目的名称和相应的结果。\\n  \\nStep 2：转到“病程记录”部分，查找是否有对应于Step 1中摘录的病理检查结果的记录。对于异常结果，应有明确的记录；对于正常结果，可以记录为“未见异常”、“无明显异常所见”等描述。\\n  \\nStep 3：根据Step 1和Step 2中的发现，进行以下判断： \\n- 如果“病理检查报告”中的检查结果，在“病程记录”中都有记录，则符合原则。 \\n- 如果“病理检查报告”中的检查结果，在“病程记录”中有遗漏或都没有记录，则违反原则。\\n  \\n请注意，在审核时，应关注以下几点： \\n- 确保“病程记录”中记录了所有异常结果。 \\n- 对于正常结果，允许使用简洁的描述，如“未见异常”，无需逐一列出每个正常项目的结果。\\n  \\n总之，审核的关键是确保“病程记录”中准确、完整地记录了“病理检查报告”中的结果，尤其是异常结果，以保证病历的完整性和医疗信息的连续性。\\n  \\n## 病历内容： \\n{pf9输入内容}\\n  \\n你是质控专家，请从日常工作中审核该规则的角度，给出回答。一步一步分析,给出总结', 8, '问题: \\n{} \\n所有的病理检查异常结果在“内容-病程记录”中都有明确记录 \\n回答', NULL, 'pf9', '查房记录中对重要病理检查结果进行记录', 1, '2024-09-20 19:14:06');
