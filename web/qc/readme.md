
### 0. 创建hm_llm 数据库

### 1. llm_qc 建表语句
```mysql
CREATE TABLE `llm_qc` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `prompt_hash` varchar(100) DEFAULT NULL COMMENT 'prompt_hash',
  `prompt` text COMMENT 'prompt',
  `answer` text COMMENT 'answer',
  `uuid` varchar(50) DEFAULT NULL COMMENT '唯一标识',
  `lora_model` varchar(32) DEFAULT NULL COMMENT 'lora_model',
  `customer_id` int(11) DEFAULT NULL COMMENT '医院id',
  `record_id` int(11) DEFAULT NULL COMMENT 'record_id',
  `answer_type` varchar(32) DEFAULT NULL COMMENT 'answer_type',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  <PERSON>EY `promp_hash_key` (`prompt_hash`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='大模型-qc'
```
```
CREATE TABLE IF NOT EXISTS llm_qc_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    rule_id VARCHAR(50) NOT NULL COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    few_shot MEDIUMTEXT COMMENT 'prompt',
    question MEDIUMTEXT COMMENT '问题描述',
    status TINYINT DEFAULT 1 COMMENT '状态（1: 启用, 0: 禁用）',
    remark TEXT COMMENT '备注信息',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC 
COMMENT='病历质控大模型规则配置';
```
CREATE TABLE `llm_qc_v2_response` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL COMMENT '医院id',
  `record_id` int(11) DEFAULT NULL COMMENT 'record_id',
  `rule_id` varchar(32) DEFAULT NULL COMMENT '规则id',
  `input` text COMMENT '大模型输入',
  `prompt_hash` varchar(255) NOT NULL COMMENT 'prompt_hash',
  `question` text COMMENT '大模型输入',
  `answer` text COMMENT '是否缺陷的结论',
  `result` text COMMENT '大模型回答结果',
  `create_date` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_date` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=387655 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='大模型-qc结果';
```

### 2. 大模型质控服务启动
（1）配置文件

配置 `web/config.ini`和`web/config_company.ini`，主要有两个：
`qc_chat_ip`和`qc_chat_port`为大模型chat服务url，`hm_llm`里面配置数据库相关信息


（2）启动大模型服务
```shell
model_path = /home/<USER>/Qwen-14B-Chat
cd aigc_hospital/training/sft
python src/stream_api_qc.py --checkpoint-path $model_path
```
（3）启动qc服务
```shell
cd 根目录
sh sh/restart_aigc_api_service.sh
```


### 3.质控数据请求串
```
{
    "request": [
        {
            "customer_id": 1001,
            "record_id": 3195982,
            "rule_id": "nh182",
            "data": [
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "初步诊断",
                    "progressId": 9999402,
                    "content": "初步诊断:胃恶性肿瘤 "
                },
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "主诉",
                    "progressId": 9999402,
                    "content": "主 诉:梗阻性黄疸ptcd术后2月 "
                }
            ]
        }
    ]
}
```


```
{
    "request": [
        {
            "customer_id": 1001,
            "record_id": 3125986,
            "rule_id": "nh190",
            "data": [
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "主诉",
                    "progressId": 9999412,
                    "content": "主 诉:发现皮疹45分钟"
                },
                {
                    "record_type_name": "入院记录",
                    "attribute_name": "现病史",
                    "progressId": 9999406,
                    "content": "现病史:患儿45分钟前踢足球后出现颜面部肿胀,伴胸闷、鼻塞、呼吸困难、腹痛,无声嘶咳嗽,无呕吐腹泻,无咳嗽流涕,无发热等不适。为进一步诊治,120转至我院急诊,急诊拟“过敏反应”收住留观。 患儿起病以来,神志清,精神软,胃纳欠佳,未入睡,大小便无殊,体重未见明显增减。"
                }
            ]
        }
    ]
}
```


```
{
  "request": [
    {
      "customer_id": 1001,
      "record_id": 3195980,
      "rule_id": "nh6",
      "data": [
        {
          "record_type_name": "入院记录",
          "attribute_name": "初步诊断",
          "progressId": 9999401,
          "content": "初步诊断: 恶性肿瘤维持性化学治疗 "
        },
        {
          "record_type_name": "入院记录",
          "attribute_name": "主诉",
          "progressId": 9999401,
          "content": "主 诉: 左颞部头皮疼痛1年余 "
        },
        {
          "record_type_name": "入院记录",
          "attribute_name": "体格检查",
          "progressId": 9999401,
          "content": "体格检查体温:-°c 脉搏:-次/分 呼吸:-次/分 血压:-mmhg 一般情况:- 皮肤黏膜:- 淋巴结:- 头部及其它器官:- 颈部:- 胸部:- - "
        },
        {
          "record_type_name": "入院记录",
          "attribute_name": "现病史",
          "progressId": 9999401,
          "content": "现病史:患者自发病以来神志-,精神-,饮食-,大便-,体重- "
        },
        {
          "record_type_name": "入院记录",
          "attribute_name": "既往史",
          "progressId": 9999401,
          "content": "既往史:平素健康状况良好,否认肝炎,结核病传染史,否认高血压、否认冠心病等心血管病史,否认糖尿病史 "
        },
        {
          "record_type_name": "入院记录",
          "attribute_name": "辅助检查",
          "progressId": 9999401,
          "content": "辅助检查- "
        }
      ]
    }
  ]
}
```
被 *********************:huimei/algorithm/aigc_post_process.git 项目依赖
