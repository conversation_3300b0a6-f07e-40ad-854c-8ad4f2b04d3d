# -*- coding: utf-8 -*-
from web.qc.qc_rule.nh182 import Nh182Flow
from web.qc.qc_rule.nh269 import Nh269Flow
from web.qc.qc_rule.nh60 import Nh60Flow
from web.qc.qc_rule.nh190 import Nh190Flow
from web.qc.qc_rule.nh6 import Nh6Flow
from web.qc.qc_rule.pf2 import Pf2Flow
from web.qc.qc_rule.pf9 import Pf9Flow
from web.qc.qc_rule.nh154 import Nh154Flow
from web.qc.qc_rule.pfw1 import Pfw1Flow
from web.qc.qc_rule.pfw2 import Pfw2Flow
from web.qc.qc_rule.pfw3 import Pfw3Flow
from web.qc.qc_rule.pfw5 import Pfw5Flow
from web.qc.qc_rule.nh54 import Nh54Flow
from web.qc.qc_rule.nh155 import Nh155Flow
from web.qc.qc_rule.nh258 import Nh258<PERSON>low
from web.qc.qc_rule.syqt42 import Syqt42<PERSON>low
from web.qc.qc_rule.pfw9 import Pfw9Flow
from web.qc.qc_rule.pfw13 import Pfw13Flow
from web.qc.qc_rule.nh120 import Nh120Flow
from web.qc.qc_rule.nh234 import Nh234Flow
from web.qc.qc_rule.nh296 import Nh296Flow
from web.qc.qc_rule.pfw16 import Pfw16Flow
from web.qc.qc_rule.pfw19 import Pfw19Flow
from web.qc.qc_rule.pfw27 import Pfw27Flow
from web.qc.qc_rule.pfw32 import Pfw32Flow
from web.qc.qc_rule.nh10 import Nh10Flow
from web.qc.qc_rule.pf3 import Pf3Flow
from web.qc.qc_rule.pf4 import Pf4Flow
from web.qc.qc_rule.pf8 import Pf8Flow
from web.qc.qc_rule.pf13 import Pf13Flow
from web.qc.qc_rule.nh121 import Nh121Flow
from web.qc.qc_rule.nh413 import Nh413Flow
from web.qc.qc_rule.nh414 import Nh414Flow
from web.qc.qc_rule.nh98 import Nh98Flow
from web.qc.qc_rule.nh99 import Nh99Flow
from web.qc.qc_rule.pf6 import Pf6Flow
from web.qc.qc_rule.llm40 import Llm40Flow
from web.qc.qc_rule.llm41 import Llm41Flow
from web.qc.qc_rule.llm42 import Llm42Flow
from web.qc.qc_rule.llm43 import Llm43Flow

from web.qc.qc_util import *
import os
from web.config import *
import configparser
from web.web_utils.mt_data_util import DataUtil
import json

class qc_v2:
    config_map = config_map
    data_util = DataUtil()
    debug_print = True
    def __init__(self):
        self.rule_flow = {
            "nh182": Nh182Flow(),
            "nh190": Nh190Flow(),
            "nh269": Nh269Flow(),
            "nh60": Nh60Flow(),
            "nh6": Nh6Flow(),
            "pf2": Pf2Flow(),
            "pf6": Pf6Flow(),
            "pf8": Pf8Flow(),
            "pf9": Pf9Flow(),
            "pf13": Pf13Flow(),
            "nh154": Nh154Flow(),
            "pfw1": Pfw1Flow(),
            "pfw2": Pfw2Flow(),
            "pfw3": Pfw3Flow(),
            "pfw5": Pfw5Flow(),
            "nh54": Nh54Flow(),
            "nh155": Nh155Flow(),
            "nh258": Nh258Flow(),
            "syqt42": Syqt42Flow(),
            "pfw9": Pfw9Flow(),
            "pfw13": Pfw13Flow(),
            "nh120": Nh120Flow(),
            "nh234": Nh234Flow(),
            "nh296": Nh296Flow(),
            "pfw16": Pfw16Flow(),
            "pfw19": Pfw19Flow(),
            "pfw27": Pfw27Flow(),
            "nh10": Nh10Flow(),
            "pfw32": Pfw32Flow(),
            "pf3": Pf3Flow(),
            "pf4": Pf4Flow(),
            "nh121": Nh121Flow(),
            "nh413": Nh413Flow(),
            "nh414": Nh414Flow(),
            "nh98": Nh98Flow(),
            "nh99": Nh99Flow(),
            "llm40": Llm40Flow(),
            "llm41": Llm41Flow(),
            "llm42": Llm42Flow(),
            "llm43": Llm43Flow(),
        }

    # LLM生成qc v2 answer
    def generate_qc_answer(self, data):
        if not data or not isinstance(data, dict) or "request" not in data or len(data["request"]) == 0:
            return {
                "code": 10001,
                "status": 400,
                "msg": "Invalid input"
            }
        req_datas = data["request"]
        rule_id = 0
        for _req_data in req_datas:
            rule_id = _req_data.get("rule_id")
            if rule_id not in self.rule_flow:
                return {
                    "code": 10001,
                    "status": 400,
                    "msg": f"Invalid rule_id:{rule_id}"
                }
            break
        qc_rule = self.rule_flow[rule_id]
        response = qc_rule.work_flow(req_datas)
        return {
            "code": 0,
            "status": 200,
            "msg": "success",
            "response": response
        }


    def get_answer_promt_v2(self,data):
        result = data.strip()
        if "</think>" in result:
            result = result.split("</think>")[-1].strip()

        pred = '无法提取有效结论'
        gpt_response = (result.replace("确定是否违反原则", "").replace("确定是否符合原则", "").
                        replace("结论可靠", "").replace("检查结论", "")
                        .replace("支持这一结论", "").replace("的结论", "").replace("最终结论", "").replace(
            "为了给出准确结论",
            "").replace("那么结论", ""))
        '''
        gpt_response = "\n".join([x for x in gpt_response.split("\n") if x.find("Step") < 0])
        if "可以视为符合原则" in gpt_response:
            ideal = "无缺陷"
            pred = "符合"
        el
        '''
        gpt_response = gpt_response.split("结论")[-1].strip()
        if "无法判断是否违反" in gpt_response or "无法判断是否符合" in gpt_response or "无法确定是否符合" in gpt_response \
                or "无法确定是否违反" in gpt_response:
            pred = "无缺陷"
        elif "违反" in gpt_response and "不违反" not in gpt_response and "未违反" not in gpt_response \
                and "没有发现违反" not in gpt_response \
                and "未发现违反" not in gpt_response \
                and "不视为违反" not in gpt_response \
                and "没有违反" not in gpt_response:

            pred = "有缺陷"
        elif "失败" in gpt_response and "成功" not in gpt_response:
            pred = "有缺陷"
        elif "不满足" in gpt_response and "满不满足" not in gpt_response:
            pred = "无缺陷"
        elif "不违反" in gpt_response or "未违反" in gpt_response \
                or "没有发现违反" in gpt_response \
                or "未发现违反" in gpt_response \
                or "没有发现违反" in gpt_response:
            pred = "无缺陷"
        elif "符合" in gpt_response and "不符合" not in gpt_response:
            pred = "无缺陷"
        elif "成功" in gpt_response:
            pred = "无缺陷"
        elif "不纳入判断" in gpt_response:
            pred = "无缺陷"
        elif "满足" in gpt_response and "不满足" not in gpt_response:
            pred = "无缺陷"
        elif "有缺陷" in gpt_response and "无缺陷" not in gpt_response:
            pred = "有缺陷"
        elif "无缺陷" in gpt_response and "有缺陷" not in gpt_response:
            pred = "无缺陷"
        if pred == "无法提取有效结论":
            print("无法提取有效结论")
            print(result)
            print(gpt_response)
        return pred

    @classmethod
    def get_patient_data(cls, data):
        if cls.debug_print:
            logger.info("--------------------------------")
            logger.info("input_data")
            logger.info(data)
            logger.info("--------------------------------")

        customer_id, record_id, metadata = data.get("customer_id"), data.get("record_id"), data.get("metadata")
        progress_data =  cls.data_util.get_patient_data(customer_id, record_id, metadata)
        progress_str_data = cls.format_patient_data_as_string(progress_data)
        if cls.debug_print:
            logger.info("--------------------------------")
            logger.info("input_data")
            logger.info(data)
            logger.info("--------------------------------")
            logger.info("progress_data")
            logger.info(progress_data)
            logger.info("--------------------------------")
            logger.info("progress_str_data")
            logger.info(progress_str_data)
            logger.info("--------------------------------")
        return progress_str_data

    @classmethod
    def format_patient_data_as_string(cls, progress_data):
        """
        将患者数据格式化为字符串

        Args:
            progress_data: 包含患者数据的字典

        Returns:
            格式化后的患者数据字符串
        """
        result = []

        for record_type_name, records in progress_data.items():
            if not records:
                continue
            result.append(f"【{record_type_name}】")

            for record in records:
                for key, value in record.items():
                    if key.startswith('^'):
                        value = cls.data_util.make_parse_content(key[1:], value)
                        result.append(value)
                    elif value is not None and value != "":
                        value = f"{key}: {value}"
                        result.append(value)
                result.append("")  # 添加空行分隔不同记录

        return "\n".join(result)

    def generate_stream(self, qc_data, progress_str_data=None):
        """
        生成流式响应的方法

        Args:
            qc_data: 包含质控规则和相关信息的字典
            patient_data: 患者数据（可选）

        Yields:
            流式响应的文本片段
        """
        # 大模型qps统计
        from web.prometheus_monitor import g_monitor, QUALITY_CONTROL
        g_monitor.set_prometheus_qps_count(QUALITY_CONTROL)

        prompt = qc_data['logic']
        output_requirement = qc_data['output_requirement']

        # 如果有患者数据，将其格式化并添加到提示中
        if progress_str_data:
            prompt = f"患者数据:\n{progress_str_data}\n\n质控规则:\n{prompt}\n\n输出要求:\n{output_requirement}"

        if self.debug_print:
            print("--------------------------------")
            print("prompt")
            print(prompt)
            print("--------------------------------")
        # Prepare messages for the model
        messages = [
            {"role": "system", "content": "你是惠每云科技开发的临床质控助手，你可以辅助临床医生完成质控过程中的相关问题。"},
            {"role": "user", "content": prompt}
        ]

        # Set up model connection
        from openai import OpenAI
        import time
        from loguru import logger

        openai_api_key = "9311063c-efe8-4439-be52-f23c3504245d"
        openai_api_base = self.config_map['openai']['openai_api_base']
        qc_chat_model = self.config_map["openai"]["qc_chat_model"]


        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
        )
        req_length = 0
        resp_length = 0
        for msg in messages:
            req_length += len(msg.get("content", ""))

        start_time = time.time()
        # 用于收集完整的输出内容
        full_content = ""

        try:
            yield "<think>开始分析</think><content>"
            for chunk in client.chat.completions.create(
                model=qc_chat_model,
                messages=messages,
                top_p=0.01,
                stream=True
            ):
                if hasattr(chunk.choices[0].delta, "content") and chunk.choices[0].delta.content:
                    output = str(chunk.choices[0].delta.content)
                    # 累加到完整内容
                    full_content += output
                    resp_length += len(output)
                    # Stream the output
                    yield output

            # 处理完整的输出内容
            logger.info(f"Full response content: {full_content}")

        except Exception as e:
            logger.error(f"QC model query Error: {str(e)}")
            g_monitor.set_prometheus_fail_count(QUALITY_CONTROL)
            yield f"<result>失败: {str(e)}</result>"
            return

        time_last = time.time() - start_time
        # 统计耗时和输入输出
        g_monitor.set_model_time_historgram(QUALITY_CONTROL, time_last)
        g_monitor.set_model_length_summary(QUALITY_CONTROL, req_length, resp_length)

        # 根据full_content分析结果
        if "结论" not in full_content:
            full_content = "结论\n" + full_content
        result = self.get_answer_promt_v2(full_content)

        yield f"</content>\n<result>{result}</result>"

    def generate_response(self, qc_data, progress_str_data=None):
        """
        生成普通同步响应的方法

        Args:
            qc_data: 包含质控规则和相关信息的字典
            progress_str_data: 患者数据（可选）

        Returns:
            包含完整响应内容和结果的字典
        """
        # 大模型qps统计
        from web.prometheus_monitor import g_monitor, QUALITY_CONTROL
        g_monitor.set_prometheus_qps_count(QUALITY_CONTROL)

        prompt = qc_data['logic']
        output_requirement = qc_data['output_requirement']

        # 如果有患者数据，将其格式化并添加到提示中
        if progress_str_data:
            prompt = f"患者数据:\n{progress_str_data}\n\n质控规则:\n{prompt}\n\n输出要求:\n{output_requirement}"

        if self.debug_print:
            print("--------------------------------")
            print("prompt")
            print(prompt)
            print("--------------------------------")

        # Prepare messages for the model
        messages = [
            {"role": "system", "content": "你是惠每云科技开发的临床质控助手，你可以辅助临床医生完成质控过程中的相关问题。"},
            {"role": "user", "content": prompt}
        ]

        # Set up model connection
        from openai import OpenAI
        import time
        from loguru import logger

        openai_api_key = "9311063c-efe8-4439-be52-f23c3504245d"
        openai_api_base = self.config_map['openai']['openai_api_base']
        qc_chat_model = self.config_map["openai"]["qc_chat_model"]

        client = OpenAI(
            api_key=openai_api_key,
            base_url=openai_api_base,
        )

        req_length = 0
        resp_length = 0
        for msg in messages:
            req_length += len(msg.get("content", ""))

        start_time = time.time()

        try:
            response = client.chat.completions.create(
                model=qc_chat_model,
                messages=messages,
                top_p=0.01,
            )

            full_content = response.choices[0].message.content
            resp_length = len(full_content)

            # 处理完整的输出内容
            logger.info(f"Full response content: {full_content}")

            # 根据full_content分析结果
            if "结论" not in full_content:
                full_content = "结论\n" + full_content
            result = self.get_answer_promt_v2(full_content)

            time_last = time.time() - start_time
            # 统计耗时和输入输出
            g_monitor.set_model_time_historgram(QUALITY_CONTROL, time_last)
            g_monitor.set_model_length_summary(QUALITY_CONTROL, req_length, resp_length)

            return {
                "code": 0,
                "status": 200,
                "msg": "success",
                "content": full_content,
                "result": result
            }

        except Exception as e:
            logger.error(f"QC model query Error: {str(e)}")
            g_monitor.set_prometheus_fail_count(QUALITY_CONTROL)
            return {
                "code": 10001,
                "status": 500,
                "msg": f"失败: {str(e)}",
                "content": "",
                "result": "无法提取有效结论"
            }

if __name__ == '__main__':
    qc_v2 = qc_v2()
    qc_v2.get_answer_promt_v2('''<think>开始分析</think><content>分析结果：
1. 患者的血压记录完整，记录值为200/80mmHg。
2. 根据正常血压范围（收缩压90-140mmHg，舒张压60-90mmHg），患者的收缩压200mmHg明显高于正常范围，属于高血压。

结论：有缺陷。</content>
<result>无法提取有效结论</result>''')
