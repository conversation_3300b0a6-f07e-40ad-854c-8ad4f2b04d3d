# coding:utf-8
from web.web_utils.sql_util import select_by_sql, sql_query
from web.web_utils.db_util import execute_sql_many
from web.web_utils.text_util import get_uuid
from web.mrg_service.discharge_summary_llm import *
import json
import numpy as np
import pandas as pd
from web.mrg_service.cdss_llm_input import get_text_parse


def get_task_data(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    prompt_type = request_data.get('prompt_type', '').strip()
    task_list = get_task(prompt_type, config_map)
    return task_list

def get_task(prompt_type, config_map):
    sql = '''
                    select tt.uuid task_uuid,tt.prompt_type,tt.task_name,tt.record_ids,tt.create_date,
    dd.id task_detail_id,dd.customer_id,dd.record_id,dd.label_flag,dd.modify_date task_detail_modify_date
     from label_task tt inner join label_task_detail dd on tt.uuid=dd.task_uuid
     where tt.prompt_type='{}' order by tt.create_date desc
                		 '''.format(prompt_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    task_list = []
    uuid2task = {}
    for x in xs:
        x['create_date'] = str(x['create_date'])
        label_flag_str = {0: '未标注', 1: 'llm_answer好', 2: 'human_answer好', 3: '平手'}[x['label_flag']]
        task_detail_str = '<tr><td style=\'border-left:solid 1px #ccc;\'>{}</td><td>{}</td><td>{}</td><td>{}</td><td>{}</td><td>{}</td></tr>'.format(
            x['task_name'], x['create_date'], x['record_id'], label_flag_str, x['task_detail_modify_date'],
            '<a href=\'/to_task_detail?task_detail_id={}&customer_id={}&record_id={}\' target=\'_blank\'>标注</a>'.format(
                x['task_detail_id'], x['customer_id'], x['record_id']))
        task_uuid = x['task_uuid']
        if task_uuid not in uuid2task:
            x['task_detail_str'] = task_detail_str
            task_list.append(x)
            uuid2task[task_uuid] = 1
        else:
            task_list[-1]['task_detail_str'] += task_detail_str
    return task_list

def run_task(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    prompt_type = request_data.get('prompt_type', '').strip()
    is_rank = request_data.get('is_rank', '').strip()
    task_name = request_data.get('task_name', '').strip()
    start_num = request_data.get('start_num', '').strip()
    total_num = request_data.get('total_num', '').strip()
    cid = request_data.get('customer_id', '1001').strip()
    record_ids = request_data.get('record_ids', '').strip()
    prompt = request_data.get('prompt', '').strip()
    task_detail_xs = []
    task_uuid = get_uuid()
    record_id_list = []
    inpatient_department = ''
    instruction = prompt
    # 生成出院小结
    if record_ids:
        record_id_list = [int(r) for r in record_ids.split(',')]
    else:
        sql = '''select rr.id record_id,rr.patient_guid,rr.customer_id,rr.patient_age,rr.patient_age_type,rr.inpatient_department,rr.patient_gender,date_format(dd.discharge_time,'%Y-%m-%d %H:%i:%s') discharge_time from mt_patient_record rr
                    inner join mt_patient_discharge_record dd on dd.record_id=rr.id
                inner join mt_patient_progress pp on pp.record_id=rr.id
            where rr.customer_id={} and rr.record_type = 2 and pp.progress_type=10 
        '''.format(cid)
        xs = sql_query(cid, sql, config_map)
        record_id_list = []
        for x in xs:
            record_id_list.append(int(x['record_id']))
            cid = x['customer_id']
            inpatient_department = x['inpatient_department']
        if is_rank=='1':
            np.random.shuffle(record_id_list)
        if total_num:
            if start_num:
                record_id_list = record_id_list[int(start_num):int(total_num)]
            else:
                record_id_list = record_id_list[:int(total_num)]
        record_ids = ','.join([str(rid) for rid in record_id_list])

    prompt_map2 = {prompt_type: prompt}
    if not prompt:
        prompt_map = get_prompt_map('出院小结', config_map)
        prompt_map2 = {prompt_type: prompt_map[prompt_type]}
        instruction = prompt_map[prompt_type]
    for rid in record_id_list:
        discharge_time = find_cysj(cid, rid, config_map)
        raw_data = download_data(cid, rid, config_map)
        data = pd.DataFrame(raw_data)
        cyjl_map = get_cyjl_origin(data, config_map)
        human_answer = cyjl_map.get(prompt_type, '')
        new_prompt_map, prompt_answer_map, prompt_progress_map = update_prompt_map(prompt_map2, cid, rid, discharge_time,data, inpatient_department,
                                                              config_map, need_input=1)
        if new_prompt_map and not prompt_answer_map[prompt_type]:
            asyncio.run(get_llm_answer(new_prompt_map, prompt_answer_map))
        new_prompt_answer_map = update_prompt_answer_map(prompt_answer_map)
        task_detail_xs.append([task_uuid, prompt_type, new_prompt_map[prompt_type], new_prompt_answer_map[prompt_type], human_answer, cid, rid])

    task_xs = [[task_uuid, prompt_type, task_name, cid, record_ids,instruction]]
    sql = '''
                INSERT INTO label_task (uuid, prompt_type, task_name,customer_id, record_ids,instruction) VALUES  (%s, %s,%s, %s, %s, %s);
        		 '''
    ret = execute_sql_many(sql, task_xs, 'hm_llm', config_map)
    sql = '''
                    INSERT INTO label_task_detail (task_uuid, prompt_type, prompt, llm_answer,human_answer,customer_id, record_id) VALUES  (%s, %s, %s, %s, %s, %s, %s);
            		 '''
    ret = execute_sql_many(sql, task_detail_xs, 'hm_llm', config_map)

    # task_list = get_task(prompt_type, config_map)
    msg = '任务已完成，请查看'
    return msg


def label_task_detail(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    task_detail_id = request_data.get('task_detail_id', '')
    label_flag = request_data.get('label_flag', '')
    label_remark = request_data.get('label_remark', '')
    label_answer = request_data.get('label_answer', '')
    msg = '标注成功！'
    if label_flag:
        sql = '''
                    update label_task_detail set label_flag={} where id={};
                     '''.format(label_flag, task_detail_id)
        ret = execute_sql(sql, 'hm_llm', config_map)
    elif label_remark or label_answer:
        sql = '''
                    update label_task_detail set remark='{}', label_answer='{}' where id={};
                     '''.format(label_remark, label_answer, task_detail_id)
        ret = execute_sql(sql, 'hm_llm', config_map)
        msg = '备注成功！'
    return msg

def get_task_detail_cyjl(request_data, config_map):
    customer_id = request_data.get('customer_id', '').strip()
    record_id = request_data.get('record_id', '').strip()
    task_detail_id = request_data.get('task_detail_id', '').strip()
    customer_id = int(customer_id)
    record_id = int(record_id)
    generate_cyjl_list = []
    sql = '''
     select  dd.id,dd.customer_id,dd.record_id,dd.label_flag,dd.prompt,dd.llm_answer,dd.human_answer,IFNULL(dd.label_answer, '') label_answer,dd.prompt_type,IFNULL(dd.remark,'') label_remark,date_format(dd.modify_date,'%Y-%m-%d %H:%i:%s') task_detail_modify_date
     from label_task_detail dd where dd.id={}
                		 '''.format(task_detail_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    task_detail = {}
    if len(xs)>0:
        generate_cyjl_list.append({'progress_type_zh': '指令', 'progress_text': xs[0]['prompt'].replace('\n','</br>'), 'label_flag': 0})
        temp_list = [{'progress_type_zh': '答案', 'progress_text': str(xs[0]['llm_answer']).replace('\n','</br>'), 'label_flag': 1},{'progress_type_zh': '答案', 'progress_text': str(xs[0]['human_answer']).replace('\n','</br>'), 'label_flag': 2}]
        np.random.shuffle(temp_list)
        temp_list[0]['progress_type_zh'] = '答案A'
        temp_list[1]['progress_type_zh'] = '答案B'
        generate_cyjl_list.extend(temp_list)
        task_detail = xs[0]

    data_sql = sqls['chuyuan'].format(customer_id, record_id)
    xs = sql_query(customer_id, data_sql, config_map)
    origin_text_list = []
    if len(xs) > 0:
        origin_text_list.append({'progress_type_zh': '科室', 'progress_text': xs[0]['inpatient_department']})
    for x in xs:
        origin_cyjl = ''
        msg_type = x['msg_type']
        if msg_type == 2:
            data = json.loads(x['progress_text'])
            for it in data:
                origin_cyjl += '{}:{}'.format(it['key'], it['value']) + "\n"
        else:
            origin_cyjl = x['progress_text']
        if x['progress_type'] ==1:
            origin_cyjl = parse_text(1, origin_cyjl)
            origin_text_list.append({'progress_type_zh': '{}-{}</br>{}'.format(x['progress_type_zh'], x['progress_type'],x['record_time_format']), 'progress_text': origin_cyjl})
        elif x['progress_type'] ==10:
            origin_cyjl = parse_text(10, origin_cyjl)
            origin_text_list.append({'progress_type_zh': '{}-{}</br>{}'.format(x['progress_type_zh'], x['progress_type'],x['record_time_format']), 'progress_text': origin_cyjl})
        else:
            origin_text_list.append({'progress_type_zh': '{}-{}</br>{}'.format(x['progress_type_zh'], x['progress_type'],x['record_time_format']), 'progress_text': origin_cyjl.replace('\n','</br>')})
    return generate_cyjl_list, origin_text_list, task_detail


prompt_type2value_map = {
    '生成首次病程记录':'''a. 如存在本次入院记录，根据入院记录生成首次病程记录；
b. 如a为空，按照生成入院记录的逻辑生成首次病程记录。

生成入院记录的逻辑：
a. 如果患者入院时间（取His入院时间）前7天内有急诊就诊记录，则读取并分析急诊的病历文书、检验检查报告、会诊记录，生成入院记录；
b. 如a为空，则读取入院前1个月内，与本次入院科室相同专科的门诊就诊记录（病历文书、检验检查报告、会诊记录），生成入院记录；
c. 如b也为空，则读取入院前1个月内，在本院所有科室做的检验检查结果，生成入院记录；
d. 如c继续为空，则输出：很抱歉，未检索到患者近1月内的就诊信息，无法生成入院记录。''',
    '生成病程/查房记录(非手术)':'''勾选后弹出日期选择框，选择日期确定后，系统分析该用户选择日期至前3天之间的以下数据：
    a.护理记录、医嘱、检验、检查结果、会诊记录和文书数据(排除入院记录、首程和出院记录)，自动生成所选日期的日常病程记录。
    b.如果a无数据，则使用护理记录、医嘱、检验、检查结果、会诊记录和文书数据(入院记录、首程)，自动生成所选日期的日常病程记录''',
    '生成病程/查房记录(术后)':'勾选后弹出日期选择框，选择日期确定后，系统分析该用户选择日期至前3天之间的以下数据：护理记录、医嘱、检验、检查结果、会诊记录和文书数据(排除出院记录)，自动生成所选日期的日常病程记录。',
    '生成出院记录':'''出院记录按照字段进行生成，以下是字段的取值来源：
    入院诊断：入院记录、首次病程记录、格式化诊断
    出院诊断：术后首次病程、手术记录、入院记录、格式化诊断、查房记录、首次查房记录、上级医师查房记录、转出记录
    入院情况：入院记录、首次病程记录
    主要化验结果：检验结果、检查结果报告、日常病程、查房记录、上级医师查房记录
    诊疗经过：病理检查结果、检验结果、检查结果报告、格式化医嘱、术后首次病程、手术记录、抢救记录、有创操作记录、日常病程、查房记录、首次查房记录、上级医师查房记录、转出记录
    出院情况：术后首次病程、日常病程、查房记录、上级医师查房记录
    出院医嘱：格式化医嘱、手术记录、有创操作记录、入院记录、查房记录、上级医师查房记录''',
    '生成手术记录':'''勾选后弹出日期选择框，选择日期确定后，触发进一步的提问“请输入拟采用的麻醉方式及术式”，用户输入后点击小飞机图标，系统基于手术前的所有文书、检验检查结果、会诊意见、医嘱开立情况，生成手术记录''',
    '生成术前讨论记录':'''勾选后弹出日期选择框，选择日期确定后，触发进一步的提问“请输入拟采用的麻醉方式及术式”，用户输入后点击小飞机图标，系统基于手术前的所有文书、检验检查结果、会诊意见、医嘱开立情况，按最终讨论结论，倒推生成术前讨论记录''',
    '生成入院记录':'''系统自动分析以下数据生成入院记录：
a. 如果患者入院时间（取His入院时间）前7天内有急诊就诊记录，则读取并分析急诊的病历文书、检验检查报告、会诊记录，生成入院记录；
b. 如a为空，则读取入院前1个月内，与本次入院科室相同专科的门诊就诊记录（病历文书、检验检查报告、会诊记录），生成入院记录；
c. 如b也为空，则读取入院前1个月内，在本院所有科室做的检验检查结果，生成入院记录；
d. 如c继续为空，则输出：很抱歉，未检索到患者近1月内的就诊信息，无法生成入院记录。''',
    '生成术前小结':'''勾选后弹出日期选择框，选择日期确定后，触发进一步的提问“请输入拟采用的麻醉方式及术式”，用户输入后点击小飞机图标，系统基于手术前的所有文书、检验检查结果、会诊意见、医嘱开立情况，按最终讨论结论，倒推生成术前小结。
    手术日期的判断标准（优先级从高到低）：
1. 如果有手术申请单，取申请单中的手术日期。
2. 有手术记录，取手术记录中的手术日期
3. 有术后首次病程记录，以记录日为手术日
4. 有出院记录，提取出院记录中记录的手术日期（如存在）
5. 如果以上数据都没有，则判断为手术尚未进行，提取全量病历信息进行生成。''',
    '生成查房记录':'勾选后弹出日期选择框，选择日期确定后，系统分析该用户选择日期至前3天之间的以下数据：护理记录、医嘱、检验、检查结果、会诊记录和文书数据(排除出院记录)，自动生成所选日期的日常病程记录。'}
prompt_type2progress_type_map = {'生成首次病程记录':2,'生成病程/查房记录(非手术)':4,'生成病程/查房记录(术后)':4,'生成出院记录':10,'生成手术记录':13,'生成术前讨论记录':21,'生成入院记录':1,'生成术前小结':5,'生成查房记录':4}
def get_task_detail_blsc(request_data, config_map):
    customer_id = request_data.get('customer_id', '').strip()
    record_id = request_data.get('record_id', '').strip()
    task_detail_id = request_data.get('task_detail_id', '').strip()
    customer_id = int(customer_id)
    record_id = int(record_id)
    generate_cyjl_list = []
    sql = '''
     select tt.task_name,dd.id,dd.customer_id,dd.record_id,dd.label_flag,dd.prompt,dd.llm_answer,dd.human_answer,IFNULL(dd.label_answer, '') label_answer,dd.prompt_type,IFNULL(dd.remark,'') label_remark,date_format(dd.modify_date,'%Y-%m-%d %H:%i:%s') task_detail_modify_date
     from label_task_detail dd inner join label_task tt on dd.task_uuid=tt.uuid where dd.id={}
                		 '''.format(task_detail_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    task_detail = {}
    if len(xs)>0:
        cid = xs[0]['customer_id']
        rid = xs[0]['record_id']
        task_name = xs[0]['task_name']
        prompt_type = xs[0]['prompt_type']
        # ToDo 取值逻辑
        value_rule = prompt_type2value_map.get(prompt_type, '').replace('\n', '</br>')
        generate_cyjl_list.append({'progress_type_zh': value_rule, 'progress_text': xs[0]['prompt'].replace('\n','</br>'), 'label_flag': 0})
        llm_answer = str(xs[0]['llm_answer'])
        human_answer = str(xs[0]['human_answer'])
        if task_name.find('文书解析')>-1:
            progress_id2text_parse = {}
            progress_param_list = [{'customer_id': cid, 'record_id': rid, 'progress_text': llm_answer,
                                        'progress_id': 10001,'progress_type': prompt_type2progress_type_map.get(prompt_type, -1),'msg_type': 0},
                                   {'customer_id': cid, 'record_id': rid, 'progress_text': human_answer,
                                        'progress_id': 10002,'progress_type': prompt_type2progress_type_map.get(prompt_type, -1),'msg_type': 0}]
            loop1 = asyncio.new_event_loop()
            asyncio.set_event_loop(loop1)
            loop1.run_until_complete(get_text_parse(progress_param_list, progress_id2text_parse, config_map))
            loop1.close()
            for progress_id in progress_id2text_parse:
                contents = progress_id2text_parse[progress_id]
                content_str = '\n\n'.join(['{}\n{}'.format(c['attribute_name'], c['content']) for c in contents])
                if int(progress_id) == 10001:
                    llm_answer = content_str
                elif int(progress_id) == 10002:
                    human_answer = content_str
        llm_answer = llm_answer.replace('\n', '</br>')
        human_answer = human_answer.replace('\n', '</br>')
        temp_list = [{'progress_type_zh': '答案', 'progress_text': llm_answer, 'label_flag': 1},{'progress_type_zh': '答案', 'progress_text': human_answer, 'label_flag': 2}]
        np.random.shuffle(temp_list)
        temp_list[0]['progress_type_zh'] = '答案A'
        temp_list[1]['progress_type_zh'] = '答案B'
        generate_cyjl_list.extend(temp_list)
        task_detail = xs[0]

    origin_text_list=[]
    return generate_cyjl_list, origin_text_list, task_detail


def get_task_detail_cyjl_compare(request_data, config_map):
    customer_id = request_data.get('customer_id', '').strip()
    record_id = request_data.get('record_id', '').strip()
    prompt_type = request_data.get('prompt_type', '').strip()
    customer_id = int(customer_id)
    record_id = int(record_id)
    sql = '''
     select  dd.id,dd.customer_id,dd.record_id,dd.label_flag,tt.instruction,dd.llm_answer,dd.label_answer,dd.prompt_type,dd.remark label_remark,date_format(dd.create_date,'%Y-%m-%d %H:%i:%s') task_detail_create_date,tt.task_name
     from label_task_detail dd inner join label_task tt on dd.task_uuid=tt.uuid
     where dd.customer_id={} and dd.record_id={} and dd.prompt_type='{}'
     order by dd.create_date desc
                		 '''.format(customer_id, record_id, prompt_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    label_detail_list = []
    for x in xs:
        prompt_version = '{}</br>{}</br>{}'.format(x['task_name'], x['prompt_type'], x['task_detail_create_date'])
        llm_answer = '{}'.format(x['llm_answer']).replace('None', '')
        label_answer = '{}</br></br></br>备注：{}'.format(x['label_answer'], x['label_remark']).replace('None', '')
        instruction = str(x['instruction']).replace('\n', '</br>')
        label_detail_list.append({'label_detail_id':x['id'],'task_name':x['task_name'],'prompt_version':prompt_version, 'instruction':instruction, 'llm_answer':llm_answer, 'label_answer':label_answer})
    return label_detail_list


def get_cyjl_origin(data,config_map):
    cyjl_map = {}
    record_data1 = data[data["progress_type"] == 10]  # 入院记录1
    if len(record_data1) > 0:  # 入院记录1/现病史10
        for _, record_data in record_data1.iterrows():
            text = record_data["progress_text"]
            keywords = ['入院诊断','出院诊断','入院情况','主要化验结果','出院时情况','出院后用药及建议医嘱']
            cyjl_map = split_by_keywords(text, keywords)

            # cyjl_map = cyjl_split(text)
            # msg_type = record_data["msg_type"]
            # progress_type = record_data["progress_type"]
            # customer_id = record_data["customer_id"]
            # customer_id = 1001
            # record_id = record_data["record_id"]
            # sps = progress_parse(text, progress_type,msg_type,config_map,customer_id,record_id)
            # for i in range(0, len(sps), 2):
            #     cyjl_map[sps[i]] = sps[i+1]
    return cyjl_map

# cyjl_reg = re.compile(
#     r"(?:入院诊断|出院诊断|入院情况|诊疗经过|出院时情况|出院后用药及建议医嘱)*")
# def cyjl_split(text):
#     keys = cyjl_reg.findall(text)
#     values = cyjl_reg.split(text)[1:]
#     rlt = {}
#     if len(keys) == len(values):
#         rlt = {k: v for k, v in zip(keys, values)}
#         for k, v in zip(keys, values):
#             if k=='出院时情况':
#                 rlt['出院情况'] = v
#             elif k == '出院后用药及建议医嘱':
#                 rlt['出院医嘱'] = v
#             else:
#                 rlt[k] = v
#     return rlt


def split_by_keywords(text, keywords):
    # 将关键字列表转换为正则表达式模式
    pattern = '|'.join([re.escape(keyword) for keyword in keywords])
    # 使用捕获组，确保保留关键字
    regex = re.compile(f'({pattern})')

    # 使用正则表达式进行切分，并包含分隔符（即关键字）
    parts = regex.split(text)

    # 遍历parts，忽略空字符串
    r_m = {}
    for i in range(1, len(parts) - 1, 2):
        if parts[i]=='出院时情况':
            r_m['出院情况'] = parts[i + 1].strip()
        elif parts[i] == '出院后用药及建议医嘱':
            r_m['出院医嘱'] = parts[i + 1].strip()
        # elif parts[i] == '主要化验结果':
        #     r_m['诊疗经过'] = parts[i + 1].strip()
        else:
            r_m[parts[i]] = parts[i + 1].strip()

    return r_m

def parse_text(progress_type, text):
    keywords = []
    if progress_type==1:
        keywords = ['主  诉：','现病史：','既往史：','体 格 检 查','专 科 检 查','辅 助 检 查','初步诊断：']
    elif progress_type==10:
        keywords = ['入院诊断','出院诊断','入院情况','主要化验结果','诊疗经过','出院时情况','出院后用药及建议医嘱']
    pattern = '|'.join([re.escape(keyword) for keyword in keywords])
    regex = re.compile(f'({pattern})')

    parts = regex.split(text)

    # 遍历parts，忽略空字符串
    new_text = ''
    for i in range(1, len(parts) - 1, 2):
        new_text += '{}<br/>{}<br/><br/>'.format(parts[i].strip(), parts[i+1].strip())
    return new_text