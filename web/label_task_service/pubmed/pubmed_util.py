# coding:utf-8
import urllib.request
import gzip
import shutil
import pandas as pd
import openai
import signal
import re
import json
import requests
import os
import tarfile


def download_pubmed(pubmed_path, url):
    # url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n1549.xml.gz'
    # url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n1550.xml.gz'
    save_path = os.path.join(pubmed_path, url.split('/')[-1])
    print('download: ',save_path)
    urllib.request.urlretrieve(url, save_path)
    output_file = save_path.replace('.gz','')
    with gzip.open(save_path, 'rb') as f_in, open(output_file, 'wb') as f_out:
        shutil.copyfileobj(f_in, f_out)
    print(f"File extracted to: {output_file}")
    return output_file

def untar(fname, dirs):
    try:
        t = tarfile.open(fname)
        t.extractall(path = dirs)
        return True
    except Exception as e:
        print(e)
        return False

def download_pmc(pubmed_path, url):
    # url = 'https://ftp.ncbi.nlm.nih.gov/pub/pmc/oa_package/00/00/PMC10054724.tar.gz'
    save_path = os.path.join(pubmed_path, url.split('/')[-1])
    output_path = os.path.join(pubmed_path, str(url.split('/')[-1]).replace('.tar.gz', ''))
    if not os.path.exists(output_path):
        print('download: ', save_path)
        urllib.request.urlretrieve(url, save_path)
        untar(save_path, pubmed_path)
    return output_path

def article_tumor(articleEn):
    reg_str = 'tumor|cancer|Carcinoma|Sarcoma|Lymphoma|Neoplasm|Malignancy|Oncology|Metastasis|Chemotherapy|Radiotherapy|Immunotherapy|Carcinogenesis|Oncogene|Palliative care|Adjuvant therapy|Lymph node involvement|In situ|Targeted therapy|Leukemia|Myeloma|Carcinogenic|infiltrating lymphocytes|Melanoma|BRCA1|BRCA2|Carcinoid|Glioblastoma|adenocarcinoma|Fibroma|Adenoma|Hemangioma|Lipoma|Osteoma|Papilloma|Chondroma|Neurofibroma|Meningioma|Angiomyolipoma'
    reg_pattern_re1 = re.compile(r'{}'.format(reg_str.lower()))
    llm_output_list = reg_pattern_re1.findall(articleEn.lower())
    if len(llm_output_list) > 0:
        return True
    return False


def post_url2(url, json_obj):
    headers = {
        "Content-Type": "application/json;charset=UTF-8"
    }
    return_json = None
    try:
        json_str = json.dumps(json_obj)
        r = requests.post(url, headers=headers, data=json_str)
        return json.loads(r.text)
    except requests.exceptions.RequestException as e:
        print(e)
    return return_json

def get_disease_profile(articleCn):
    reg_str = '瘤|癌|白血病|骨髓增生异常综合征'
    reg_pattern_re1 = re.compile(r'{}'.format(reg_str.lower()))
    # request_url = '''http://************:9092/inference'''
    request_url = '''http://************/go_pangoo/inference'''
    kb_seq_request_json = {
        "contents": [{
            "content": articleCn,
            "type": 86
        }]}

    result = post_url2(request_url, kb_seq_request_json)
    body = result.get("body", {})
    if body is None:
        return {}
    contentResults = body.get("contentResults", [])
    if len(contentResults) == 0:
        return {}
    sentences = contentResults[0].get('sentences', [])
    profile_map = {}
    disease_profile_list = []
    for i, sentence in enumerate(sentences):
        if sentence.get('concepts') is None:
            continue
        for concept in sentence['concepts']:
            if concept.get('conceptTypeNew', -1)!=5:
                continue
            if concept.get('standardConceptName', '')=='':
                continue
            llm_output_list = reg_pattern_re1.findall(concept.get('standardConceptName', ''))
            if len(llm_output_list) == 0:
                continue
            if concept['standardConceptName'] not in profile_map:
                disease_profile_list.append({
                        "id": concept['standardConceptId'],
                        "name": concept['standardConceptName']
                    })
            profile_map[concept.get('standardConceptName', '')] = 1
    return profile_map, disease_profile_list

def insert_admin(p_json):
    request_url = '''http://172.16.3.51:3020/documentAdmin/autoCollection'''
    # request_url = '''http://huitu.huimeionline.com/documentAdmin/autoCollection'''
    result = post_url2(request_url, p_json)
    r_data = result.get("data", {})
    print(r_data)
    is_success = False
    if "head" in r_data and "error" in r_data['head'] and r_data['head']['error']==0:
        is_success = True
    return is_success


timeout = 240
# 定义一个超时处理函数
def timeout_handler(signum, frame):
    raise TimeoutError("Timeout expired")

# 注册超时处理函数
signal.signal(signal.SIGALRM, timeout_handler)
def chatgpt_generate(prompt):
    answer = ''
    if not prompt:
        return answer
    openai.api_type = "azure"
    openai.api_base = "https://hmopenairesource.openai.azure.com/"
    openai.api_version = "2023-03-15-preview"
    openai.api_key = '********************************'
    signal.alarm(timeout)
    try:
        response = openai.ChatCompletion.create(
            engine="test_gpt_16k",
            top_p=0,
            timeout=30,
            frequency_penalty=0,
            presence_penalty=0,
            stop=None,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]
        )
        choices = response.get('choices', [])
        if len(choices) > 0:
            finish_reason = choices[0].get("finish_reason", '')
            if finish_reason == 'stop':
                answer = choices[0].get("message", {}).get("content", '')
                # print(answer)
                # if len(answer) > 0:
                #     return answer
    except:
        print("Operation timed out")
    finally:
        # 关闭定时器
        signal.alarm(0)
    return answer


def gen_excel(result_list,save_file):
    result_value = []
    result_key = []
    for result_map in result_list:
        result_value.append(list(result_map.values()))
        if not result_key:
            result_key = list(result_map.keys())
    write_pd = pd.DataFrame(result_value, columns=result_key)
    write_pd.to_excel('{}.xlsx'.format(save_file))
