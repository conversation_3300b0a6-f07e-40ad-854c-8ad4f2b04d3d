# coding:utf-8
import os
import xml.etree.ElementTree as ET
import oss2

accessKeyId = "LTAI5tFzytE3i2YUiPY7Gw5g"
accessKeySecret="******************************"
# 使用代码嵌入的RAM用户的访问密钥配置访问凭证。
auth2 = oss2.Auth(accessKeyId, accessKeySecret)
# auth2 = oss2.ProviderAuth(EnvironmentVariableCredentialsProvider())
bucket = oss2.Bucket(auth2, 'https://oss-cn-hangzhou.aliyuncs.com', 'hm-img-storage')
# bucket.put_object_from_file('yourObjectName', 'yourLocalFile')
proDir = os.path.split(os.path.realpath(__file__))[0]
pubmed_path = os.path.join(proDir, 'pubmed_path')

prompt_title = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这个PubMed标题，要求翻译后的中文符合中文语法且通俗易懂。根据以下示例，续写最后一个英文标题的译文。
示例1：
英文标题：Senescent alveolar macrophages promote early-stage lung tumorigenesis.
译文：老化的肺泡巨噬细胞促进发生早期肺部肿瘤。

示例2：
英文标题：Radiotherapy Plus Cisplatin With or Without Lapatinib for Non-Human Papillomavirus Head and Neck Carcinoma: A Phase 2 Randomized Clinical Trial.
译文：放疗加顺铂联合或不联合拉帕替尼治疗非人乳头瘤病毒头颈癌：2 期随机临床试验。

示例3：
英文标题：Isatuximab, Carfilzomib, Lenalidomide, and Dexamethasone for the Treatment of High-Risk Newly Diagnosed Multiple Myeloma.
译文：伊沙妥昔单抗、卡非佐米、来那度胺和地塞米松用于治疗新诊断的高危多发性骨髓瘤。

示例4：
英文标题：{}
译文：'''
prompt_abstract = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这篇PubMed文章的摘要，要求翻译后的中文符合中文语法且通俗易懂，只输出译文即可。
英文摘要：{}'''

# prompt_keyword = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这篇PubMed文章的关键词，要求翻译后的中文符合中文语法且通俗易懂，只输出译文即可。
# 英文关键词：{}'''
prompt_keyword = '''<指令>假设你是一名医学领域专家兼翻译人员，现在需要你帮我翻译PubMed文章的关键词。根据以下示例，给出最后一个英文关键词的译文。
示例1：
英文关键词：Humans,Antibodies,Antigens, Neoplasm,CD8-Positive T-Lymphocytes,Immunotherapy,T Cell Transcription Factor 1,genetics,Neoplasms,immunology,therapy,T cell dysfunction,T cell fitness,T cell priming,TCF1 stem-like T cells,checkpoint blockade,tumor immunology,tumor-draining lymph node,vaccine.
译文：人类抗体,抗原,肿瘤,CD8 阳性 T 淋巴细胞,免疫治疗,T 细胞转录因子 1,遗传学,肿瘤,免疫学,治疗,T 细胞功能障碍,T 细胞适应性,T 细胞启动,TCF1 干样 T 细胞, 检查点封锁,肿瘤免疫学,肿瘤引流淋巴结,疫苗.

示例2：
英文关键词：Humans,United States,Neoplasm Staging,Prognosis,Survival Analysis,Anus Neoplasms,diagnosis,American Joint Committee on Cancer (AJCC) version 9,anal cancer,cancer prognostication,principles of cancer staging,staging/tumor/node/metastasis (TNM) classification.
译文：人类,美国,肿瘤分期,预后,生存分析,肛门肿瘤,诊断,美国癌症联合委员会 (AJCC) 第 9 版,肛门癌,癌症预测,癌症分期原则,分期/肿瘤/淋巴结/转移 (TNM) 分类.

示例3：
英文关键词：Humans,Female,Breast Neoplasms,metabolism,Antineoplastic Combined Chemotherapy Protocols,therapeutic use,Receptor, ErbB-2,metabolism,Receptors, Progesterone,metabolism,therapeutic use,breast neoplasms,clinical trials,hormone receptor-positive,hormone therapy,human epidermal growth factor receptor 2 (HER2)-negative breast cancer.
译文：人类,女性,乳腺癌,代谢,抗肿瘤联合化疗方案,治疗用途,受体,ErbB-2,代谢,受体,黄体酮,代谢,治疗用途,乳腺癌肿瘤,临床试验,激素受体阳性,激素治疗,人类表皮 生长因子受体 2 (HER2) 阴性乳腺癌.

示例4：
英文关键词：{}
译文：'''
prompt_body = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这篇PubMed文章的正文，要求翻译后的中文符合中文语法且通俗易懂，只输出译文即可。
英文正文：{}'''
oss_img_rul = 'https://hm-img-storage.oss-cn-hangzhou.aliyuncs.com/hmknowledge_img/20240130'

def get_nested_text(element):
    if element is None:
        return ''
    if element.tag =='fig':
        return '--raw_data--{}--raw_data--'.format(xml_img_html(element))
    elif element.tag == 'table-wrap':
        return '--raw_data--{}--raw_data--'.format(xml_table_html(element))
    # 获取当前标签的文本
    text = element.text or ''

    # 递归处理所有子标签
    for child in element:
        # 递归调用，获取子标签的文本
        child_text = get_nested_text(child)

        # 将子标签的文本添加到当前文本
        if child.tag == 'p':
            text += '\n' + child_text
        else:
            text += child_text

        # 如果子标签有尾部文本，也添加到当前文本
        if child.tail:
            text += child.tail

    return text

def tag_xml2html(xml_data):
    html_data = str(xml_data).replace('<bold>','<strong>').replace('</bold>','</strong>').replace('<break/>','<br/>')
    return html_data

def xml_table_html(table_node):
    label_text = get_nested_text(table_node.find('label'))
    caption_text = get_nested_text(table_node.find('caption'))
    raw_data = ET.tostring(table_node.find('table'), encoding="utf-8").decode("utf-8")
    raw_data = tag_xml2html(raw_data)
    table_str = '<p></p>{}{}{}'.format(label_text, caption_text, raw_data)
    return table_str

def xml_img_html(img_node):
    graphic = img_node.find('graphic')
    # graphic_id = graphic.get('xlink:href')
    graphic_id = ''
    for k in graphic.keys():
        if k.find('href')>-1:
            graphic_id = graphic.get(k)
    label_text = get_nested_text(img_node.find('label'))
    caption = get_nested_text(img_node.find('caption'))
    # raw_data = ET.tostring(img_node.find('caption'), encoding="utf-8").decode("utf-8")
    img_str = '<p></p>{}{}<p><img src="{}/{}.jpg" title="" alt="{}/{}.jpg"/></p>'.format(label_text,caption,oss_img_rul,graphic_id,oss_img_rul,graphic_id)
    return img_str

def parse_body(body_node):
    body_cn_list = []
    for sec in body_node.findall('sec'):
        p_list = []
        for child in sec:
            p_list.append(get_nested_text(child))
            # if child.tag in ['fig','table-wrap']:
            #     if p_list:
            #         body_cn = chatgpt_generate(prompt_body.format('\n'.join(p_list)))
            #         if body_cn:
            #             body_cn2 = ''.join(['<p>{}</p>'.format(s) for s in body_cn.split('\n') if s])
            #             body_cn_list.append(body_cn2)
            #             p_list = []
            #     if child.tag=='fig':
            #         body_cn_list.append(xml_img_html(child))
            #     elif child.tag == 'table-wrap':
            #         body_cn_list.append(xml_table_html(child))
            # else:
            #     p_list.append(get_nested_text(child))
        if p_list:
            for p_str in '\n'.join(p_list).split('--raw_data--'):
                if not p_str:
                    continue
                if p_str.find('<img') > -1 or p_str.find('<table') > -1:
                    body_cn_list.append(p_str)
                else:
                    body_cn = chatgpt_generate(prompt_body.format(p_str))
                    if body_cn:
                        body_cn2 = ''.join(['<p>{}</p>'.format(s) for s in body_cn.split('\n') if s])
                        body_cn_list.append(body_cn2)
    return ''.join(body_cn_list)


def parse_ref(ref_node):
    ref_text_list = []
    for ref in ref_node.findall('ref'):
        label_text = get_nested_text(ref.find('label'))
        element_citation = ref.find('element-citation')
        if element_citation is None:
            ref_text = get_nested_text(ref.find('mixed-citation'))
            ref_text_list.append(ref_text)
            continue
        person_group = element_citation.find('person-group')
        name_list = []
        for name in person_group.findall('name'):
            name_list.append(get_nested_text(name))
        raw_data = ''
        if element_citation.find('article-title') is not None:
            raw_data = ET.tostring(element_citation.find('article-title'), encoding="utf-8").decode("utf-8")
        source_text = get_nested_text(element_citation.find('source'))
        publisher_name_text = get_nested_text(element_citation.find('publisher-name'))
        publisher_loc_text = get_nested_text(element_citation.find('publisher-loc'))
        if publisher_name_text:
            source_text += '.'+publisher_name_text
        if publisher_loc_text:
            source_text += '.'+publisher_loc_text
        year_text = get_nested_text(element_citation.find('year'))
        volume_text = get_nested_text(element_citation.find('volume'))
        fpage_text = get_nested_text(element_citation.find('fpage'))
        lpage_text = get_nested_text(element_citation.find('lpage'))
        if fpage_text:
            volume_text += ':'+fpage_text
        if lpage_text:
            volume_text += '-'+lpage_text
        pub_id = element_citation.find('pub-id')
        doi_text=''
        if pub_id is not None:
            if pub_id.get('pub-id-type')=='pmid':
                doi_text = 'pmid: {}'.format(pub_id.text)
            elif pub_id.get('pub-id-type')=='doi':
                doi_text = 'doi: {}'.format(pub_id.text)
        ref_text = '<p>{} {}.{}.{}.{};{}{}</p>'.format(label_text, ','.join(name_list), raw_data,source_text,year_text,volume_text,doi_text).replace('..','.').replace('<article-title>','').replace('</article-title>','')
        ref_text_list.append(ref_text)
    return ''.join(ref_text_list)


if __name__ == '__main__':
    # parse_body('/Users/<USER>/Downloads/PMC5274670/CRINM2017-5696512.nxml')
    print('done')
