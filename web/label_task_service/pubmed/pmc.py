# coding:utf-8
import os
import xml.etree.ElementTree as ET
import oss2

accessKeyId = "LTAI5tFzytE3i2YUiPY7Gw5g"
accessKeySecret="******************************"
# 使用代码嵌入的RAM用户的访问密钥配置访问凭证。
auth2 = oss2.Auth(accessKeyId, accessKeySecret)
# auth2 = oss2.ProviderAuth(EnvironmentVariableCredentialsProvider())
bucket = oss2.Bucket(auth2, 'https://oss-cn-hangzhou.aliyuncs.com', 'hm-img-storage')
# bucket.put_object_from_file('yourObjectName', 'yourLocalFile')
proDir = os.path.split(os.path.realpath(__file__))[0]
pubmed_path = os.path.join(proDir, 'pubmed_path')


def parse_xml(xml_file):
    # 解析XML文件
    tree = ET.parse(xml_file)
    pubmedArticle = tree.getroot()
    # 遍历XML元素
    try:
        front = pubmedArticle.find('front')

        journal_meta = front.find('journal-meta')
        journal_title_group = journal_meta.find('journal-title-group')
        journal_title = journal_title_group.find('journal-title').text

        article_meta = front.find('article-meta')
        pmid = ''
        doi = ''
        for article_id in article_meta.findall('article-id'):
            if article_id.get('pub-id-type') == 'pmid':
                pmid = article_id.text
            elif article_id.get('pub-id-type') == 'doi':
                doi = article_id.text

        year = ''
        month = ''
        for pub_date in article_meta.findall('pub-date'):
            if pub_date.get('pub-type') == 'epub':
                year = pub_date.find('year').text
                month = pub_date.find('month').text

        title_group = article_meta.find('title-group')

        article_title = title_group.find('article-title')
        article_title_text = get_nested_text(article_title)

        abstract = article_meta.find('abstract')
        abstract_text = get_nested_text(abstract)

        kwd_group = article_meta.find('kwd-group')
        keyword_list = []
        if kwd_group:
            for kwd in kwd_group.findall('kwd'):
                keyword_list.append(get_nested_text(kwd))
        keyword_text = ','.join(keyword_list)
        body = pubmedArticle.find('body')
        body_text = parse_body(body)
        if not body_text:
            return 0
        back = pubmedArticle.find('back')
        ref_list = back.find('ref-list')
        ref_text = parse_ref(ref_list)

        articleTitleCn = chatgpt_generate(prompt_title.format(article_title_text))
        abstractTextCn = chatgpt_generate(prompt_abstract.format(abstract_text))
        keywordCn = ''
        if keyword_text:
            keywordCn = chatgpt_generate(prompt_keyword.format(keyword_text))
        _, disease_profile_list = get_disease_profile('{} {} {}'.format(articleTitleCn, abstractTextCn, keywordCn))
        p_json = {
            "articleTitleCn": articleTitleCn,
            "articleTitle": article_title_text,
            "pubDate": "{}-{}".format(year, '0{}'.format(month)[-2:]),
            "title": journal_title,
            "diseaseList": disease_profile_list,
            # "abstractText": abstractTextCn,
            "contents": [{
                "title": "摘要",
                "content": ''.join(['<p>{}</p>'.format(s) for s in abstractTextCn.split('\n') if s]),
                "sort": 1
            }, {
                "title": "正文",
                "content": body_text,
                "sort": 2
            }, {
                "title": "引用",
                "content": ref_text,
                "sort": 3
            }],
            "keyWord": keyword_text,
            "doi": doi,
            "pubmed_url": "https://pubmed.ncbi.nlm.nih.gov/{}/".format(pmid),
            "journal_url": "https://doi.org/{}".format(doi)

        }
        p_json_path = xml_file.replace('nxml', 'json')
        with open(p_json_path, 'w', encoding='utf-8') as f:
            json.dump(p_json, f, ensure_ascii=False)
        # is_success = insert_admin(p_json)
        # return is_success
        return 1
    except:
        return 0
    # return False


def upload_beiyi():
    dir_list = ['978-7-5659-0516-2-2-1耳鼻咽喉头颈科学','978-7-5659-0678-7-3-1传染病学','978-7-5659-0724-1-3-2儿科学','978-7-5659-0743-2-妇产科学','978-7-5659-0744-9-3-2眼科学','978-7-5659-0745-6-3-1皮肤病学与性病学（四色）','978-7-5659-0747-0-3-1-外科学','978-7-5659-0751-7-神经病学']
    for _d in dir_list:
        dir_path = '/Users/<USER>/Documents/北京大学医学出版社/{}/XML/Images'.format(_d)
        for _, file_path in enumerate(os.listdir(dir_path)):
            if file_path.endswith('.jpg'):
                print('upload img: start')
                bucket.put_object_from_file('hmknowledge_img/20240315/{}'.format(file_path),os.path.join(dir_path, file_path))
                print('upload img: done, {},{}'.format(_d, file_path))
    print('done')


if __name__ == '__main__':
    # upload_beiyi()
    # result_data_path = '/Users/<USER>/Documents/pmc_result.txt'
    # pmc_map = {}
    # with open(result_data_path, "r") as f_r:
    #     for i, line in enumerate(f_r.readlines()):
    #         pmc_id = line.strip()
    #         pmc_map[pmc_id] = ''
    # df = pd.read_csv('/Users/<USER>/Downloads/oa_file_list.csv')
    # for index, row in df.iterrows():
    #     file = row['File']
    #     accessionID = row['Accession ID']
    #     if accessionID in pmc_map:
    #         print(file, accessionID)
    #         pmc_map[accessionID] = file
    # with open('pmc2path.json', 'w', encoding='utf-8') as f:
    #     json.dump(pmc_map, f, ensure_ascii=False)
    # download_pmc(pubmed_path=pubmed_path,url='https://ftp.ncbi.nlm.nih.gov/pub/pmc/oa_package/f1/c8/PMC7615270.tar.gz')

    # bucket.put_object_from_file('hmknowledge_img/20240130/EMS187151-f001.jpg', '/Users/<USER>/py/aigc_hospital/web/pubmed/pubmed_path/PMC7615270/EMS187151-f001.jpg')

    pmc2path = json.load(open('pmc2path.json', "rb"))
    for i, k in enumerate(sorted(pmc2path)):
        print(i)
        if i < 270:
            continue
        # if i >= 15:
        #     break
        output_dir_path = download_pmc(pubmed_path=pubmed_path, url='https://ftp.ncbi.nlm.nih.gov/pub/pmc/{}'.format(pmc2path[k]))

        for _, file_path in enumerate(os.listdir(output_dir_path)):
            if file_path.endswith('.jpg'):
                print('upload img: start')
                bucket.put_object_from_file('hmknowledge_img/20240130/{}'.format(file_path),os.path.join(output_dir_path, file_path))
                print('upload img: done, {}'.format(file_path))
            elif file_path.endswith('.nxml'):
                print('parse xml: start')
                result = parse_xml(os.path.join(output_dir_path, file_path))
                print('parse xml: done {}, {}/{}'.format(result, output_dir_path, file_path))
    # parse_xml('/Users/<USER>/Downloads/PMC10775503/12943_2023_Article_1925.nxml')
    print('done')
