# coding:utf-8
import os
import xml.etree.ElementTree as ET

proDir = os.path.split(os.path.realpath(__file__))[0]
jsonPath = os.path.join(proDir, 'pmid_map.json')
pubmed_config_path = os.path.join(proDir, 'pubmed_config.json')
pubmed_path = os.path.join(proDir, 'pubmed_path')

isoList = ['CA: A Cancer Journal for Clinicians','New England Journal of Medicine','The Lancet','Nature Reviews Clinical Oncology','JAMA Oncology','Cancer Cell','Journal of Clinical Oncology','Cancer Discovery','Nature Medicine','CA Cancer J Clin','N Engl J Med','Lancet','Nat Rev Clin Oncol','JAMA Oncol','Cancer Cell','J Clin Oncol','Cancer Discov','Nat Med','JAMA']

month_map = {'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6, 'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12}
prompt_title = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这个PubMed标题，要求翻译后的中文符合中文语法且通俗易懂。根据以下示例，续写最后一个英文标题的译文。
示例1：
英文标题：Senescent alveolar macrophages promote early-stage lung tumorigenesis.
译文：老化的肺泡巨噬细胞促进发生早期肺部肿瘤。

示例2：
英文标题：Radiotherapy Plus Cisplatin With or Without Lapatinib for Non-Human Papillomavirus Head and Neck Carcinoma: A Phase 2 Randomized Clinical Trial.
译文：放疗加顺铂联合或不联合拉帕替尼治疗非人乳头瘤病毒头颈癌：2 期随机临床试验。

示例3：
英文标题：Isatuximab, Carfilzomib, Lenalidomide, and Dexamethasone for the Treatment of High-Risk Newly Diagnosed Multiple Myeloma.
译文：伊沙妥昔单抗、卡非佐米、来那度胺和地塞米松用于治疗新诊断的高危多发性骨髓瘤。

示例4：
英文标题：{}
译文：'''
prompt_abstract = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这篇PubMed文章的摘要，要求翻译后的中文符合中文语法且通俗易懂，只输出译文即可。
英文摘要：{}'''

# prompt_keyword = '''<指令>假设你是一名医学领域专家兼翻译人员，请帮我翻译下面这篇PubMed文章的关键词，要求翻译后的中文符合中文语法且通俗易懂，只输出译文即可。
# 英文关键词：{}'''
prompt_keyword = '''<指令>假设你是一名医学领域专家兼翻译人员，现在需要你帮我翻译PubMed文章的关键词。根据以下示例，给出最后一个英文关键词的译文。
示例1：
英文关键词：Humans,Antibodies,Antigens, Neoplasm,CD8-Positive T-Lymphocytes,Immunotherapy,T Cell Transcription Factor 1,genetics,Neoplasms,immunology,therapy,T cell dysfunction,T cell fitness,T cell priming,TCF1 stem-like T cells,checkpoint blockade,tumor immunology,tumor-draining lymph node,vaccine.
译文：人类抗体,抗原,肿瘤,CD8 阳性 T 淋巴细胞,免疫治疗,T 细胞转录因子 1,遗传学,肿瘤,免疫学,治疗,T 细胞功能障碍,T 细胞适应性,T 细胞启动,TCF1 干样 T 细胞, 检查点封锁,肿瘤免疫学,肿瘤引流淋巴结,疫苗.

示例2：
英文关键词：Humans,United States,Neoplasm Staging,Prognosis,Survival Analysis,Anus Neoplasms,diagnosis,American Joint Committee on Cancer (AJCC) version 9,anal cancer,cancer prognostication,principles of cancer staging,staging/tumor/node/metastasis (TNM) classification.
译文：人类,美国,肿瘤分期,预后,生存分析,肛门肿瘤,诊断,美国癌症联合委员会 (AJCC) 第 9 版,肛门癌,癌症预测,癌症分期原则,分期/肿瘤/淋巴结/转移 (TNM) 分类.

示例3：
英文关键词：Humans,Female,Breast Neoplasms,metabolism,Antineoplastic Combined Chemotherapy Protocols,therapeutic use,Receptor, ErbB-2,metabolism,Receptors, Progesterone,metabolism,therapeutic use,breast neoplasms,clinical trials,hormone receptor-positive,hormone therapy,human epidermal growth factor receptor 2 (HER2)-negative breast cancer.
译文：人类,女性,乳腺癌,代谢,抗肿瘤联合化疗方案,治疗用途,受体,ErbB-2,代谢,受体,黄体酮,代谢,治疗用途,乳腺癌肿瘤,临床试验,激素受体阳性,激素治疗,人类表皮 生长因子受体 2 (HER2) 阴性乳腺癌.

示例4：
英文关键词：{}
译文：'''
def parse_xml(xml_file):
    pmid_map = json.load(open(jsonPath, "rb"))
    # 解析XML文件
    tree = ET.parse(xml_file)
    root = tree.getroot()
    result = []
    # 遍历XML元素
    for pubmedArticle in root.findall('PubmedArticle'):
        try:
            medlineCitation = pubmedArticle.find('MedlineCitation')
            year = 0
            month = ''
            pmid = medlineCitation.find('PMID').text
            if pmid in pmid_map:
                continue

            article = medlineCitation.find('Article')

            journal = article.find('Journal')
            journalIssue = journal.find('JournalIssue')
            pubDate = journalIssue.find('PubDate')
            if pubDate:
                ele_year = pubDate.find('Year')
                if ele_year is not None:
                    year = int(pubDate.find('Year').text)
                    month = month_map.get(pubDate.find('Month').text,pubDate.find('Month').text)
                else:
                    pubmedData = pubmedArticle.find('PubmedData')
                    history = pubmedData.find('History')
                    for pubMedPubDate in history.findall('PubMedPubDate'):
                        if pubMedPubDate.get('PubStatus') == 'pubmed':
                            year = int(pubMedPubDate.find('Year').text)
                            month = pubMedPubDate.find('Month').text
                            break
            if year!=2023:
                continue

            title = journal.find('Title').text
            iSOAbbreviation = journal.find('ISOAbbreviation').text
            if iSOAbbreviation not in isoList:
                continue

            doi = ''
            for eLocationID in article.findall('ELocationID'):
                if eLocationID.get('EIdType') == 'doi':
                    doi = eLocationID.text
            articleTitle = get_nested_text(article.find('ArticleTitle'))


            abstractText = ''
            abstract = article.find('Abstract')
            if abstract:
                copyrightInformation = abstract.find('CopyrightInformation')
                if copyrightInformation is not None:
                    continue
                for abstractText2 in abstract.findall('AbstractText'):
                    if abstractText2.get('Label'):
                        abstractText += '{}:{}<br>'.format(abstractText2.get('Label'), get_nested_text(abstractText2))
                    else:
                        abstractText += get_nested_text(abstractText2)
            if not abstractText:
                continue

            mesh_list = []
            meshHeadingList = medlineCitation.find('MeshHeadingList')
            if meshHeadingList:
                for meshHeading in meshHeadingList.findall('MeshHeading'):
                    descriptorName = meshHeading.find('DescriptorName').text
                    mesh_list.append(descriptorName)
                    for qualifierName in meshHeading.findall('QualifierName'):
                        mesh_list.append(qualifierName.text)

            keyword_list = []
            keywordList = medlineCitation.find('KeywordList')
            if keywordList:
                for keyword in keywordList.findall('Keyword'):
                    keyword_list.append(keyword.text)
            meshHeadingEn = ','.join(mesh_list)
            keywordEn = ','.join(keyword_list)
            articleEn = '【文章标题】{}【文章摘要】{}【文章关键词1】{}【文章关键词2】{}'.format(articleTitle,abstractText,meshHeadingEn,keywordEn)
            # print(articleEn)
            articleTitleCn,abstractTextCn,meshHeadingCn,keywordCn,profileWords = '','','','',''
            is_tumor = article_tumor(articleEn)
            if is_tumor:
                articleTitleCn = chatgpt_generate(prompt_title.format(articleTitle))
                abstractTextCn = chatgpt_generate(prompt_abstract.format(abstractText))
                if meshHeadingEn or keywordEn:
                    # todo 医学关键词prompt
                    # keywordCn = chatgpt_generate('{}{}{}'.format('翻译以下医学文本：', meshHeadingEn,keywordEn))
                    keywordCn = chatgpt_generate(prompt_keyword.format(meshHeadingEn+','+keywordEn))
                # profileWords = ','.join(list(get_disease_profile('{} {} {}'.format(articleCn,abstractTextCn,keywordCn)).keys()))
                _, disease_profile_list = get_disease_profile('{} {} {}'.format(articleTitleCn,abstractTextCn,keywordCn))
                p_json = {
                    "articleTitleCn": articleTitleCn,
                    "articleTitle": articleTitle,
                    "pubDate": "{}-{}".format(year, '0{}'.format(month)[-2:]),
                    "title": title,
                    "diseaseList": disease_profile_list,
                    "abstractText": abstractText,
                    "keyWord": keywordEn,
                    "doi": doi,
                    "pubmed_url": "https://pubmed.ncbi.nlm.nih.gov/{}/".format(pmid),
                    "journal_url": "https://doi.org/{}".format(doi)

                }
                is_success = insert_admin(p_json)
                if is_success:
                    pmid_map[pmid] = 1
                print(f"pmid: {pmid}, title: {title}, is_success: {is_success}")

            # print(f"pmid: {pmid}, doi: {doi}, title: {title}")
            result.append({"xml_file":xml_file,"pmid":pmid,"doi":doi,"title":title,"pubDate":"{}-{}".format(year,month),"articleTitle":articleTitle,"articleTitleCn":articleTitleCn,"abstractText":abstractText,"abstractTextCn":abstractTextCn,"is_tumor":is_tumor,"profileWords":profileWords,"meshHeading":meshHeadingEn,"keyword":keywordEn,"meshHeadingCn":meshHeadingCn,"keywordCn":keywordCn})
        except:
            pass
    with open(jsonPath, 'w', encoding='utf-8') as f:
        json.dump(pmid_map, f, ensure_ascii=False)
    return result

def run_auto_pubmed():
    pubmed_config = json.load(open(pubmed_config_path, "rb"))
    max_index = pubmed_config['max_index']+1
    url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n{}.xml.gz'.format(max_index)
    download_pubmed(pubmed_path, url)
    xml_file = os.path.join(pubmed_path, 'pubmed23n{}.xml'.format(max_index))
    parse_xml(xml_file)
    pubmed_config['max_index'] = max_index
    with open(pubmed_config_path, 'w', encoding='utf-8') as f:
        json.dump(pubmed_config, f, ensure_ascii=False)
    return 1

if __name__ == '__main__':
    # for i in range(1491,1553,1):
    #     url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n{}.xml.gz'.format(i)
    #     download_pubmed(pubmed_path, url)
    # result_list = []
    # for i in range(1491, 1553, 1):
    #     print(i)
    #     xml_file = os.path.join(pubmed_path, 'pubmed23n{}.xml'.format(i))
    #     rl = parse_xml(xml_file)
    #     result_list.extend(rl)
    # time_str = time.strftime("%Y%m%d_%H%M%S", time.localtime(time.time()))
    # gen_excel(result_list,'pubmed23n_{}.xml'.format(time_str))
    # run_auto_pubmed()
    # xml_file = os.path.join(pubmed_path, 'pubmed23n{}.xml'.format(1494))
    # parse_xml(xml_file)
    for i in range(1496, 1553, 1):
        url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n{}.xml.gz'.format(i)
        download_pubmed(pubmed_path, url)
        xml_file = os.path.join(pubmed_path, 'pubmed23n{}.xml'.format(i))
        parse_xml(xml_file)
    print('done')
