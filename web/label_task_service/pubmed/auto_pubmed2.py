# coding:utf-8
import urllib.request
import gzip
import shutil
import xml.etree.ElementTree as ET
import pandas as pd
import openai
import time
import signal

isoList = ['CA: A Cancer Journal for Clinicians','New England Journal of Medicine','The Lancet','Nature Reviews Clinical Oncology','JAMA Oncology','Cancer Cell','Journal of Clinical Oncology','Cancer Discovery','Nature Medicine','CA Cancer J Clin','N Engl J Med','Lancet','Nat Rev Clin Oncol','JAMA Oncol','Cancer Cell','J <PERSON>','Cancer Discov','Nat Med','JAMA']
def download_pubmed(url):
    # url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n1549.xml.gz'
    # url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n1550.xml.gz'
    save_path = url.split('/')[-1]
    print('download: ',save_path)
    urllib.request.urlretrieve(url, save_path)
    output_file = save_path.replace('.gz','')
    with gzip.open(save_path, 'rb') as f_in, open(output_file, 'wb') as f_out:
        shutil.copyfileobj(f_in, f_out)
    print(f"File extracted to: {output_file}")
    return output_file

month_map = {'Jan': 1, 'Feb': 2, 'Mar': 3, 'Apr': 4, 'May': 5, 'Jun': 6, 'Jul': 7, 'Aug': 8, 'Sep': 9, 'Oct': 10, 'Nov': 11, 'Dec': 12}
def parse_xml(xml_file):
    # 解析XML文件
    tree = ET.parse(xml_file)
    root = tree.getroot()
    result = []
    # 遍历XML元素
    for pubmedArticle in root.findall('PubmedArticle'):
        medlineCitation = pubmedArticle.find('MedlineCitation')
        pubmedData = pubmedArticle.find('PubmedData')
        history = pubmedData.find('History')
        year = 0
        month = ''
        for pubMedPubDate in history.findall('PubMedPubDate'):
            if pubMedPubDate.get('PubStatus')=='pubmed':
                year = int(pubMedPubDate.find('Year').text)
                month = pubMedPubDate.find('Month').text
                break
        if year!=2023:
            continue
        pmid = medlineCitation.find('PMID').text

        article = medlineCitation.find('Article')

        journal = article.find('Journal')
        journalIssue = journal.find('JournalIssue')
        # pubDate = journalIssue.find('PubDate')
        # if pubDate:
        #     year = pubDate.find('Year').text
        #     month = month_map.get(pubDate.find('Month').text,'')
        # if year!=2023:
        #     continue
        title = journal.find('Title').text
        iSOAbbreviation = journal.find('ISOAbbreviation').text
        if iSOAbbreviation not in isoList:
            continue

        doi = ''
        for eLocationID in article.findall('ELocationID'):
            if eLocationID.get('EIdType') == 'doi':
                doi = eLocationID.text
        articleTitle = get_nested_text(article.find('ArticleTitle'))


        abstractText = ''
        abstract = article.find('Abstract')
        if abstract:
            copyrightInformation = article.find('CopyrightInformation')
            if copyrightInformation:
                continue
            for abstractText2 in abstract.findall('AbstractText'):
                if abstractText2.get('Label'):
                    abstractText += '{}:{}'.format(abstractText2.get('Label'), abstractText2.text)
                else:
                    abstractText += abstractText2.text
        if not abstractText:
            continue

        mesh_list = []
        meshHeadingList = medlineCitation.find('MeshHeadingList')
        if meshHeadingList:
            for meshHeading in meshHeadingList.findall('MeshHeading'):
                descriptorName = meshHeading.find('DescriptorName').text
                mesh_list.append(descriptorName)
                for qualifierName in meshHeading.findall('QualifierName'):
                    mesh_list.append(qualifierName.text)

        keyword_list = []
        keywordList = medlineCitation.find('KeywordList')
        if keywordList:
            for keyword in keywordList.findall('Keyword'):
                keyword_list.append(keyword.text)
        # articleTitleCn = chatgpt_generate('{}{}'.format('翻译：',articleTitle))
        # abstractTextCn = chatgpt_generate('{}{}'.format('翻译：',abstractText))
        meshHeadingEn = ';'.join(mesh_list)
        # meshHeadingCn = chatgpt_generate('{}{}'.format('翻译：',meshHeadingEn))
        keywordEn = ';'.join(keyword_list)
        # keywordCn = chatgpt_generate('{}{}'.format('翻译：',keywordEn))

        articleEn = '【文章标题】{}【文章摘要】{}【文章关键词1】{}【文章关键词2】{}'.format(articleTitle,abstractText,meshHeadingEn,keywordEn)
        print(articleEn)

        # articleCn = chatgpt_generate('{}{}'.format('翻译以下：', meshHeadingEn))
        # print(f"pmid: {pmid}, doi: {doi}, title: {title}")
        # result.append({"pmid":pmid,"doi":doi,"articleTitle":articleTitle,"articleTitleCn":articleTitleCn,"title":title,"pubDate":"{}-{}".format(year,month),"abstractText":abstractText,"meshHeading":meshHeadingEn,"keyword":keywordEn,"abstractTextCn":abstractTextCn,"meshHeadingCn":meshHeadingCn,"keywordCn":keywordCn})
    return result

def article_tumor(articleEn):
    reg_str = 'tumor|cancer|Carcinoma|Sarcoma|Lymphoma|Neoplasm|Malignancy|Oncology|Metastasis|Chemotherapy|Radiotherapy|Immunotherapy|Carcinogenesis|Oncogene|Palliative care|Adjuvant therapy|Lymph node involvement|In situ|Targeted therapy|Leukemia|Myeloma|Carcinogenic|infiltrating lymphocytes|Melanoma|BRCA1|BRCA2|Carcinoid|Glioblastoma|adenocarcinoma|Fibroma|Adenoma|Hemangioma|Lipoma|Osteoma|Papilloma|Chondroma|Neurofibroma|Meningioma|Angiomyolipoma'
    reg_pattern_re1 = re.compile(r'{}'.format(reg_str.lower()))
    llm_output_list = reg_pattern_re1.findall(articleEn.lower())
    if len(llm_output_list) > 0:
        return True
    return False

def get_nested_text(element):
    # 获取当前标签的文本
    text = element.text or ''

    # 递归处理所有子标签
    for child in element:
        # 递归调用，获取子标签的文本
        child_text = get_nested_text(child)

        # 将子标签的文本添加到当前文本
        text += child_text

        # 如果子标签有尾部文本，也添加到当前文本
        if child.tail:
            text += child.tail

    return text

timeout = 240
# 定义一个超时处理函数
def timeout_handler(signum, frame):
    raise TimeoutError("Timeout expired")

# 注册超时处理函数
signal.signal(signal.SIGALRM, timeout_handler)
def chatgpt_generate(prompt):
    openai.api_type = "azure"
    openai.api_base = "https://hmopenairesource.openai.azure.com/"
    openai.api_version = "2023-03-15-preview"
    openai.api_key = '********************************'
    signal.alarm(timeout)
    try:
        response = openai.ChatCompletion.create(
            engine="test_gpt_16k",
            top_p=0,
            timeout=30,
            frequency_penalty=0,
            presence_penalty=0,
            stop=None,
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ]
        )
        choices = response.get('choices', [])
        if len(choices) > 0:
            finish_reason = choices[0].get("finish_reason", '')
            if finish_reason == 'stop':
                answer = choices[0].get("message", {}).get("content", '')
                print(answer)
                if len(answer) > 0:
                    return answer
    except:
        print("Operation timed out")
    finally:
        # 关闭定时器
        signal.alarm(0)
        return ''


def gen_excel(result_list,save_file):
    result_value = []
    result_key = []
    for result_map in result_list:
        result_value.append(list(result_map.values()))
        if not result_key:
            result_key = list(result_map.keys())
    write_pd = pd.DataFrame(result_value, columns=result_key)
    write_pd.to_excel('{}.xlsx'.format(save_file))

def json2xlsx():
    import json
    xs = json.load(open('/Users/<USER>/Desktop/disease_test_data/test_disease_predict_compress_v19.json', 'r'))
    result_list = []
    for x in xs:
        result_list.append(['{}\n{}'.format(x['instruction'],x['input']),x['output']])
    write_pd = pd.DataFrame(result_list, columns=['prompt','label'])
    write_pd.to_excel('{}.xlsx'.format('tttt'))

def jsonl2xlsx():
    import json,os
    from tqdm import tqdm
    dev_list = json.load(open('/Users/<USER>/Downloads/dev_IMCS-V2-MRG_instruction.json', "rb"))
    result_list = []
    with open('/Users/<USER>/Downloads/generated_predictions.jsonl', "r") as f_r:
    # with open('/Users/<USER>/Desktop/generated_predictions_model1_v19.txt', "r") as f_r:
        for i, line in enumerate(f_r.readlines()):
            x = json.loads(line)
            result_list.append([dev_list[i]['instruction'], x['label'], x['predict']])

    write_pd = pd.DataFrame(result_list, columns=['prompt','label','predict'])
    write_pd.to_excel('{}.xlsx'.format('tttt33333333'))

def process_imcs():
    import json
    dev_list = json.load(open('/Users/<USER>/Downloads/dev_IMCS-V2-MRG_instruction.json', "rb"))
    result = []
    for imcs in dev_list:
        instruction = str(imcs['instruction'])+'\n'
        start = max(instruction.find('问诊对话历史：'),0)
        last = max(instruction.rfind('患者：'), instruction.rfind('医生：'))
        end = instruction[last:].find('\n')
        instruction2 = instruction[start:last+end]
        result.append({'instruction': '根据患者提供的医疗信息，自动生成门诊病历，涵盖主诉、现病史、辅助检查、既往史、诊断以及建议等六大部分。', 'input': instruction2, 'output': imcs['output'],'sample_id': imcs['sample_id']})
    with open('imcs_mrg_test.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False)


if __name__ == '__main__':
    # url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n1496.xml.gz'
    # download_pubmed(url)
    # parse_xml('/Users/<USER>/Downloads/pubmed23n1516.xml')
    # result_list = parse_xml('pubmed23n1550.xml')
    # gen_excel(result_list,'pubmed23n1549.xml')
    # chatgpt_generate('翻译：i am a chinese')
    # jsonl2xlsx()
    # for i in range(1491,1553,1):
    #     url = 'https://ftp.ncbi.nlm.nih.gov/pubmed/updatefiles/pubmed23n{}.xml.gz'.format(i)
    #     download_pubmed(url)
    process_imcs()
    print('done')