# coding:utf-8
from web.web_utils.sql_util import select_by_sql, sql_query
from web.web_utils.db_util import execute_sql_many
from web.web_utils.text_util import get_uuid
from web.mrg_service.discharge_summary_llm import *
from web.mrg_service.cdss_llm import get_input_by_prompt_type
import json
import numpy as np
import pandas as pd


def get_prompt_task_data(request_data, config_map):
    customer_id = config_map['customer_id']
    request_data = json.loads(request_data.decode('utf-8'))
    department_name = request_data.get('department_name', '').strip()
    prompt_type = request_data.get('prompt_type', '').strip()
    task_list = get_prompt_task(customer_id, department_name, prompt_type, config_map)
    return task_list

def get_prompt_task(customer_id, department_name, prompt_type, config_map):
    task2department = {}
    sql = '''select ee.department_name,pp.sub_type,pp.sub_type_code,ee.task_id from llm_prompt_extend ee inner join llm_prompt pp on ee.prompt_id=pp.id
where ee.customer_id={} and ee.flag=1 and ee.task_id>0 and pp.sub_type='{}' and pp.flag=1 ;'''.format(customer_id, prompt_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    for x in xs:
        key = '{}_{}'.format(x['sub_type'], x['task_id'])
        if key not in task2department:
            task2department[key] = {}
        task2department[key][x['department_name']] = 1
    sql = '''
                    select tt.id task_id, tt.uuid task_uuid,tt.department_name,tt.prompt_type,tt.task_name,tt.record_ids,tt.create_date,
    dd.id task_detail_id,dd.customer_id,dd.record_id,dd.label_flag,dd.modify_date task_detail_modify_date
     from label_task tt inner join label_task_detail dd on tt.uuid=dd.task_uuid
     where tt.customer_id={} and tt.prompt_type='{}' and tt.department_name='{}' order by tt.create_date desc
                		 '''.format(customer_id, prompt_type, department_name)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    task_list = []
    uuid2task = {}
    for x in xs:
        x['create_date'] = str(x['create_date'])
        # label_flag_str = {0: '未标注', 1: 'llm_answer好', 2: 'human_answer好', 3: '平手'}[x['label_flag']]
        label_flag_str = ''
        task_detail_str = '<tr><td style=\'border-left:solid 1px #ccc;\'>{}</td><td>{}</td><td>{}</td><td>{}</td><td>{}</td><td>{}</td></tr>'.format(
            x['task_name'], x['create_date'], x['record_id'], label_flag_str, x['task_detail_modify_date'],
            '<a href=\'/to_prompt_task_detail_compare?task_detail_id={}&customer_id={}&record_id={}&prompt_type={}\' target=\'_blank\'>查看</a>'.format(
                x['task_detail_id'], x['customer_id'], x['record_id'],prompt_type))
        task_uuid = x['task_uuid']
        if task_uuid not in uuid2task:
            x['task_detail_str'] = task_detail_str
            x['is_use'] = '否'
            x['use_departments'] = ''
            kk = '{}_{}'.format(x['prompt_type'], x['task_id'])
            if kk in task2department:
                x['is_use'] = '是'
                x['use_departments'] = ','.join(sorted(list(task2department[kk].keys())))
            task_list.append(x)
            uuid2task[task_uuid] = 1
        else:
            task_list[-1]['task_detail_str'] += task_detail_str
    return task_list

def run_prompt_task(request_data, config_map):
    input_max_len = int(config_map['llm_model']['input_max_len'])
    request_data = json.loads(request_data.decode('utf-8'))
    department_name = request_data.get('department_name', '').strip()
    prompt_type = request_data.get('prompt_type', '').strip()
    is_rank = request_data.get('is_rank', '').strip()
    task_name = request_data.get('task_name', '').strip()
    start_num = request_data.get('start_num', '').strip()
    total_num = request_data.get('total_num', '').strip()
    cid = request_data.get('customer_id', '1001').strip()
    record_ids = request_data.get('record_ids', '').strip()
    prompt = request_data.get('prompt', '').strip()
    record_date = request_data.get('record_date', '').strip()
    prompt_extend = request_data.get('prompt_extend', '').strip()
    prompt_type_code = request_data.get('prompt_type_code', '').strip()
    task_detail_xs = []
    task_uuid = get_uuid()
    record_id_list = []
    inpatient_department = ''
    instruction = prompt
    if record_ids:
        record_id_list = [int(r) for r in record_ids.split(',')]
    else:
        sql = '''select rr.id record_id,rr.patient_guid,rr.customer_id,rr.patient_age,rr.patient_age_type, dd.discharge_department inpatient_department,rr.patient_gender,date_format(dd.discharge_time,'%Y-%m-%d %H:%i:%s') discharge_time from mt_patient_record rr
                    inner join mt_patient_discharge_record dd on dd.record_id=rr.id
                inner join mt_patient_progress pp on pp.record_id=rr.id
            where rr.customer_id = {} and rr.record_type = 2 and pp.progress_type=10 and dd.discharge_department='{}'
        '''.format(cid, department_name)
        xs = sql_query(cid, sql, config_map)
        record_id_list = []
        for x in xs:
            record_id_list.append(int(x['record_id']))
            cid = x['customer_id']
            inpatient_department = x['inpatient_department']
        if is_rank=='1':
            np.random.shuffle(record_id_list)
        if total_num:
            if start_num:
                record_id_list = record_id_list[int(start_num):int(total_num)]
            else:
                record_id_list = record_id_list[:int(total_num)]
        record_ids = ','.join([str(rid) for rid in record_id_list])

    for rid in record_id_list:
        # todo
        prompt_input, progress_list = get_input_by_prompt_type(cid, rid, prompt_type_code, record_date, config_map, input_max_len)
        if prompt_type_code in ['llm_cdss_gen_sqtl', 'llm_cdss_gen_sqxj', 'llm_cdss_gen_ssjl']:
            prompt_input = '拟采用的麻醉方式及术式:{}\n'.format(prompt_extend) + prompt_input
        prompt_input = prompt_input[:input_max_len]
        if prompt_input:
            prompt = prompt.replace('{input}', prompt_input).replace('{record_date}', record_date)
            prompt_answer_map = {}
            new_prompt_map = {prompt_type:prompt}
            asyncio.run(get_llm_answer(new_prompt_map, prompt_answer_map))
            llm_answer = prompt_answer_map[prompt_type]
        else:
            llm_answer = '很抱歉，未检索到患者近1月内的就诊信息，无法{}。'.format(prompt_type)
        task_detail_xs.append([task_uuid, prompt_type, prompt_input, llm_answer, cid, rid])

    task_xs = [[task_uuid, prompt_type, task_name, cid, record_ids,instruction,department_name]]
    sql = '''
                INSERT INTO label_task (uuid, prompt_type, task_name,customer_id, record_ids,instruction,department_name) VALUES  (%s, %s,%s, %s, %s, %s, %s);
        		 '''
    ret = execute_sql_many(sql, task_xs, 'hm_llm', config_map)
    sql = '''
                    INSERT INTO label_task_detail (task_uuid, prompt_type, prompt, llm_answer,customer_id, record_id) VALUES  (%s, %s, %s, %s, %s, %s);
            		 '''
    ret = execute_sql_many(sql, task_detail_xs, 'hm_llm', config_map)

    # task_list = get_task(prompt_type, config_map)
    msg = '任务已完成，请查看'
    return msg


def get_prompt_task_detail_compare(request_data, config_map):
    customer_id = request_data.get('customer_id', '').strip()
    record_id = request_data.get('record_id', '').strip()
    prompt_type = request_data.get('prompt_type', '').strip()
    customer_id = int(customer_id)
    record_id = int(record_id)
    sql = '''
     select  dd.id,dd.customer_id,dd.record_id,dd.prompt prompt_input, dd.label_flag,tt.instruction,dd.llm_answer,dd.label_answer,dd.prompt_type,dd.remark label_remark,date_format(dd.create_date,'%Y-%m-%d %H:%i:%s') task_detail_create_date,tt.task_name
     from label_task_detail dd inner join label_task tt on dd.task_uuid=tt.uuid
     where dd.customer_id={} and dd.record_id={} and dd.prompt_type='{}'
     order by dd.create_date desc
                		 '''.format(customer_id, record_id, prompt_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    label_detail_list = []
    for x in xs:
        prompt_version = '{}</br>{}</br>{}'.format(x['task_name'], x['prompt_type'], x['task_detail_create_date'])
        llm_input = '{}'.format(x['prompt_input']).replace('None', '')
        llm_answer = '{}'.format(x['llm_answer']).replace('None', '')
        # instruction = str(x['instruction']).replace('\n', '</br>')
        instruction = str(x['instruction'])
        label_detail_list.append({'label_detail_id':x['id'],'task_name':x['task_name'],'prompt_version':prompt_version, 'instruction':instruction, 'llm_answer':llm_answer, 'llm_input':llm_input})
    return label_detail_list


def get_cdss_llm_department(request_data, config_map):
    customer_id = config_map['customer_id']
    cdss_department_str = ''
    llm_department_str = ''
    cdss_department_list = []
    llm_department_list = []
    is_same = 0
    sql = '''
     select component from api_jssdk_library ajl where ajl.customer_id={} and product='cdss';
                		 '''.format(customer_id)
    xs = sql_query(customer_id, sql, config_map)
    for x in xs:
        component = {}
        if x['component'] and len(x['component']) > 5:
            component = eval(x['component'].replace('null', 'None').replace('false', '0').replace('true', '1'))
        if '$bigModelDepartments$' in component:
            cdss_department_list = component['$bigModelDepartments$'].split(',')
            cdss_department_str = ','.join(sorted(cdss_department_list))
    sql = '''
         select distinct department_name from llm_prompt_extend where customer_id={} and flag=1;
                    		 '''.format(customer_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    for x in xs:
        llm_department_list.append('{}'.format(x['department_name']))
    if llm_department_list:
        llm_department_str = ','.join(sorted(llm_department_list))
    if set(cdss_department_list) <= set(llm_department_list):
        is_same = 1
    return cdss_department_str, llm_department_str, is_same

def update_llm_department(request_data, config_map):
    customer_id = config_map['customer_id']
    cdss_department_list = []
    sql = '''
     select component from api_jssdk_library ajl where ajl.customer_id={} and product='cdss';
                		 '''.format(customer_id)
    xs = sql_query(customer_id, sql, config_map)
    for x in xs:
        component = {}
        if x['component'] and len(x['component']) > 5:
            component = eval(x['component'].replace('null', 'None').replace('false', '0').replace('true', '1'))
        if '$bigModelDepartments$' in component:
            cdss_department_list = component['$bigModelDepartments$'].split(',')
    sql = '''
         select distinct department_name from llm_prompt_extend where customer_id={} and flag=1;
                    		 '''.format(customer_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    llm_department_map = {}
    for x in xs:
        llm_department_map['{}'.format(x['department_name'])]=1

    sql = '''
                select id from llm_prompt where (type='llm_cdss' or type='出院小结') and flag=1 ;
            		 '''
    prompt_id_xs = select_by_sql(sql, 'hm_llm', config_map)

    sql = '''
                            INSERT INTO llm_prompt_extend (prompt_id, customer_id,department_name,flag) VALUES  (%s,%s, %s,%s);
                    		 '''
    insert_xs = []
    for cdss_department in cdss_department_list:
        if cdss_department not in llm_department_map:
            for p in prompt_id_xs:
                prompt_id = p['id']
                insert_xs.append([prompt_id, customer_id, cdss_department, 1])

    ret = execute_sql_many(sql, insert_xs, 'hm_llm', config_map)
    return "更新成功"


def update_llm_prompt_status(request_data, config_map):
    customer_id = config_map['customer_id']
    request_data = json.loads(request_data.decode('utf-8'))
    apply_type = request_data.get('apply_type', 1)
    prompt_type = request_data.get('prompt_type', '').strip()
    department_type = request_data.get('department_type', 0)
    departments = request_data.get('departments', '').strip()
    department_list = departments.split(',')
    task_id = request_data.get('task_id', 0)
    if apply_type == 2:
        task_id = 0
    if department_type == 2:
        department_list = []
    sql = '''
                                        update llm_prompt_extend ee inner join llm_prompt pp on ee.prompt_id=pp.id set ee.task_id=%s 
where ee.customer_id=%s and ee.flag=1 and pp.sub_type=%s and pp.flag=1
                                         '''
    values = [task_id, customer_id, prompt_type]
    if department_list:
        sql += '''and ee.department_name in ({})'''.format(', '.join(['%s'] * len(department_list)))
        values.extend(department_list)
    ret = execute_sql(sql, 'hm_llm', config_map, values)
    return "更新成功"


def get_department_prompt(request_data, config_map):
    customer_id = config_map['customer_id']
    department2prompt = {}
    sql = '''
         select ee.department_name,pp.sub_type,pp.sub_type_code from llm_prompt_extend ee inner join llm_prompt pp on ee.prompt_id=pp.id
where ee.customer_id={} and ee.flag=1 and pp.type='llm_cdss' and pp.flag=1 and pp.prompt is not null order by pp.rank desc;
                    		 '''.format(customer_id)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    for x in xs:
        if x['department_name'] not in department2prompt:
            department2prompt[x['department_name']] = []
        department2prompt[x['department_name']].append(x['sub_type'])
    return department2prompt


def get_current_prompt(request_data, config_map):
    customer_id = config_map['customer_id']
    request_data = json.loads(request_data.decode('utf-8'))
    department_name = request_data.get('department_name', '').strip()
    prompt_type = request_data.get('prompt_type', '').strip()
    prompt = ''
    prompt_type_code = ''
    sql = '''
         select ee.department_name,pp.sub_type,pp.sub_type_code,ifnull(tt.instruction,pp.prompt) prompt from llm_prompt_extend ee inner join llm_prompt pp on ee.prompt_id=pp.id
        		left join label_task tt on ee.task_id=tt.id
where ee.customer_id={} and ee.department_name='{}' and ee.flag=1 and pp.sub_type='{}' and pp.flag=1 ;
                    		 '''.format(customer_id, department_name, prompt_type)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    for x in xs:
        prompt = x['prompt']
        prompt_type_code = x['sub_type_code']
    return prompt,prompt_type_code
