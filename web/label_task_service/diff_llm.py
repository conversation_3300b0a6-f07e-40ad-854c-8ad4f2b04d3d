# coding:utf-8
from web.label_task_service.label_task import *
import json
import numpy as np
import pandas as pd
import openai


def diff_llm_result(cid, record_id_list, prompt_type, config_map):
    inpatient_department = '肝脏外科'
    prompt_map = get_prompt_map('出院小结', config_map)
    prompt_map2 = {prompt_type: prompt_map[prompt_type]}
    task_detail_xs = []
    for rid in record_id_list:
        raw_data = download_data(cid, rid, config_map)
        data = pd.DataFrame(raw_data)
        cyjl_map = get_cyjl_origin(data, config_map)
        human_answer = cyjl_map.get(prompt_type, '')
        new_prompt_map, prompt_answer_map, _ = update_prompt_map(prompt_map2, cid, rid,'', data, inpatient_department,
                                                              config_map, need_input=1)
        if new_prompt_map and not prompt_answer_map[prompt_type]:
            asyncio.run(get_llm_answer(new_prompt_map, prompt_answer_map))
        new_prompt_answer_map = update_prompt_answer_map(prompt_answer_map)

        # llm_input = new_prompt_map[prompt_type]
        # llm_answer = get_llm_result(llm_input, "/data/share_model/Qwen2-7B-Instruct")

        task_detail_xs.append([new_prompt_map[prompt_type], new_prompt_answer_map[prompt_type], cid, rid, human_answer, prompt_type])
    sql = '''
                        INSERT INTO temp_discharge_diff_model (prompt, qwen_api_answer,customer_id, record_id,human_answer, prompt_type) VALUES  (%s, %s,%s, %s, %s, %s);
                		 '''
    ret = execute_sql_many(sql, task_detail_xs, 'hm_llm', config_map)
    msg = '任务已完成，请查看'
    return msg


def update_llm_result(model_name, config_map):
    sql = '''
          select  id,prompt from temp_discharge_diff_model where {}_answer is null 
                    		 '''.format(model_name)
    xs = select_by_sql(sql, 'hm_llm', config_map)
    for i, x in enumerate(xs):
        prompt = x['prompt']
        result_id = x['id']
        llm_answer = ''
        if model_name=='qwen_14b':
            model_path = "/data/share_model/Qwen1.5-14B-Chat"
            llm_answer = get_llm_result(prompt, model_path)
        elif model_name=='qwen_7b':
            model_path = "/data/share_model/Qwen2-7B-Instruct"
            llm_answer = get_llm_result(prompt, model_path)
        elif model_name=='glm_9b':
            model_path = "/data/share_model/glm-4-9b-chat"
            llm_answer = get_llm_result(prompt, model_path)
        if not llm_answer:
            continue
        sql = '''
                    update temp_discharge_diff_model set {}_answer='{}' where id={};
                     '''.format(model_name,llm_answer, result_id)
        ret = execute_sql(sql, 'hm_llm', config_map)
        print('{}:{}/{}'.format(model_name, i, len(xs)))
    return 1

def get_llm_result(llm_input, model):
    # model = "/data/share_model/Qwen1.5-14B-Chat"
    # model = "/data/share_model/Qwen2-7B-Instruct"
    # model = "/data/share_model/glm-4-9b-chat"
    message_list = [{"role": "user", "content": llm_input}]
    openai.api_type = "open_ai"
    openai.api_base = "http://{}:{}/v1".format('218.205.95.254','8081')
    openai.api_key = "none"
    response = openai.ChatCompletion.create(
        model=model,
        top_p=0.01,
        timeout=30,
        max_tokens=1024,
        messages=message_list
    )
    choices = response.get('choices', [])
    if len(choices) > 0:
        finish_reason = choices[0].get("finish_reason", '')
        if finish_reason == 'stop':
            answer = choices[0].get("message", {}).get("content", '')
            if len(answer) > 0:
                return answer
    return ''