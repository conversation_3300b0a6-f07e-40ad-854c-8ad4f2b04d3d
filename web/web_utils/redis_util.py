#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Redis缓存工具，用于整个项目的缓存需求
"""

import json
import os
import pickle
import sys
from typing import Any, Dict, List, Optional, Tuple, Union

import redis

# 获取项目根目录
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import config_map


class RedisCache:
    """Redis缓存工具类，提供通用的缓存操作方法"""
    
    _instance = None
    
    def __new__(cls, *args, **kwargs):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super(RedisCache, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        """初始化Redis连接"""
        if self._initialized:
            return
            
        try:
            redis_config = config_map.get('redis', {})
            redis_addr = redis_config.get('redis_addr', '')
            redis_password = redis_config.get('redis_password', '')
            redis_db = int(redis_config.get('redis_db', 0))
            
            # 解析主机和端口
            if ':' in redis_addr:
                host, port = redis_addr.split(':')
                port = int(port)
            else:
                host = redis_addr
                port = 6379
                
            # 创建Redis连接池
            pool = redis.ConnectionPool(
                host=host,
                port=port,
                password=redis_password,
                db=redis_db,
                decode_responses=False,  # 不自动解码，以支持多种数据类型
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            self.client = redis.Redis(connection_pool=pool)
            self._initialized = True
            self._test_connection()
        except Exception as e:
            print(f"Redis连接初始化失败: {str(e)}")
            self.client = None
            
    def _test_connection(self):
        """测试Redis连接是否正常"""
        try:
            self.client.ping()
            return True
        except Exception as e:
            print(f"Redis连接测试失败: {str(e)}")
            return False
    
    def _serialize(self, data: Any) -> bytes:
        """序列化数据"""
        try:
            return pickle.dumps(data)
        except Exception:
            return json.dumps(data).encode('utf-8')
    
    def _deserialize(self, data: bytes) -> Any:
        """反序列化数据"""
        if data is None:
            return None
            
        try:
            return pickle.loads(data)
        except Exception:
            try:
                return json.loads(data.decode('utf-8'))
            except Exception:
                return data
                
    def set(self, key: str, value: Any, expire: int = None) -> bool:
        """
        设置缓存
        
        Args:
            key: 缓存键
            value: 缓存值，可以是任何可序列化的对象
            expire: 过期时间（秒），None表示永不过期
            
        Returns:
            bool: 设置是否成功
        """
        if self.client is None:
            return False
            
        try:
            serialized_value = self._serialize(value)
            if expire is None:
                return self.client.set(key, serialized_value)
            else:
                return self.client.setex(key, expire, serialized_value)
        except Exception as e:
            print(f"Redis设置缓存失败: {str(e)}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取缓存
        
        Args:
            key: 缓存键
            default: 默认值，如果缓存不存在则返回
            
        Returns:
            Any: 缓存值或默认值
        """
        if self.client is None:
            return default
            
        try:
            data = self.client.get(key)
            if data is None:
                return default
            return self._deserialize(data)
        except Exception as e:
            print(f"Redis获取缓存失败: {str(e)}")
            return default
    
    def delete(self, key: str) -> bool:
        """
        删除缓存
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 删除是否成功
        """
        if self.client is None:
            return False
            
        try:
            return bool(self.client.delete(key))
        except Exception as e:
            print(f"Redis删除缓存失败: {str(e)}")
            return False
    
    def exists(self, key: str) -> bool:
        """
        检查缓存是否存在
        
        Args:
            key: 缓存键
            
        Returns:
            bool: 是否存在
        """
        if self.client is None:
            return False
            
        try:
            return bool(self.client.exists(key))
        except Exception as e:
            print(f"Redis检查缓存失败: {str(e)}")
            return False
    
    def expire(self, key: str, seconds: int) -> bool:
        """
        设置缓存过期时间
        
        Args:
            key: 缓存键
            seconds: 过期秒数
            
        Returns:
            bool: 设置是否成功
        """
        if self.client is None:
            return False
            
        try:
            return bool(self.client.expire(key, seconds))
        except Exception as e:
            print(f"Redis设置过期时间失败: {str(e)}")
            return False
    
    def ttl(self, key: str) -> int:
        """
        获取缓存剩余生存时间
        
        Args:
            key: 缓存键
            
        Returns:
            int: 剩余秒数，-1表示永不过期，-2表示不存在
        """
        if self.client is None:
            return -2
            
        try:
            return self.client.ttl(key)
        except Exception as e:
            print(f"Redis获取剩余生存时间失败: {str(e)}")
            return -2
    
    # 列表操作
    def list_push(self, key: str, *values) -> int:
        """向列表右侧推入数据"""
        if self.client is None:
            return 0
            
        try:
            serialized_values = [self._serialize(v) for v in values]
            return self.client.rpush(key, *serialized_values)
        except Exception as e:
            print(f"Redis列表推入数据失败: {str(e)}")
            return 0
    
    def list_range(self, key: str, start: int = 0, end: int = -1) -> List[Any]:
        """获取列表指定范围的元素"""
        if self.client is None:
            return []
            
        try:
            data = self.client.lrange(key, start, end)
            return [self._deserialize(item) for item in data]
        except Exception as e:
            print(f"Redis获取列表范围失败: {str(e)}")
            return []
    
    # 哈希表操作
    def hash_set(self, name: str, key: str, value: Any) -> bool:
        """设置哈希表字段值"""
        if self.client is None:
            return False
            
        try:
            serialized_value = self._serialize(value)
            return bool(self.client.hset(name, key, serialized_value))
        except Exception as e:
            print(f"Redis设置哈希表失败: {str(e)}")
            return False
    
    def hash_get(self, name: str, key: str, default: Any = None) -> Any:
        """获取哈希表字段值"""
        if self.client is None:
            return default
            
        try:
            data = self.client.hget(name, key)
            if data is None:
                return default
            return self._deserialize(data)
        except Exception as e:
            print(f"Redis获取哈希表失败: {str(e)}")
            return default
    
    def hash_delete(self, name: str, *keys) -> int:
        """删除哈希表字段"""
        if self.client is None:
            return 0
            
        try:
            return self.client.hdel(name, *keys)
        except Exception as e:
            print(f"Redis删除哈希表字段失败: {str(e)}")
            return 0
    
    # 计数器操作
    def incr(self, key: str, amount: int = 1) -> int:
        """增加计数器"""
        if self.client is None:
            return 0
            
        try:
            return self.client.incrby(key, amount)
        except Exception as e:
            print(f"Redis增加计数器失败: {str(e)}")
            return 0
    
    def decr(self, key: str, amount: int = 1) -> int:
        """减少计数器"""
        if self.client is None:
            return 0
            
        try:
            return self.client.decrby(key, amount)
        except Exception as e:
            print(f"Redis减少计数器失败: {str(e)}")
            return 0
    
    # 批量操作
    def pipeline(self) -> 'redis.client.Pipeline':
        """获取管道对象，用于批量操作"""
        if self.client is None:
            raise Exception("Redis连接未初始化")
        return self.client.pipeline()
    
    def clear_all(self) -> bool:
        """清空当前数据库的所有键（谨慎使用）"""
        if self.client is None:
            return False
            
        try:
            return bool(self.client.flushdb())
        except Exception as e:
            print(f"Redis清空数据库失败: {str(e)}")
            return False


# 创建Redis缓存单例实例，方便直接导入使用
redis_cache = RedisCache() 