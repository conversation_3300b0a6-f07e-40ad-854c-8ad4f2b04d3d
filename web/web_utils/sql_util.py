# coding:utf-8
from web.web_utils.db_util import select_by_sql

sqls = {
    'chuyuan': '''
                select 
                    B.customer_id,
                    B.id as record_id,
                    B.record_type,
                    B.patient_age,
                    B.patient_age_type,
                    B.patient_gender,
                    B.inpatient_department,
                    C.id progress_id,
                    C.progress_text, C.progress_type,ss.record_name progress_type_zh,ifnull(D.msg_type,0) msg_type,C.record_time_format
                from ( 
                    select id, create_date, record_id, progress_message as progress_text, progress_type,record_time_format
                    from mt_patient_progress 
                    where customer_id = {0} and record_id = {1} and progress_status!=9 
                ) C
                inner join mt_patient_record B on B.id = C.record_id
                inner join mt_patient_progress_extend D on C.id=D.progress_id
                left join ms_system_record_type ss on ss.record_type=C.progress_type
                order by C.record_time_format
                ''',
    'progress_text': '''
                select 
                    B.customer_id,
                    B.id as record_id,
                    B.record_type,
                    B.patient_age,
                    B.patient_age_type,
                    B.patient_gender,
                    B.inpatient_department,
                    C.progress_text, C.progress_type,ifnull(D.msg_type,0) msg_type,C.record_time_format
                from ( 
                    select id, create_date, record_id, progress_message as progress_text, progress_type,record_time_format
                    from mt_patient_progress 
                    where customer_id = {0} and record_id = {1} and progress_type in ({2}) and progress_status!=9 
                ) C
                inner join mt_patient_record B on B.id = C.record_id
                inner join mt_patient_progress_extend D on C.id=D.progress_id
                order by C.create_date
                ''',
    "disease": """
                select distinct customer_id, record_id, disease_type, disease_name 
                from mt_patient_disease
                where customer_id={0} and record_id={1} and status=1
                """,
    "exam_":"""
                select examination_time, examination_name, examination_method,examination_part, examination_result
                from `mt_patient_examination` 
                where customer_id={0} and record_id={1} and examination_time is not null
                order by examination_time;""",
    "exam":"""
                select record_time_format as examination_time, examination_name, examination_method,examination_part, examination_result
                from `mt_patient_examination` 
                where customer_id={0} and record_id={1} 
                order by record_time_format desc;""",
    "test": """
    	select ii.create_date, ii.test_item, ii.test_result, ii.test_value_unit, ii.test_value_change,ii.qualitative_result_value,tt.test_name,ifnull(tt.test_date_time,tt.report_time) test_date_time,tt.create_date
                from {2}  ii
								inner join mt_patient_test tt on ii.test_id=tt.id
                where ii.customer_id={0} and ii.record_id={1}
                order by tt.test_date_time;
            """,
    "test_bad": """
    	select ii.create_date, ii.test_item, ii.test_result, ii.test_value_unit, ii.test_value_change,ii.qualitative_result_value,tt.test_name,tt.test_date_time,tt.create_date
                from {2}  ii
								inner join mt_patient_test tt on ii.test_id=tt.id
                where ii.customer_id={0} and ii.record_id={1}  and (lower(ii.test_value_change) = "h" or lower(ii.test_value_change) = "l" or ii.test_value_change = "1" 
                or lower(ii.qualitative_result_value)='l' or lower(ii.qualitative_result_value)='h')
                order by tt.test_date_time;
            """,
    'patient_progress': '''
                select 
                    B.customer_id,
                    B.id as record_id,
                    B.record_type,
                    B.patient_age,
                    B.patient_age_type,
                    B.patient_gender,
                    B.inpatient_department,
                    C.progress_text, C.progress_type,ss.record_name progress_type_zh,ifnull(D.msg_type,0) msg_type,C.record_time_format
                from ( 
                    select id, create_date, record_id, progress_message as progress_text, progress_type,record_time_format
                    from mt_patient_progress 
                    where customer_id = {0} and record_id = {1} and progress_status!=9 
                ) C
                inner join mt_patient_record B on B.id = C.record_id
                inner join mt_patient_progress_extend D on C.id=D.progress_id
                left join ms_system_record_type ss on ss.record_type=C.progress_type
                order by C.record_time_format
                '''
    }


# 查询DB
def sql_query(cid, sql, config_map):
    db_info = config_map.get(str(cid), {"db":"hmcdss2","sub_db":"hmcdss2"})
    xs = select_by_sql(sql, db_info["db"], config_map, db_info["sub_db"])
    return xs
