# coding:utf-8
import pymysql.cursors


def db_connect(db,config_map, sub_db):
    sql_host = config_map[db].get('sql_host','127.0.0.1')
    sql_user = config_map[db].get('sql_user','enginer')
    sql_port = int(config_map[db].get('sql_port','3306'))
    sql_pswd = config_map[db].get('sql_pswd','dataisbest')
    if sub_db:
        sql_db = sub_db
    else:
        sql_db = config_map[db].get('sql_db', 'hmcdss2')
    db_connection = pymysql.connect(host=sql_host,
                                    user=sql_user,
                                    port=sql_port,
                                    password=sql_pswd,
                                    db=sql_db,
                                    charset='utf8',
                                    cursorclass=pymysql.cursors.DictCursor)
    return db_connection


def select_by_sql_ssh(sql):
    result = []
    try:
        from sshtunnel import SSHTunnelForwarder
        with SSHTunnelForwarder(
                ssh_address_or_host=('***********', 22),  # 指定ssh登录的跳转机的address
                ssh_username='huimei',  # 跳转机的用户
                ssh_password='HuiMei2017',  # 跳转机的密码
                remote_bind_address=('***********', 3306)
        ) as server:
            db = 'hmcdss2'
            myConfig = pymysql.connect(
                user="enginer",
                passwd="dataisbest",
                host="127.0.0.1",  # 此处必须是 127.0.0.1
                db=db,
                port=server.local_bind_port)
            cursor = myConfig.cursor(pymysql.cursors.DictCursor)
            cursor.execute(sql)
            result = cursor.fetchall()
            cursor.close()
    finally:
        return result

def select_by_sql(sql, db, config_map, sub_db='', params=None):
    if config_map[db].get('sql_host','127.0.0.1').find('mysql_test')>-1:
        return select_by_sql_ssh(sql)
    db_connection = db_connect(db, config_map, sub_db)
    result = []
    try:
        with db_connection.cursor() as cursor:
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            result = cursor.fetchall()
            # print('1111')
    finally:
        db_connection.close()
        return result


def execute_sql(sql, db, config_map, values=(), sub_db=''):
    db_connection = db_connect(db, config_map, sub_db)
    ret = -1
    try:
        with db_connection.cursor() as cursor:
            if len(values) > 0:
                ret = cursor.execute(sql, values)
            else:
                ret = cursor.execute(sql)
            # print('delete', ret)
            db_connection.commit()
    finally:
        # print('error!, sql:%s, db:%s, sub_db:%s' %(sql, db, sub_db))
        db_connection.close()
    return ret


def execute_sql_many(sql, xs, db, config_map, sub_db=''):
    db_connection = db_connect(db, config_map, sub_db)
    re = -1
    try:
        with db_connection.cursor() as cursor:
            length = 300
            for i in range(0, len(xs), length):
                re = cursor.executemany(sql, xs[i:i+length])
                if re:
                    db_connection.commit()
    finally:
        db_connection.close()
    return re
