import os
import re
import csv
import requests
import json
from web.web_utils.db_util import select_by_sql
from web.config import config_map


class DataUtil:
    _instance = None
    _table_columns_path = os.path.join(os.path.dirname(__file__), '../web_data/table_columns_info.csv')
    _default_db = 'hmcdss2'  # 添加默认数据库名称

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(DataUtil, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if hasattr(self, 'initialized'):
            return
        self.initialized = True
        self.config_map = config_map
        self.origin_progress_url = f"http://{config_map['app']['pangoo_ip']}/go_pangoo/pangoo/progress/getPatientProgress"
        self.progress_attribute_url = f"http://{config_map['app']['pangoo_ip']}/go_pangoo/pangoo/progress/getParsedPatientProgress"
        self.table_columns = {}
        self.record_type_map = {}  # 存储record_type到record_name和mt_table_name的映射
        self.table_column_name_map = {}   # 存储表字段的英文名到中文名的映射
        self.table_name_map = {}   # 存储表的英文名到中文名的映射
        self.table_filter_map = {} # 存储表的过滤条件
        self.attribute_type_map = {} # 存储attribute_type到attribute_name的映射
        self.load_system_info()

    def get_patient_data(self, customer_id, record_id, metadata):
        """
        主入口，根据record_type获取文书与mt表单数据
        Args:
            customer_id (_type_): 
            record_id (_type_): 
            metadata (_type_): [{
                'recordType': 0, // 与 ms_system_record_type表中record_type对应
                'attributeIds': [],
                'isDoc': true
            }, {
                'recordType': 12004,
                'tableName': 'mt_patient_record_first_disease',
                'columns': [
                    'disease_name',
                    'is_main_disease'
                ],
                'isDoc': false
            }, ...] 


        Returns:
            _type_: {
                record_type_id: [{
                    '文书内容': "",
                    '创建时间': ""
                }]
            }
        """
        progress_cache = {}
        result = {}
        metadata.sort(key=lambda x: x.get('recordType', -1))
        for row in metadata:
            record_type_id = row.get('recordType')
            if row.get('tableName'):
                en_name = row.get('tableName')
                record_type_name = self.to_cn_name(en_name)
                new_data = self.get_table_columns(record_id, en_name, row.get('columns', []))
            else:
                if not record_type_id and record_type_id != 0:
                    continue
                record_type_name = self.to_cn_name(record_type_id)
                new_data = self.get_progress_data(customer_id, record_id, record_type_id, row.get('attributeIds', []), progress_cache)
            result.setdefault(record_type_name, []).extend(new_data)
        return result
    
    def get_progress_data(self, customer_id, record_id, record_type_id, attribute_ids, progress_cache):
        if attribute_ids:
            data = self._post_progress_attribute_data(customer_id, record_id, record_type_id)
            data = self.filter_by_attribute_ids(data, attribute_ids)
        else:
            response = self._get_progress_data_by_cache(customer_id, record_id, progress_cache)
            data = response.get(record_type_id, [])
        result = self._progress_process(data)
        return result
    
    def filter_by_attribute_ids(self, data, attribute_ids):
        if not attribute_ids:
            return data
        for row in data:
            if 'progresses' in row:
                new_progresses = []
                for attr_progress in row['progresses']:
                    if attr_progress.get('dictionaryAttributeId') in attribute_ids:
                        new_progresses.append(attr_progress)
                row['progresses'] = new_progresses
        return data
    
    def make_parse_content(self, attr_name, attr_value):
        # 解析文书中包含字段标题，这里处理一下防止内容重复
        regex = r'\s*'.join(attr_name)
        match = re.match(regex, attr_value[:10])
        if match:
            return attr_value
        title = re.match(r'^.{2,10}[:：]?', attr_value[:15])
        if title:
            if set(title.group()) & set(attr_name):
                return attr_value
        return f'{attr_name}: {attr_value}'

    def _progress_process(self, data):
        # 文书数据统一格式
        if not data:
            return []
        result = []
        for row in data:
            new_row = {}
            if 'progresses' in row:
                for attr_progress in row['progresses']:
                    attr_name = self.attribute_type_map.get(attr_progress.get('dictionaryAttributeId'), '')
                    if not attr_name:
                        continue
                    attr_name = '^' + attr_name
                    attr_value = attr_progress.get('attributeValue', '')
                    new_row[attr_name] = attr_value
            for key, value in row.items():
                if not value:
                    continue
                cn_key = self._progress_key_to_cn(key)
                if not cn_key:
                    continue
                new_row[cn_key] = value
            result.append(new_row)
        return result
    
    def _post_progress_attribute_data(self, customer_id, record_id, record_type_id):
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            payload = {
                'customerId': customer_id,
                'recordId': record_id,
                'recordTypes': [record_type_id],
                'progressIds': []
            }
            
            response = requests.post(self.progress_attribute_url, headers=headers, data=json.dumps(payload))
            if response.status_code == 200:
                result = response.json()
                # 处理返回的数据
                if 'body' in result:
                    return result.get('body', [])
                else:
                    print(f"请求解析文书接口失败: {self.progress_attribute_url}，err: {result.get('msg', '')}")
            else:
                print(f"请求解析文书接口失败: {self.progress_attribute_url}，状态码: {response.status_code}")
        except Exception as e:
            print(f"请求解析文书接口失败: {self.progress_attribute_url}，err: {str(e)}")
        return []

    def _get_progress_data_by_cache(self, customer_id, record_id, cache):
        if not cache:
            cache.update({-1: []})
            result = self._post_pangoo_request(customer_id, record_id)
            if result:
                for row in result:
                    cache.setdefault(row.get('progressType'), []).append(row)
        return cache

    def _post_pangoo_request(self, customer_id, record_id):
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            payload = {
                'customerId': customer_id,
                'recordId': record_id
            }
            
            response = requests.post(self.origin_progress_url, headers=headers, data=json.dumps(payload))
            if response.status_code == 200:
                result = response.json()
                # 处理返回的数据
                if 'body' in result:
                    return result.get('body', [])
                else:
                    print(f"请求文书原文接口失败: {self.origin_progress_url}，err: {result.get('msg', '')}")
            else:
                print(f"请求文书原文接口失败: {self.origin_progress_url}，状态码: {response.status_code}")
        except Exception as e:
            print(f"请求文书原文接口失败: {self.origin_progress_url}，err: {str(e)}")
        
        return []
    
    def _progress_key_to_cn(self, key):
        return {
            # 'completionTime': '文书完成时间',
            'progressText': '文书内容',
            'progressTitleName': '文书标题',
            # 'progressType': '文书类型',
            'recordTimeFormat': '文书创建时间',
            # 'progressTemplateName': '客户文书模版名称',
            # 'progressTypeName': '客户文书名称',
            # 'progressId': '文书ID',
            # 'progressGuid': '客户文书唯一标识',
            # 'doctorName': '医师签名',
            'documentTitleTime': '标题时间',
            'firstSignTime': '首次签名时间',
            # 'rawRecordTimeFormat': '原始文书创建时间'
        }.get(key, '')
    
    def is_ot_patient(self, record_id):
        # 判断是否为门诊患者 1.门诊患者，2.住院患者
        sql = 'select record_type from hmcdss2.mt_patient_record where id=%s'
        query_result = select_by_sql(sql, self._default_db, self.config_map, params=(record_id, ))
        for row in query_result:
            if row.get('record_type') == 1:
                return True
        return False
    
    def to_cn_name(self, key):
        # 文书或表单名称转为中文
        if key in self.record_type_map:
            return self.record_type_map.get(key, {}).get('record_name', '')
        if key in self.table_name_map:
            return self.table_name_map.get(key, '')
        return key

    def get_table_columns(self, record_id, table_name, columns):
        # 结构化表单根据 hm_mc_platform.mc_rule_table_catlog 拼接sql
        # 获取表的过滤条件
        filter_condition = self.table_filter_map.get(table_name, {})
        record_id_key = filter_condition.get('record_id_key', 'record_id')
        query_condition = filter_condition.get('query_condition', '')
        where_clause = f"{record_id_key} = {record_id}"
        
        # 添加其他过滤条件
        if query_condition:
            where_clause += f" AND {query_condition}"
        
        # 获取需要查询的列
        if not columns:
            columns = self.table_columns.get(table_name, [])
            if not columns:
                columns = ['id']

        columns_str = ", ".join(columns)
    
        # 构建SQL查询
        sql = f"""
        SELECT {columns_str}
        FROM hmcdss2.{table_name}
        WHERE {where_clause}
        """
        
        # 执行查询
        query_result = select_by_sql(sql, self._default_db, self.config_map)
        
        # 转换列名(英文到中文)
        result = []
        for row in query_result:
            formatted_row = {}
            for col in columns:
                # 获取列的中文名称
                col_cn = self.table_column_name_map.get(f"{table_name}.{col}", col)
                formatted_row[col_cn] = row.get(col, "")
            result.append(formatted_row)
        return result

    def load_system_info(self):
        # 从_table_columns_path文件中读取出需要读取的表字段
        self._load_table_columns()

        # 从 hm_mc_platform.mc_platform_rule_table_dict 中读取表中英文映射名称
        self._load_table_name_map()

        # 从 hm_mc_platform.mc_platform_rule_table_catlog 表中读取表所需的过滤条件
        self._load_table_filter_conditions()

        # 从 hmcdss2.ms_system_record_type 表中读取 record_type, record_name, mt_table_name 映射关系
        self._load_record_type_map()

        # 从 hmcdss2.ms_attribute_type 表中读取 attribute_type, attribute_name 映射关系
        self._load_attribute_type_map()
        
    def _load_table_columns(self):
        try:
            """
            SELECT 
                table_cn_name table_name, table_name table_en_name, 
                column_cn_name column_name, column_name column_en_name
            FROM hm_mc_platform.mc_platform_table_dict
            """
            table_columns = {}
            with open(self._table_columns_path, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                next(csv_reader)  # 跳过表头
                for row in csv_reader:
                    if len(row) >= 2:
                        table_name = row[1]
                        column_name = row[3]
                        if table_name not in table_columns:
                            table_columns[table_name] = []
                        table_columns[table_name].append(column_name)
            self.table_columns = table_columns
        except Exception as e:
            print(f"读取表字段信息出错: {str(e)}")

    def _load_table_name_map(self):
        try:
            table_column_name_map = {}
            table_name_map = {}
            sql = """
            SELECT table_name, table_cn_name, column_name, column_cn_name
            FROM hm_mc_platform.mc_platform_table_dict
            """
            result = select_by_sql(sql, self._default_db, self.config_map, 'hm_mc_platform')

            for item in result:
                table_name = item.get('table_name', '')
                table_cn_name = item.get('table_cn_name', '')
                column_name = item.get('column_name', '')
                column_cn_name = item.get('column_cn_name', '')

                key = f"{table_name}.{column_name}"
                table_name_map[table_name] = table_cn_name
                table_column_name_map[key] = column_cn_name

            self.table_column_name_map = table_column_name_map
            self.table_name_map = table_name_map
        except Exception as e:
            print(f"读取表中英文映射名称出错: {str(e)}")

    def _load_table_filter_conditions(self):
        try:
            table_filter_map = {}
            sql = """
            SELECT table_en_name, record_id_to_foreign_key, query_condition_postfix 
            FROM hm_mc_platform.mc_platform_table_catalog
            """
            result = select_by_sql(sql, self._default_db, self.config_map, 'hm_mc_platform')

            for item in result:
                table_name = item.get('table_en_name', '')
                record_id_key = item.get('record_id_to_foreign_key', '')
                query_condition = item.get('query_condition_postfix', '')

                if table_name not in table_filter_map:
                    table_filter_map[table_name] = {}

                table_filter_map[table_name]['record_id_key'] = record_id_key
                table_filter_map[table_name]['query_condition'] = query_condition
            self.table_filter_map = table_filter_map
        except Exception as e:
            print(f"读取表过滤条件出错: {str(e)}")

    def _load_record_type_map(self):
        try:
            record_type_map = {}
            sql = """
            SELECT record_type, record_name, mt_table_name, ot_table_name
            FROM hmcdss2.ms_system_record_type
            """
            result = select_by_sql(sql, self._default_db, self.config_map, 'hmcdss2')

            for item in result:
                record_type = item.get('record_type', 0)
                record_name = item.get('record_name', '')
                mt_table_name = item.get('mt_table_name', '')
                ot_table_name = item.get('ot_table_name', '')
                record_type_map[record_type] = {
                    'record_name': record_name,
                    'mt_table_name': mt_table_name,
                    'ot_table_name': ot_table_name
                }
            self.record_type_map = record_type_map
        except Exception as e:
            print(f"读取record_type映射关系出错: {str(e)}")

    def _load_attribute_type_map(self):
        try:
            sql = """
            select id, attribute_name from hmcdss2.ms_dictionary_attribute
            """
            result = select_by_sql(sql, self._default_db, self.config_map, 'hmcdss2')
            
            attribute_type_map = {}
            for item in result:
                attribute_type_map[item.get('id')] = item.get('attribute_name', '')
            self.attribute_type_map = attribute_type_map
        except Exception as e:
            print(f"读取attribute_type映射关系出错: {str(e)}")
        