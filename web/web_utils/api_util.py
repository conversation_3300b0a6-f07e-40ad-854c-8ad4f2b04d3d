# coding:utf-8
import time

import requests
import json

is_run = False
def post_url(url, json_obj, headers={"Content-Type": "application/json;charset=UTF-8"}):
    res = {}
    try:
        json_str = json.dumps(json_obj)
        r = requests.post(url, headers=headers, data=json_str)
        res = json.loads(r.text)
        return res
    except requests.exceptions.RequestException as e:
        print(e)
        return res

def get_zljg_from_api(name):
    print(name)
    answer = ''
    # global is_run
    # print(is_run)
    start = time.time()
    # while not is_run:
    # if 1:
    for i in range(1):
        # is_run = True
        prompt="问题:\n入院记录\n【主诉】 上腹部不适2月,黑便2周,呕血1周,加重3小时。 【体格检查】 t:36.3°c p:70次/分 r:16次/分 bp: 125/67mmhg 神志差,精神差,呼吸急促,营养不良,表情痛苦,发育正常,自主体位,呼之不答,查体欠佳。全身皮肤无黄染,无肝掌、蜘蛛痣。全身浅表淋巴结无肿大,头颅无畸形,巩膜无黄染、眼球无突出、瞳孔等大等圆、对光反射灵敏,颈软,气管居中,甲状腺未及肿大,胸廓无畸形,双肺呼吸音清,未及干湿罗音。心前区无隆起,心界不大,心律齐,各瓣膜区未及病理性杂音。腹部平软,无压痛及反跳痛,肝脾肋下未及,肝肾区无叩击痛,肠鸣音4次/分,移动性浊音(-),双下肢不肿,双侧足背动脉搏动可 。肛门及生殖器未检,四肢脊柱无畸形,活动自如,神经系统检查(-)。 【现病史】 患者上腹部不适伴纳差2月余,10-3 饮3两黄酒后因黑便、出冷汗就诊外院,查wbc 13.44,n 64.5%,hb 99g/l,plt 313,hct 30.8%,rbc 3.58,crp 19mg/l;血钾 3.5mmol/l,scr 75umol/l,bun 9.1mmol/l,amy 51u/l,alt/ast/ck/心肌标志物(-)。pt 12.4s,aptt 26.5s,fib 3.7g/l,d二聚体 1.0mg/l。头胸ct:腔隙性脑梗塞,建议进一步mr;两肺多发小结节,建议随访;两侧腋下淋巴结增多,部分临界大小,两肺纤维灶;附见:胃腔扩张,见团片样高密度影,结合临床随访。补液治疗后(具体用药不详)好转。但黑便持续存在,10-08胃口不佳,未食晚餐,夜间10点、凌晨1点半呕血3次,呕血暗红色伴血块,量多共约>1000ml,出现晕厥,伴冷汗,伴黑便,具体量次不详;晕厥3-4分钟后恢复,后就诊第五人民医院,wbc 12.26,n 50.6%,hb 47g/l,plt 298,hct 15.2%,rbc 1.69;生化:血钾 2.8mmol/l,scr 70umol/l,bun 9.5mmol/l,amy 53u/l;凝血功能:pt 12.3s,aptt 21.9s,fib 2.9g/l,d二聚体 1.36mg/l。10-9上下腹部ct平扫:与2022-10-03相比,胃腔内高密度较前减少;下腹部未见明显异常。外院予禁食禁水,以多巴胺、抑酸护胃、生长抑素、羟乙基淀粉、补钾补液等治疗,10-9凌晨转至我院急诊。急诊查hb 38g/l,plt 222 x10^9/l,wbc 14.10x10^9/l,白蛋白 20g/l;k+ 3.8mmol/l,淀粉酶 49u/l,d-二聚体 0.71mg/l,心肌肌钙蛋白t:0.049ng/ml;氨基末端利钠肽前体:116.1pg/ml;胸腹盆ct:左下肺小结节,随访;两肺少许慢性炎症及渗出,两侧胸腔极少量积液。贫血症。胃窦壁局部稍增厚,建议必要时内镜检查。小肠局部积气。予告病危,禁食禁水、深静脉置管、胃管胃肠减压,患者入院后未再出现呕血、呕吐,无明显腹痛,未解大便,留置胃肠减压1小时后开始可见暗红色物质逐渐流出后边褐色胃内容物,急诊内镜考虑胃角溃疡(forrest a1,iia),行止血术,予生长抑素、多巴胺、抑酸护胃止血、拉氧头孢抗感染、补液支持、白蛋白,输血1u*2次。为进一步诊治于223-10-10收入消化留观病房。入观后予禁食、抑酸护胃、补铁、补液、白蛋白营养支持等对症治疗,2023-10-13饮水后无腹痛等不适,复查血红蛋白57g/l,予开放流质饮食,继续抑酸护胃、补铁,拔除导尿管和胃管,予康复新液10ml tid口服保护粘膜。2023-11-14患者于夜间23:00外出如厕下床时因双下肢无力摔倒,当时神志清醒,血压72/43mmhg,血氧饱和度98%,心率66bpm,嘱患者卧床,在卧床过程中呕血20ml,立刻予心电监护、扩容、多巴胺升压、止血、抑酸、申请输血,急查血常规、crp、出凝血功能,联系急诊内镜、普外科,告知患者家属患者病情危重,随时有死亡风险,家属表示知晓;23:30复测血压70/40mmhg,心率62bpm,予多巴胺畅滴,23:45患者再次呕大量鲜血伴血块,血压106/54mmhg,心率107bpm,继续心电监护、扩容、多巴胺畅滴;凌晨1:00患者血压109/54mmhg,1:20患者外出行手术治疗,当时血压80/41mmhg,转运途中患者再次呕大量鲜血,血压70/40左右。现患者为进一步诊治收入我科。目前患者禁食中,近期体重无明显变化。 【既往史】 疾病史:既往无消化道出血病史。  【系统回顾】呼吸系统:无咳嗽、咳痰、咯血、呼吸困难、发冷、发热、盗汗  循环系统:无心悸、心前区疼痛;无呼吸困难、水肿、尿少等  消化系统:详见现病史  泌尿生殖系统:无排尿困难;无尿频、尿急、尿痛;无水肿  血液系统:无乏力、头晕、耳鸣、皮肤苍白、出血点、瘀斑、血肿及肝脾、淋巴结肿大  内分泌系统:无畏寒、怕热、多汗、乏力、视力障碍、食欲异常、烦渴、多尿  神经精神系统:无头痛、记忆力减退、意识障碍、晕厥、痉挛、感觉及运动异常  运动骨骼系统:无肢体肌肉麻木、疼痛、痉挛、萎缩、瘫痪;无关节肿痛、骨折 【专科检查】 贫血貌,消瘦;全身皮肤无黄染,无肝掌、蜘蛛痣,全身浅表淋巴结无明显肿大,胸壁无畸形,双侧呼吸动度对称一致,双肺呼吸音清,未闻及明显干湿啰音。心脏听诊律齐,未闻及明显杂音和异常心音。 【辅助检查】腹部增强ct【我院、2023-10-11、zs23340512】胃壁水肿增厚,请结合内镜检查;左肾囊肿。\n入院记录中是否与「1.胃溃疡伴出血」诊断，请分别回答是否有关联依据？若有关联，则回答“有关联”与关联的依据；若无关联，则回答“无关联”与无关联的依据；请按诊断分别回答:"
        json_obj = {"input":prompt}
        chat_url = 'http://223.72.199.109:8001/generate'
        return_json = post_url(chat_url, json_obj)
        answer = return_json.get('response','')
        # print(answer)
        end = time.time()
        print('请求{},done time:{}s'.format(name, str(end - start)[:4]))
        # is_run = False
    return answer
import openai
prompt_map = {
    "32": "若我有一亿美元，在人工智能盛行的今天，我怎样投资才能收益最大化？",
    "256": "糕点商店里原本有三种蛋糕：草莓奶油蛋糕，巧克力椰蓉蛋糕，和红丝绒布朗尼蛋糕。如名字所描述的那样，每种蛋糕都有两种成分：草莓奶油蛋糕包含草莓和奶油两个成分，巧克力椰蓉蛋糕包含巧克力和椰蓉两种成分，红丝绒布朗尼蛋糕包含红丝绒和布朗尼两种成分。在蛋糕制作完成后，往往每>种成分的材料都会有所剩余。为了减少浪费，商店常常会把多出来的成分两两搭配，做成新的小商品卖出去。比如草莓和巧克力可以做成草莓味巧克力酱，布朗尼和椰蓉可以做成布朗尼椰蓉饼干。以此类推可知，如果所有的成分都可以两两组合，那么最终商店能做出哪些小商品出来？",
    "512": "桌子有左中右3个抽屉；张三，李四，王五，赵六都看到桌子上有一袋巧克力。张三让李四和王五出门后，在赵六面前把这袋巧克力放进了右抽屉；王五回来后，张三让赵六出门去找李四，并在王五面前从左抽屉拿出一盒饼干放进中抽屉里；等李四和赵六返回，张三又让王五和赵六出去买酱油，等二人走后，他告诉李四刚才已将一盒饼干放进中抽屉；张三等了很久，发现王五和赵六还没回来，就派李四去寻找，可最后只有王五和李四回来了。王五告诉张三，一开始他们没有找到卖酱油的店，所以只好分头去买，后来赵六走丢了；回来的路上，王五碰上了李四，两人便先赶了回来。于是，张三让两人出门去找赵六；为防再次走丢，张三叮嘱李四和王五要时刻同行，就算酱油买不到，也要找回赵六。结果，李四和王五在外面找到了赵六，发现他已经买了酱油。三人觉得张三从来不出门跑腿，十分气愤，讨论并达成共识，回去见到张三后，不要告诉他买到了酱油的事情，并让王五把酱油藏到自己的背包里。等三人一同回来后，他们按照计划谎称没有买到酱油，并希望张三以后买东西也要一同出门，不能偷懒，张三答应了。当大家最后站在桌子前，四人分别写下自己知道的物品清单和物品所在位置。问，这四人写下的物品和位置信息是否一致，为什么？",
    "1024": "折纸的过程看似简单，其实想要做好，还是需要一套很复杂的工艺。以折一支玫瑰花为例，我们可以将整个折纸过程分成三个阶段，即：创建栅格折痕，制作立体基座，完成花瓣修饰。首先是创建栅格折痕：这一步有点像我们折千纸鹤的第一步，即通过对称州依次对折，然后按照长和宽两个维度，依次进行多等分的均匀折叠；最终在两个方向上的折痕会交织成一套完整均匀的小方格拼接图案；这些小方格就组成了类似二维坐标系的参考系统，使得我们在该平面上，通过组合临近折痕的方式从二维小方格上折叠出三维的高台或凹陷，以便于接下来的几座制作过程。需要注意的是，在建立栅格折痕的过程中，可能会出现折叠不对成的情况，这种错误所带来的后果可能是很严重的，就像是蝴蝶效应，一开始只是毫厘之差，最后可能就是天壤之别。然后是制作立体基座：在这一步，我们需要基于栅格折痕折出对称的三维高台或凹陷。从对称性分析不难发现，玫瑰花会有四个周对称的三维高台和配套凹陷。所以，我们可以先折出四分之一的凹陷和高台图案，然后以这四分之一的部分作为摸板，再依次折出其余三个部分的重复图案。值得注意的是，高台的布局不仅要考虑长和宽这两个唯独上的规整衬度和对称分布，还需要同时保证高这个维度上的整齐。与第一阶段的注意事项类似，请处理好三个维度上的所有折角，确保它们符合计划中所要求的那种布局，以免出现三维折叠过程中的蝴蝶效应；为此，我们常常会在折叠第一个四分之一图案的过程中，与成品玫瑰花进行反复比较，以便在第一时间排除掉所有可能的错误。最后一个阶段是完成花瓣修饰。在这个阶段，我们往往强调一个重要名词，叫用心折叠。这里的用心已经不是字面上的认真这个意思，而是指通过我们对于大自然中玫瑰花外型的理解，借助自然的曲线去不断修正花瓣的形状，以期逼近现实中的玫瑰花瓣外形。请注意，在这个阶段的最后一步，我们需要通过拉扯已经弯折的四个花瓣，来调整玫瑰花中心的绽放程度。这个过程可能会伴随玫瑰花整体结构的崩塌，所以，一定要控制好调整的力道，以免出现不可逆的后果。最终，经过三个阶段的折叠，我们会得到一支栩栩如生的玫瑰花冠。如果条件允许，我们可以在一根拉直的铁丝上缠绕绿色纸条，并将玫瑰花冠插在铁丝的一段。这样，我们就得到了一支手工玫瑰花。总之，通过创建栅格折痕，制作立体基座，以及完成花瓣修饰，我们从二维的纸面上创作出了一支三维的花朵。这个过程虽然看似简单，但它确实我们人类借助想象力和常见素材而创作出的艺术品。请赏析以上内容的精妙之处。",
    "24": "请以单侧肢体麻木3天为主诉生成300字的门诊病历"
}
prompt_list = list(prompt_map.values())
def hmgpt_generate(name):
    # print(name)
    message_list = []
    # prompt = "问题:\n入院记录\n【主诉】 上腹部不适2月,黑便2周,呕血1周,加重3小时。 【体格检查】 t:36.3°c p:70次/分 r:16次/分 bp: 125/67mmhg 神志差,精神差,呼吸急促,营养不良,表情痛苦,发育正常,自主体位,呼之不答,查体欠佳。全身皮肤无黄染,无肝掌、蜘蛛痣。全身浅表淋巴结无肿大,头颅无畸形,巩膜无黄染、眼球无突出、瞳孔等大等圆、对光反射灵敏,颈软,气管居中,甲状腺未及肿大,胸廓无畸形,双肺呼吸音清,未及干湿罗音。心前区无隆起,心界不大,心律齐,各瓣膜区未及病理性杂音。腹部平软,无压痛及反跳痛,肝脾肋下未及,肝肾区无叩击痛,肠鸣音4次/分,移动性浊音(-),双下肢不肿,双侧足背动脉搏动可 。肛门及生殖器未检,四肢脊柱无畸形,活动自如,神经系统检查(-)。 【现病史】 患者上腹部不适伴纳差2月余,10-3 饮3两黄酒后因黑便、出冷汗就诊外院,查wbc 13.44,n 64.5%,hb 99g/l,plt 313,hct 30.8%,rbc 3.58,crp 19mg/l;血钾 3.5mmol/l,scr 75umol/l,bun 9.1mmol/l,amy 51u/l,alt/ast/ck/心肌标志物(-)。pt 12.4s,aptt 26.5s,fib 3.7g/l,d二聚体 1.0mg/l。头胸ct:腔隙性脑梗塞,建议进一步mr;两肺多发小结节,建议随访;两侧腋下淋巴结增多,部分临界大小,两肺纤维灶;附见:胃腔扩张,见团片样高密度影,结合临床随访。补液治疗后(具体用药不详)好转。但黑便持续存在,10-08胃口不佳,未食晚餐,夜间10点、凌晨1点半呕血3次,呕血暗红色伴血块,量多共约>1000ml,出现晕厥,伴冷汗,伴黑便,具体量次不详;晕厥3-4分钟后恢复,后就诊第五人民医院,wbc 12.26,n 50.6%,hb 47g/l,plt 298,hct 15.2%,rbc 1.69;生化:血钾 2.8mmol/l,scr 70umol/l,bun 9.5mmol/l,amy 53u/l;凝血功能:pt 12.3s,aptt 21.9s,fib 2.9g/l,d二聚体 1.36mg/l。10-9上下腹部ct平扫:与2022-10-03相比,胃腔内高密度较前减少;下腹部未见明显异常。外院予禁食禁水,以多巴胺、抑酸护胃、生长抑素、羟乙基淀粉、补钾补液等治疗,10-9凌晨转至我院急诊。急诊查hb 38g/l,plt 222 x10^9/l,wbc 14.10x10^9/l,白蛋白 20g/l;k+ 3.8mmol/l,淀粉酶 49u/l,d-二聚体 0.71mg/l,心肌肌钙蛋白t:0.049ng/ml;氨基末端利钠肽前体:116.1pg/ml;胸腹盆ct:左下肺小结节,随访;两肺少许慢性炎症及渗出,两侧胸腔极少量积液。贫血症。胃窦壁局部稍增厚,建议必要时内镜检查。小肠局部积气。予告病危,禁食禁水、深静脉置管、胃管胃肠减压,患者入院后未再出现呕血、呕吐,无明显腹痛,未解大便,留置胃肠减压1小时后开始可见暗红色物质逐渐流出后边褐色胃内容物,急诊内镜考虑胃角溃疡(forrest a1,iia),行止血术,予生长抑素、多巴胺、抑酸护胃止血、拉氧头孢抗感染、补液支持、白蛋白,输血1u*2次。为进一步诊治于223-10-10收入消化留观病房。入观后予禁食、抑酸护胃、补铁、补液、白蛋白营养支持等对症治疗,2023-10-13饮水后无腹痛等不适,复查血红蛋白57g/l,予开放流质饮食,继续抑酸护胃、补铁,拔除导尿管和胃管,予康复新液10ml tid口服保护粘膜。2023-11-14患者于夜间23:00外出如厕下床时因双下肢无力摔倒,当时神志清醒,血压72/43mmhg,血氧饱和度98%,心率66bpm,嘱患者卧床,在卧床过程中呕血20ml,立刻予心电监护、扩容、多巴胺升压、止血、抑酸、申请输血,急查血常规、crp、出凝血功能,联系急诊内镜、普外科,告知患者家属患者病情危重,随时有死亡风险,家属表示知晓;23:30复测血压70/40mmhg,心率62bpm,予多巴胺畅滴,23:45患者再次呕大量鲜血伴血块,血压106/54mmhg,心率107bpm,继续心电监护、扩容、多巴胺畅滴;凌晨1:00患者血压109/54mmhg,1:20患者外出行手术治疗,当时血压80/41mmhg,转运途中患者再次呕大量鲜血,血压70/40左右。现患者为进一步诊治收入我科。目前患者禁食中,近期体重无明显变化。 【既往史】 疾病史:既往无消化道出血病史。  【系统回顾】呼吸系统:无咳嗽、咳痰、咯血、呼吸困难、发冷、发热、盗汗  循环系统:无心悸、心前区疼痛;无呼吸困难、水肿、尿少等  消化系统:详见现病史  泌尿生殖系统:无排尿困难;无尿频、尿急、尿痛;无水肿  血液系统:无乏力、头晕、耳鸣、皮肤苍白、出血点、瘀斑、血肿及肝脾、淋巴结肿大  内分泌系统:无畏寒、怕热、多汗、乏力、视力障碍、食欲异常、烦渴、多尿  神经精神系统:无头痛、记忆力减退、意识障碍、晕厥、痉挛、感觉及运动异常  运动骨骼系统:无肢体肌肉麻木、疼痛、痉挛、萎缩、瘫痪;无关节肿痛、骨折 【专科检查】 贫血貌,消瘦;全身皮肤无黄染,无肝掌、蜘蛛痣,全身浅表淋巴结无明显肿大,胸壁无畸形,双侧呼吸动度对称一致,双肺呼吸音清,未闻及明显干湿啰音。心脏听诊律齐,未闻及明显杂音和异常心音。 【辅助检查】腹部增强ct【我院、2023-10-11、zs23340512】胃壁水肿增厚,请结合内镜检查;左肾囊肿。\n入院记录中是否与「1.胃溃疡伴出血」诊断，请分别回答是否有关联依据？若有关联，则回答“有关联”与关联的依据；若无关联，则回答“无关联”与无关联的依据；请按诊断分别回答:"
    # message_list.append({"role": "user", "content": '假设你是呼吸科医生。下面是一份病历，请基于患者症状、病史等信息预测诊断依据、鉴别诊断。\n1、患者,女,3岁。\n2、主诉:咳嗽伴发热、喘息5天\n3、现病史:患儿5天前无明显诱因下出现咳嗽,为阵发性连声咳,每次6-8声,喉间有痰不易咳出,咳剧时脸色涨红,无犬吠样咳,咳毕无鸡鸣样回声,伴发热,体温最高39.7°C,无盗汗、消瘦;无呛咳、呼吸困难;无抽搐、腹泻;无烦躁不安、皮疹,于7.22大江东医院就诊,予阿奇霉素静滴(7.22-7.23),患儿仍高热,于7.24我院门诊就诊,期间予尤立新静滴(7.24-7.25)、甲泼尼龙琥珀酸钠静滴(7.25)后患儿热退,7.25查血常规基本正常,查胸部X线:支气管肺炎,心影饱满。现患儿体温正常1天,仍咳嗽剧烈伴喘息,为求进一步诊治,门诊拟“1.呼吸道合胞体病毒肺炎 2.房间隔缺损 3.漏斗胸 4.先天性喉软骨软化病 ”收住入院。 患儿病来神清,精神可,胃纳可,睡眠可,大小便无殊,体重无明显增减。\n4、专科情况:哭声未哭,面色红润,前囟已闭,腹壁皮下脂肪2cm眼球活动灵活,瞳孔大小:左3mm、右3mm,对光反应灵敏腹壁反射引出,提睾反射(男)女孩专科小结:T36.9°C,P98次/分,R26次/分,BP99/59mmHg。神志清,精神可,咽充血明显,双扁桃体未见肿大,未见脓点,呼吸平,未见三凹征,两肺呼吸音粗,可见喉鸣音及少许湿罗音,心律齐,心音中,未及明显杂音,腹软,肝脾未及,神经系统阴性。\n5、辅助检查:德清县医院07.21呼吸四项病毒阴性,MP-IGM阴性,血常规、CRP正常,07.23胸部CT:左上肺感染性病变、左上肺阻塞性肺气肿,07.24血常规WBC 3.1*109G/L,NE50.2%,CRP18.6mg/dl。\n6、初步诊断:1. 呼吸道合胞体病毒肺炎 2. 房间隔缺损 3. 漏斗胸 4. 先天性喉软骨软化病'})
    prompt = '''【背景】我们公司（惠每）现在要研发一款AI问诊工具：用户（患者）通过和AI（医生）聊天的方式来获取一些帮助，同时AI（医生）也能通过聊天的方式一步一步地获取用户（患者）的病情信息。现在设定你为AI（医生），一款由惠每公司研发的名叫“Dr.Mayson”的AI医生，你具有丰富的临床经验和广泛且深厚的医学专业知识，善于和患者沟通，注重问诊技巧。【问诊技巧】1、多使用开放性问题避免导向性提问,鼓励患者充分表达。边听边想,在获取信息的同时提出并验证诊断假设。2、注意患者的情绪变化,如焦虑、抑郁等，采取符合对象的问诊方法。3、对特殊人群如老人、儿童要有针对性的问诊技巧。【指令】请结合上述的背景、问诊技巧，及下面的注意事项，开始对一名{age}{sex}患者进行问诊（先自我介绍）。要求在通过多轮对话后，能够获取到患者以下病情信息：主诉、现病史、既往史、过敏史、个人史、家族史、体格检查、辅助检查、初步诊断。【注意事项】1. 因为这是一款AI问诊工具，所以姓名对于诊断疾病没有帮助，因此你应避免询问用户（患者）的姓名。年龄和性别在指令中有提到，因此你应该能够知道用户（患者）的年龄和性别信息，所以也要避免再次询问用户（患者）的年龄、性别。2. 医患聊天（问诊）是个持续的过程，因此请谨记，在接下来的多轮对话中，都要以这个Prompt为前提。3. 你给出的聊天内容要尽可能口语化避免用一些术语，这样有利于用户（患者）理解，同时也要富有感情，让用户（患者）感受到温暖（比如多用“您”字，如“您好”）。4. 对话中你要避免一次性询问过多问题，通过一步一步的提问。5. 同样的问题，或者同样的话，你只能说一次。'''
    # prompt = prompt_list[int(name.replace('Python',''))]
    message_list.append({"role": "user", "content": prompt})
    openai.api_type = "open_ai"
    # openai.api_base = "http://223.72.199.109:8001/v1"
    openai.api_base = "http://223.72.199.109:8000/v1"
    # openai.api_base = "http://172.16.7.10:8000/v1"
    openai.api_key = "none"
    start = time.time()
    i = 0
    for chunk in openai.ChatCompletion.create(model='/home/<USER>/Qwen-14B-Chat', messages=message_list,top_p=0.1, stream=True,stop= ["<|endoftext|>","<|im_end|>","<|im_start|>"]):
        if hasattr(chunk.choices[0].delta, "content"):
            if i == 0:
                end = time.time()
                print('请求{},First token time:{}s'.format(name,str(end-start)[:4]))
            i+=1
            # print(chunk.choices[0].delta.content, end="", flush=True)

def tst_self_stream(name):
    reqUrl = 'http://172.16.7.10:8800/stream_chat'
    reqBody={"prompt":"【背景】我们公司（惠每）现在要研发一款AI问诊工具：用户（患者）通过和AI（医生）聊天的方式来获取一些帮助，同时AI（医生）也能通过聊天的方式一步一步地获取用户（患者）的病情信息。现在设定你为AI（医生），一款由惠每公司研发的名叫“Dr.Mayson”的AI医生，你具有丰富的临床经验和广泛且深厚的医学专业知识，善于和患者沟通，注重问诊技巧。【问诊技巧】1、多使用开放性问题避免导向性提问,鼓励患者充分表达。边听边想,在获取信息的同时提出并验证诊断假设。2、注意患者的情绪变化,如焦虑、抑郁等，采取符合对象的问诊方法。3、对特殊人群如老人、儿童要有针对性的问诊技巧。【指令】请结合上述的背景、问诊技巧，及下面的注意事项，开始对一名9岁男性患者进行问诊（先自我介绍）。要求在通过多轮对话后，能够获取到患者以下病情信息：主诉、现病史、既往史、过敏史、个人史、家族史、辅助检查、初步诊断。【注意事项】1. 因为这是一款AI问诊工具，所以姓名对于诊断疾病没有帮助，因此你应避免询问用户（患者）的姓名。年龄和性别在指令中有提到，因此你应该能够知道用户（患者）的年龄和性别信息，所以也要避免再次询问用户（患者）的年龄、性别。2. 医患聊天（问诊）是个持续的过程，因此请谨记，在接下来的多轮对话中，都要以这个Prompt为前提。3. 你给出的聊天内容要尽可能口语化避免用一些术语，这样有利于用户（患者）理解，同时也要富有感情，让用户（患者）感受到温暖（比如多用“您”字，如“您好”）。4. 对话中你要避免一次性询问过多问题，通过一步一步的提问。5. 同样的问题，或者同样的话，你只能说一次。","dialogue_code":"LR66JA4CFN33","customer_id":1001
        ,"chat_model":"qwen_api"}
    r = requests.post(reqUrl, stream=True, json=reqBody)
    if r.encoding is None:
        r.encoding = 'utf-8'

    # 遍历这个流式的json的请求数据
    start = time.time()
    i = 0
    for lines in r.iter_lines(decode_unicode=True):
        if lines:  # 每一个循环都是一个json数据
            if i == 0:
                end = time.time()
                print('请求{},First token time:{}s'.format(name, str(end - start)[:4]))
            i += 1

from multiprocessing import  Process
if __name__ == '__main__':
    process_list = []
    for i in range(30):  # 开启5个子进程执行fun1函数
        # p = Process(target=get_zljg_from_api, args=('Python'+str(i),))  # 实例化进程对象
        p = Process(target=tst_self_stream, args=('Python'+str(i),))  # 实例化进程对象
        # p = Process(target=get_zljg_from_api, args=('Python'+str(i),))  # 实例化进程对象
        p.start()
        # time.sleep(1)
        process_list.append(p)

    for i in process_list:
        p.join()

    print('结束测试')