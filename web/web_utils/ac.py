# coding:utf-8
# -*- coding: utf-8 -*-
"""
AC自动机
"""
import os

__all__ = ['Ahocorasick', ]

class Node(object):
    def __init__(self):
        self.next = {}
        self.fail = None  # 失败指针
        self.isWord = False

class Ahocorasick(object):
    def __init__(self):
        self.__root = Node()

    def addWord(self, word):
        tmp = self.__root
        for char in word:
            tmp = tmp.next.setdefault(char, Node())
        tmp.isWord = True

    def make(self):
        """
        构建失败路径
        """
        tmpQueue = list()
        tmpQueue.append(self.__root)
        while len(tmpQueue) > 0:
            temp = tmpQueue.pop()
            p = None
            for k, v in temp.next.items():
                if temp == self.__root:
                    temp.next[k].fail = self.__root
                else:
                    p = temp.fail
                    while p is not None:
                        if k in p.next:
                            temp.next[k].fail = p.next[k]
                            break
                        p = p.fail
                    if p is None:
                        temp.next[k].fail = self.__root
                tmpQueue.append(temp.next[k])

    def search(self, content):
        """
        返回列表，每个元素为匹配的模式串在句中的起止位置
        """
        result = []
        startWordIndex = 0
        for currentPosition in range(len(content)):
            word = content[currentPosition]
            endWordIndex = currentPosition
            p = self.__root
            while word in p.next:
                if p == self.__root:
                    startWordIndex = currentPosition
                p = p.next[word]
                if p.isWord:
                    result.append((startWordIndex, endWordIndex))
                if p.next and endWordIndex + 1 < len(content):
                    endWordIndex += 1
                    word = content[endWordIndex]
                else:
                    break
                while (word not in p.next) and (p != self.__root):
                    p = p.fail
                    startWordIndex += 1
                if p == self.__root:
                    break
        return result

    def replace(self, content):
        """
        匹配到的字符串以'*'号表示
        """
        replacepos = self.search(content)
        result = content
        for posindex in replacepos:
            result = result[0:posindex[0]] + (posindex[1] - posindex[0] + 1) * '*' + content[posindex[1] + 1:]
        return result

def test(a):
    print('********', a)
    ah = Ahocorasick()
    ah.addWord(u'测试')
    ah.addWord(u"我是")
    ah.addWord(u'长骨骨膜增厚')
    ah.make()
    print(ah.search(u'测试123我是好人'))
    print(ah.search(u'长骨骨膜增厚'))

def ac_max_search(ac, sentence, word_map):
    word_2_pos = []
    flag = [0] * len(sentence)
    for x in sorted(ac.search(sentence), key=lambda x: x[1] - x[0], reverse=True):
        r_word = sentence[x[0]:x[1] + 1]
        if r_word not in word_map:
            continue
        if sum(flag[x[0]:x[1] + 1]) > 0: continue
        for i in range(x[0], x[1] + 1): flag[i] = 1
        word_2_pos.append([r_word, x[0]])
    return word_2_pos

if __name__ == '__main__':
    test('a')