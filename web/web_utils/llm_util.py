from transformers import AutoModelForCausalLM, AutoTokenizer
from peft import PeftModel
import torch

class ModelMerger:
    def __init__(self, base_path, lora_path):
        self.base_path = base_path
        self.lora_path = lora_path
        self.base_model = None
        self.peft_model = None
        self.merged_model = None

    def load_base_model(self):
        self.base_model = AutoModelForCausalLM.from_pretrained(self.base_path)

    def load_peft_model(self):
        if self.base_model is None:
            raise ValueError("Base model must be loaded first.")
        self.peft_model = PeftModel.from_pretrained(self.base_model, self.lora_path)

    def merge_and_unload(self):
        if self.peft_model is None:
            raise ValueError("PEFT model must be loaded first.")
        self.merged_model = self.peft_model.merge_and_unload()

    def save_merged_model(self, save_path):
        if self.merged_model is None:
            raise ValueError("Merged model must be created first.")
        # self.merged_model.to(torch.float16)  # Convert to float16
        # self.merged_model.save_pretrained(save_path, torch_dtype=torch.float16)
        self.merged_model.save_pretrained(save_path)

    def save_merged_model_fp16(self, save_path):
        if self.merged_model is None:
            raise ValueError("Merged model must be created first.")
        self.merged_model.to(torch.float16)  # Convert to float16
        self.merged_model.save_pretrained(save_path, torch_dtype=torch.float16)
if __name__ == '__main__':
    base_path = ""
    lora_path = ""
    save_path = "{}_merge_lora_llm".format(base_path.split("/")[-1])

    merger = ModelMerger(base_path, lora_path)
    merger.load_base_model()
    merger.load_peft_model()
    merger.merge_and_unload()
    # merger.save_merged_model(save_path)
    # merger.save_merged_model_fp16(save_path)
