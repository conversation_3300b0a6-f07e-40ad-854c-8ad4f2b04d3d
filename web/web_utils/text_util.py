# coding:utf-8
import unicodedata
import json
import re
from web.web_utils.api_util import post_url
import uuid

chuyuan_kws = ['【门诊诊断】','【入院诊断】','【出院诊断】','【病理诊断】','【入院情况[（\(]*主要症状及体征[）\)]*】','【入院情况】','【主诉】','【入院时主要症状及体征】','【现病史】','【主要化验结果】','【入院第一次检查】','【出院前检查】','【系统回顾】','【既往史】','【婚育史】','【月经史】','【个人史】','【家族史】','【体格检查】','【特殊检查 主要会诊】','【手术情况】','【辅助检查】','【诊疗经过[（\(]*手术名称、日期输血量 详细病理 伤口愈合及其他[）\)]*】','【诊疗经过】','【出院情况 出院医嘱（用药及建议）】','【出院时情况 出院后用药及建议】','【出院情况】','【出院医嘱】','【出院前检查】','【出院带药】','【出院建议】','【出院宣教】','【门诊信息】','【普通门诊/专病门诊地点及时间】','【病房地点】','【微信公众号】','【病史复印】','【影像胶片打印】','【治疗结果】','【出院日期】','【入院日期】','记录者','初步诊断','诊断依据','诊断医师','诊断与诊断依据[：\:]诊断','文档标题','手术日期','手术开始时间','手术截止时间','麻醉开始时间','麻醉结束时间','术前诊断','术后诊断','拟施手术名称','手术名称','手术医生姓名','手术医师代码','手术主刀医师','麻醉方式','麻醉医生姓名','麻醉医师代码','手术经过','术中出现的情况及处理','术中出血','术中输血','医师签名','录入时间','医师']
chuyuan_kws = [it.strip()for it in chuyuan_kws]

cyjl_split = f"({'|'.join(map(lambda x:x.replace('【', '【?').replace('】', '】?')+'[:：]*', chuyuan_kws))})"
cyjl_split_reg = re.compile(cyjl_split)


# 兼容markdown换行
def markdown_ft(text):
    return text.strip().replace("\r\n", "").replace("\n", "").replace("。。", '。').replace('​','').replace(' ','')


def normalize_text(the_str):
    try:
        if not the_str:
            return ""
        if isinstance(the_str, str):
            the_str = the_str.strip()
        return unicodedata.normalize("NFKC", the_str).lower()
    except:
        return the_str


def normalize_name(the_str):
    the_str = normalize_text(the_str).replace('(','').replace(')','').replace('[','').replace(']','').replace('{','').replace('}','').replace('.','').replace(':','').replace(' ','').replace('自备','')
    return the_str


def xml2text(origin_text, progress_type, msg_type, config_map, cid):
    target_text = ''
    sps = progress_parse(origin_text, progress_type, msg_type, config_map, cid)
    for i in range(0, len(sps), 2):
        attribute_value = markdown_ft(sps[i + 1])
        target_text += '{}：{}\n'.format(sps[i],attribute_value)
    return target_text


# 文书解析
def progress_parse(data,progress_tye,msg_type,config_map,customer_id=1001,record_id=1,visit_type=1):
    rlt = []
    if msg_type ==2:
        data = json.loads(data)
        for it in data:
            rlt.append(it["key"])
            rlt.append(it["value"])
    else:
        text_list = text_parse(customer_id,record_id,data,progress_tye,msg_type,config_map,visit_type)
        for it in text_list:
            attr_value = it["attributeValue"].replace(it["attributeKey"], '')
            rlt.append(it["attributeName"])
            rlt.append(attr_value)
    # else:
    #     # TODO xml解析
    #     rlt = cyjl_split_reg.split(data)[1:]

    return rlt

def text_parse(customer_id,record_id,progress_text,progress_tye,msg_type,config_map,visit_type=1):
    progress_text = progress_text.replace('​','').replace(' ','')
    json_obj = {
        "recordId": int(record_id),
        "customerId": int(customer_id),
        "progressType": int(progress_tye),
        "msgType": int(msg_type),
        "progressText":progress_text
    }
    # pangoo_url = 'http://{}:{}/pangoo/progress/progressParserAcc'.format(config_map['app']['pangoo_ip'],config_map['app']['pangoo_port'])
    pangoo_url = 'http://{}/go_pangoo/pangoo/progress/progressParserAcc'.format(config_map['app']['pangoo_ip'])
    return_json = post_url(pangoo_url, json_obj)
    text_list = return_json.get('body', [])
    return text_list

def compare_time(time_str1, time_str2):
    time_str1 = time_str1.replace('年','-').replace('月','-').replace('日','').replace('.','-')
    time_str2 = time_str2.replace('年','-').replace('月','-').replace('日','').replace('.','-')
    return time_str1<time_str2

def get_uuid():
    uuid_str = str(uuid.uuid1()).replace('-','')
    return uuid_str


def hw_messages(messages=[]) -> list:
    concat_msg = ""
    for message in messages:
        concat_msg = concat_msg + f"<|im_start|>{message['role']}\n{message['content']}<|im_end|>\n"
    concat_msg = concat_msg + "<|im_start|>assistant\n"

    return [{"content": concat_msg}]


def valid_login(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    user_name = request_data.get('user_name', '').strip()
    password = request_data.get('password', '').strip()
    customer_id = request_data.get('customer_id', '1001').strip()
    flag = False
    msg = ''
    if user_name=='hmgpt' and password=='huimei@2023':
        flag = True
    else:
        msg = '用户名或密码错误！请重新输入'
    return flag, msg, '',user_name, int(customer_id)


def merge_and_deduplicate(target_dict, source_dict):
    for key, value in source_dict.items():
        if key in target_dict:
            continue
        else:
            target_dict[key] = value
    return target_dict
