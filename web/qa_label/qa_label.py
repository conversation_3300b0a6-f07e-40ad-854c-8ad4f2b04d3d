# coding:utf-8
import sys
import re
from web.web_utils.text_util import progress_parse, markdown_ft, normalize_text
from web.web_utils.db_util import *
import json


def get_qa_label_data(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    qa_question = request_data.get('qa_question', '').strip()
    qa_answer = request_data.get('qa_answer', '').strip()
    review_status = request_data.get('review_status', '').strip()
    qa_type = request_data.get('qa_type', '').strip()
    page = request_data.get('page', 1)
    page_size = request_data.get('page_size', 50)
    count_flag = request_data.get('count_flag', 0)
    data_sql = '''
                select id, qa_type,origin_question, real_question, real_answer,origin_answer,review_status,date_format(modify_date,'%Y-%m-%d %H:%i:%s') modify_date from qa_label 
            '''
    count_sql = '''
                        select count(1) all_count from qa_label 

                    '''
    sql = ''' 
where qa_type='{}' '''.format(qa_type)
    if review_status:
        sql += " and review_status ={}".format(review_status)
    if qa_question:
        sql += " and real_question regexp '{}'".format(qa_question)
    if qa_answer:
        sql += " and real_answer regexp '{}'".format(qa_answer)
    data_sql += sql + " limit {},{}".format((page - 1) * page_size, page_size)

    xs = select_by_sql(data_sql, 'hm_llm', config_map)
    for x in xs:
        x['real_answer'] = x['real_answer'] if len(x['real_answer']) < 200 else x['real_answer'][:200] + '...'
        x['review_status_str'] = {0:'未评测',1:'已通过',2:'未通过'}.get(x['review_status'], '')
    all_count = -1
    if count_flag:
        count_sql += sql
        xs2 = select_by_sql(count_sql, 'hm_llm', config_map)
        all_count = 0
        for x2 in xs2:
            all_count = x2['all_count']
    return 1, xs, all_count


def get_qa_label_detail(request_data, config_map):
    id = request_data.get('id')
    if not id:
        return {}
    data_sql = '''
                select id, qa_type,origin_question, real_question, real_answer,origin_answer,review_status,date_format(modify_date,'%Y-%m-%d %H:%i:%s') modify_date from qa_label where id = {}
            '''.format(id)
    xs = select_by_sql(data_sql, 'hm_llm', config_map)
    return xs[0]


def insert_qa_label(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    qa_question = request_data.get('qa_question', '').strip()
    qa_answer = request_data.get('qa_answer', '').strip()
    qa_type = request_data.get('qa_type', '').strip()
    xs = [[qa_type, qa_question, qa_question, qa_answer, qa_answer]]
    sql = '''
            INSERT INTO qa_label (qa_type,origin_question, real_question, real_answer,origin_answer) VALUES (%s, %s, %s, %s, %s);
    		 '''
    ret = execute_sql_many(sql,xs, 'hm_llm', config_map)
    return ret


def update_qa_label(request_data, config_map):
    request_data = json.loads(request_data.decode('utf-8'))
    qa_question = request_data.get('qa_question', '').strip()
    qa_answer = request_data.get('qa_answer', '').strip()
    review_status = request_data.get('review_status')
    id = request_data.get('id')
    sql = '''
            UPDATE qa_label SET 
    		 '''
    if qa_question:
        sql += " real_question = '{}',".format(qa_question)
    if qa_answer:
        sql += " real_answer = '{}',".format(qa_answer)
    if review_status:
        sql += " review_status = {}".format(review_status)
    else:
        sql += " review_status = 1"
    sql += " WHERE id = {}".format(id)
    ret = execute_sql(sql, 'hm_llm', config_map)
    return ret

