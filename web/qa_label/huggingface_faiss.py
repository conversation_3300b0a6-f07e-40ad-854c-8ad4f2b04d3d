# coding:utf-8
import faiss
from sentence_transformers import SentenceTransformer
from web.web_utils.db_util import *
from web.web_utils.text_util import normalize_text
import json

model = SentenceTransformer('/home/<USER>/huggingface_model/sbert-base-chinese-nli')
global index,ids

def update_faiss_index(config_map):
    global index,ids
    data_sql = '''
                    select id,origin_question, real_question, real_answer,origin_answer,review_status,date_format(modify_date,'%Y-%m-%d %H:%i:%s') modify_date from qa_label 
                '''
    xs = select_by_sql(data_sql, 'hm_llm', config_map)
    sentences = []
    ids = []
    for x in xs:
        sentences.append(normalize_text(x['real_question']))
        ids.append(x['id'])
    sentence_embeddings = model.encode(sentences)
    dimension = sentence_embeddings.shape[1]
    index = faiss.IndexFlatL2(dimension)
    index.add(sentence_embeddings)
    return 1


def search_qa(request_data, config_map, top_k=10):
    global index,ids
    if not index:
        return -1, [], 0
    request_data = json.loads(request_data.decode('utf-8'))
    qa_question = request_data.get('qa_question', '').strip()
    search = model.encode([normalize_text(qa_question)])
    D, I = index.search(search, top_k)
    search_id_list = []
    for i in I[0]:
        if i<0:
            continue
        search_id_list.append(ids[i])
    data_sql = '''
                    select id, qa_type,origin_question, real_question, real_answer,origin_answer,review_status,date_format(modify_date,'%Y-%m-%d %H:%i:%s') modify_date from qa_label where id in ({}) 
                '''.format(','.join([str(i) for i in search_id_list]))
    xs = select_by_sql(data_sql, 'hm_llm', config_map)
    if not xs:
        return 1, xs, 0
    new_xs = [xs[0]]*len(xs)
    for x in xs:
        x['real_answer'] = x['real_answer'] if len(x['real_answer'])<200 else x['real_answer'][:200]+'...'
        x['review_status_str'] = {0:'未评测',1:'已通过',2:'未通过'}.get(x['review_status'], '')
        new_xs[search_id_list.index(x['id'])] = x
    return 1, new_xs, len(new_xs)