set -x
#SERVICE_DIR="/home/<USER>/work/aigc_hospital"

#git --git-dir="$SERVICE_DIR/.git" --work-tree="$SERVICE_DIR" reset --hard origin/master
#git --git-dir="$SERVICE_DIR/.git" --work-tree="$SERVICE_DIR" clean -f
#git --git-dir="$SERVICE_DIR/.git" --work-tree="$SERVICE_DIR" pull origin

#cd $SERVICE_DIR/web
#kill -9 $(ps aux | awk '/[a]igc_api_service/ {print $2}')
kill -9 $(lsof -i:8800 -t)
nohup python -u aigc_api_service.py  >&8800.log &
echo $(pwd)
