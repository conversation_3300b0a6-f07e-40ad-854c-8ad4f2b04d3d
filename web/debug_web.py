# coding:utf-8
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.getcwd())
import configparser
import pdb
from web.web_utils.sql_util import sqls, sql_query
from web.web_utils.text_util import normalize_text
from web.web_utils.db_util import *
import re
import pandas as pd
from web.regression_test.regressions import regression_disease
from web.regression_test.count_token import count_token,count_token_gen_excel
from web.label_task_service.diff_llm import diff_llm_result,update_llm_result


proDir = os.path.split(os.path.realpath(__file__))[0]
# 在当前文件路径下查找.ini文件
configPath = os.path.join(proDir, 'config.ini')
# configPath = os.path.join(proDir, 'config_company.ini')
con = configparser.RawConfigParser()
con.read(configPath, encoding='utf-8')
sections = con.sections()
config_map = {}
for sec in sections:
    items = dict(con.items(sec))
    config_map[sec] = items

reg_pattern_zzjh = re.compile(r'{}'.format("([诊治疗]{2}计划.*)(?:签名)?"))
reg_pattern_zls = re.compile(r'{}'.format("[^\u4e00-\u9fa5](治疗上.*)(?:医.签名)?"))
reg_pattern_zl = re.compile(r'{}'.format("[^\u4e00-\u9fa5](治疗[:：，,计划].*)(?:医.签名)?"))

def get_zlgc_list():
    test_sql = '''
                select pp.record_id,pp.progress_type, pp.progress_message, pp.record_time_format from mt_patient_progress pp
inner join mt_patient_record rr on pp.record_id=rr.id 
where rr.inpatient_department regexp '呼吸' and pp.progress_type in (2,3,4,12,24) 
                '''
    record_list = sql_query(1001, test_sql, config_map)
    record_zljg_map = {}
    zljg_exist_map = {}
    for i, v in enumerate(record_list):
        print('{}/{}'.format(i, len(record_list)))
        progress_text = normalize_text(str(v['progress_message']))
        progress_type = v['progress_type']
        record_id = v['record_id']
        record_time_format = str(v['record_time_format'])[:10]
        if record_id not in record_zljg_map:
            record_zljg_map[record_id] = []
            zljg_exist_map[record_id] = {}
        if record_time_format in zljg_exist_map[record_id]:
            continue
        zljg_exist_map[record_id][record_time_format] = 1
        for reg_pattern in [reg_pattern_zzjh,reg_pattern_zls,reg_pattern_zl]:
            llm_output_list = reg_pattern.findall(progress_text)
            if len(llm_output_list) > 0:
                record_zljg_map[record_id].append('{}{}'.format(str(record_time_format)[:10], llm_output_list[0]))
                break
    result = []
    for record_id in record_zljg_map:
        result.append([record_id,'\n'.join(record_zljg_map[record_id])])
    data_node = pd.DataFrame(result, columns=['record_id', 'cfjl-zlgc'])
    data_node.to_csv("record2cfjl-zlgc.csv", index=False)
    return ''

def insert_yunying():
    xs = pd.read_excel('/Users/<USER>/Documents/单病种问题_微调22.xlsx', keep_default_na=False, sheet_name=0)
    insert_list = []
    for index, line in xs.iterrows():
        old_question = str(line['问题']).strip()
        question = str(line['问题微调']).strip()
        old_answer = str(line['回复']).strip()
        answer = str(line['回复微调']).strip()
        insert_list.append(['单病种知识库', old_question, question,old_answer,answer])
    sql = '''
                INSERT INTO qa_label (qa_type,origin_question, real_question, real_answer,origin_answer) VALUES (%s, %s, %s, %s, %s);
                 '''
    ret = execute_sql_many(sql, insert_list, 'hm_llm', config_map)
    print(ret)


if __name__ == '__main__':
    # pdb.set_trace()
    # customer_id = 1687
    # record_id = 108706
    # generate_text = assemble_discharge_summary(customer_id, record_id, config_map)
    # zljg_list = get_zlgc_list()
    # insert_yunying()
    # error_str = regression_disease(config_map)
    # print(error_str)
    # count_token(config_map)
    # count_token_gen_excel(config_map)
    # rid_list = [18621950,19597549,19950263,19961383,19971054,19988670,19999316,20025511,20040365,20059109]
    # rid_list = [18621950,19597549,19950263,19961383,19971054,19988670,19999316,20025511,20040365]
    # for prompt_type in ['入院诊断','出院诊断','入院情况','诊疗经过','出院情况','出院医嘱']:
    #     diff_llm_result(1480,rid_list,prompt_type, config_map)
    # for model_name in ['glm_9b']:
    #     update_llm_result(model_name, config_map)
    # print('ok')
    # from web.mrg_service.cdss_llm import get_record_tree,get_llm_prompt_list
    # import json
    # # aa = get_record_tree(1480, 18621950, config_map)
    # aa = get_llm_prompt_list('llm_cdss', config_map)
    # print(json.dumps(aa, indent=4, ensure_ascii=False))
    content = '发热，又称发烧，在医学上是指体温升高超出正常范围的现象，通常是对诸如感染、损伤或免疫挑战等体内或外在刺激的一种生理反应。例如，白细胞介素-1β (IL-1β) 是一种由巨噬细胞和单核细胞产生的促炎因子，它在机体的全身和局部反应中扮演关键角色，尤其是在应对上述刺激时，是造成急性和慢性炎症的主要原因之一，并且是发热反应的关键介质<ref>[5]</ref>。IL-1β 的成熟和释放需要 caspase-1 的活化，这一过程涉及到胞内受体对病原体或内源性危险信号的识别，随后通过一系列复杂的免疫反应途径最终导致炎症和发热的发生<ref>[4]</ref>。因此，发热可以视为机体免疫系统激活的一部分，旨在对抗潜在的有害因素<ref>[3][4]</ref>。'
    doc_index_map = {}
    rstart = content.rfind('<ref')
    if rstart > -1:
        if content[rstart:].find('</ref>') == -1:
            content = content[:rstart]
    match_ref_list = re.findall(r'<ref>.*?</ref>', content)
    _match_ref_map = {}
    for _match_ref in match_ref_list:
        if _match_ref in _match_ref_map:
            continue
        _match_ref_map[_match_ref] = 1
        matches = re.findall(r'\[(\d+)\]', _match_ref)
        _match_ref2 = _match_ref
        real_idx_int_list = []
        for _match in matches:
            if _match not in doc_index_map:
                real_idx = str(len(doc_index_map) + 1)
                doc_index_map[_match] = real_idx
                _match_ref2 = _match_ref2.replace('[{}]'.format(_match), '[{}]'.format(real_idx))
                real_idx_int_list.append(int(real_idx))
            else:
                _match_ref2 = _match_ref2.replace('[{}]'.format(_match), '[{}]'.format(doc_index_map[_match]))
                real_idx_int_list.append(int(doc_index_map[_match]))
        _match_ref2 = _match_ref2.replace('<ref>', '').replace('</ref>', '')
        if len(real_idx_int_list) > 1:
            _match_ref2 = ''.join(['[{}]'.format(k) for k in sorted(real_idx_int_list)])
        content = content.replace(_match_ref, _match_ref2)
    print(content)
